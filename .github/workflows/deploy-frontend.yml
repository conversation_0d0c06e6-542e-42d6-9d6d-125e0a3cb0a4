name: Deploy Frontend to AWS

on:
  push:
    branches: [dev, main]
    paths: 
      - 'src/**'
      - 'public/**'
      - 'package.json'
      - 'yarn.lock'
      - 'vite.config.ts'
      - '.github/workflows/deploy-frontend.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
        - dev
        - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'main' && 'main' || 'dev' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set environment variables
      run: |
        echo "ENVIRONMENT=${{ vars.ENVIRONMENT }}" >> $GITHUB_ENV
        echo "AWS_DEFAULT_REGION=${{ vars.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
        echo "AWS_ROOT_REGION=${{ vars.AWS_ROOT_REGION }}" >> $GITHUB_ENV
        echo "AWS_GLOBAL_REGION=${{ vars.AWS_GLOBAL_REGION }}" >> $GITHUB_ENV
        echo "APP_NAME=${{ vars.APP_NAME }}" >> $GITHUB_ENV
        echo "NODE_VERSION=${{ vars.NODE_VERSION || '20' }}" >> $GITHUB_ENV

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'yarn'
        cache-dependency-path: 'yarn.lock'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Build application
      run: |
        echo "Building for environment: $ENVIRONMENT"
        yarn build
      env:
        VITE_APP_ENVIRONMENT: ${{ env.ENVIRONMENT }}
        VITE_APP_AWS_REGION: ${{ env.AWS_DEFAULT_REGION }}
        VITE_APP_VERSION: ${{ github.sha }}

    - name: Configure AWS credentials for app account (S3)
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}

    - name: Deploy to S3
      run: |
        echo "Deploying to S3 bucket: ${{ vars.S3_BUCKET_NAME }}"
        aws s3 sync dist/ s3://${{ vars.S3_BUCKET_NAME }}/ --delete --exact-timestamps
        echo "✅ Frontend deployed to S3 successfully"

    - name: Configure AWS credentials for root account (CloudFront)
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.ROOT_AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.ROOT_AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Invalidate CloudFront cache
      run: |
        echo "Invalidating CloudFront distribution: ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}"
        INVALIDATION_ID=$(aws cloudfront create-invalidation \
          --distribution-id ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }} \
          --paths "/*" \
          --query 'Invalidation.Id' \
          --output text \
          --region us-east-1)
        
        echo "✅ CloudFront invalidation created: $INVALIDATION_ID"
        echo "🔄 Cache invalidation may take a few minutes to complete"

    - name: Deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Environment**: ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
        echo "- **S3 Bucket**: ${{ vars.S3_BUCKET_NAME }}" >> $GITHUB_STEP_SUMMARY
        echo "- **CloudFront Distribution**: ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}" >> $GITHUB_STEP_SUMMARY
        echo "- **AWS Region**: ${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Status**: ✅ Successfully deployed" >> $GITHUB_STEP_SUMMARY 