/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Oxygen',
          'Ubuntu',
          'Cantarell',
          'Fira Sans',
          'Droid Sans',
          'Helvetica Neue',
          'sans-serif',
        ],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // WhatsApp-inspired Chat Colors (Enhanced for demo)
        chat: {
          // Primary colors
          primary: "#075E54",
          "primary-light": "#128C7E",
          "primary-dark": "#054740",

          // Message bubbles
          "outbound": "#DCF8C6", // WhatsApp signature green for outbound messages
          "outbound-dark": "#056162", // Dark mode outbound
          "outbound-text": "#000000",
          "outbound-text-dark": "#FFFFFF",
          "outbound-meta": "rgba(0, 0, 0, 0.45)",
          "outbound-meta-dark": "rgba(255, 255, 255, 0.6)",

          "inbound": "#FFFFFF",
          "inbound-dark": "#202C33", // Dark mode inbound
          "inbound-text": "#000000",
          "inbound-text-dark": "#E9EDEF",
          "inbound-meta": "rgba(0, 0, 0, 0.45)",
          "inbound-meta-dark": "rgba(233, 237, 239, 0.6)",

          // Backgrounds
          "bg": "#EFEAE2", // WhatsApp chat background pattern
          "bg-dark": "#0B141A", // Dark mode chat background
          "sidebar": "#FFFFFF",
          "sidebar-dark": "#202C33",
          "header": "#F0F2F5",
          "header-dark": "#2A3942",

          // Input and composer
          "input-bg": "#FFFFFF",
          "input-bg-dark": "#2A3942",
          "input-border": "#E5E7EB",
          "input-border-dark": "#3B4A54",

          // Status colors
          "online": "#4FC3F7",
          "typing": "#075E54",
          "error": "#FF0000",
          "success": "#075E54",
          "delivered": "#53BDEB", // WhatsApp blue for delivered status
          "read": "#53BDEB", // Blue checkmarks for read

          // Text colors
          "text-primary": "#111B21",
          "text-primary-dark": "#E9EDEF",
          "text-secondary": "#667781",
          "text-secondary-dark": "#8696A0",
          "text-muted": "#8696A0",
          "text-muted-dark": "#667781",
          "text-on-primary": "#FFFFFF",

          // Gray scale
          "gray-50": "#F8F9FA",
          "gray-100": "#F1F3F4",
          "gray-200": "#E4E6EA",
          "gray-300": "#CDD6DD",
          "gray-400": "#8696A0",
          "gray-500": "#667781",
          "gray-600": "#54656F",
          "gray-700": "#3B4A54",
          "gray-800": "#202C33",
          "gray-900": "#111B21",

          // Hover states
          "hover": "rgba(0, 0, 0, 0.05)",
          "hover-dark": "rgba(255, 255, 255, 0.05)",

          // Borders
          "border": "#E4E6EA",
          "border-dark": "#3B4A54",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in": {
          from: { opacity: "0" },
          to: { opacity: "1" },
        },
        "fade-out": {
          from: { opacity: "1" },
          to: { opacity: "0" },
        },
        "slide-in-from-top": {
          from: { transform: "translateY(-100%)" },
          to: { transform: "translateY(0)" },
        },
        "slide-in-from-bottom": {
          from: { transform: "translateY(100%)" },
          to: { transform: "translateY(0)" },
        },
        "slide-in-from-left": {
          from: { transform: "translateX(-100%)" },
          to: { transform: "translateX(0)" },
        },
        "slide-in-from-right": {
          from: { transform: "translateX(100%)" },
          to: { transform: "translateX(0)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.2s ease-out",
        "fade-out": "fade-out 0.2s ease-out",
        "slide-in-from-top": "slide-in-from-top 0.2s ease-out",
        "slide-in-from-bottom": "slide-in-from-bottom 0.2s ease-out",
        "slide-in-from-left": "slide-in-from-left 0.2s ease-out",
        "slide-in-from-right": "slide-in-from-right 0.2s ease-out",
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
} 