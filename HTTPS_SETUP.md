# HTTPS Development Setup for WhatsApp Integration

## Why HTTPS is Required

WhatsApp Business API integration requires HTTPS for Facebook SDK to work properly. Meta's embedded signup will not function on HTTP connections.

## Quick Start

1. **Start the HTTPS development server:**
   ```bash
   yarn dev
   ```

2. **Access your app at:**
   ```
   https://localhost:5173
   ```

3. **Accept the self-signed certificate warning** in your browser (this is normal for local development)

## SSL Certificate Setup

This project includes pre-configured SSL certificates for localhost development. The certificates are automatically generated and configured in `vite.config.ts`.

### Certificate Files (Auto-generated)
- `localhost.key` - Private key file
- `localhost.crt` - Certificate file

These files are added to `.gitignore` for security.

## Browser Certificate Warning

When you first visit `https://localhost:5173`, your browser will show a security warning because we use self-signed certificates. This is completely normal for local development.

### How to Accept the Certificate:

**Chrome/Safari:**
- Click "Advanced"
- Click "Proceed to localhost (unsafe)"

**Firefox:**
- Click "Advanced"
- Click "Accept the Risk and Continue"

## Regenerating Certificates

If you need to regenerate the SSL certificates:

```bash
cd notefy-app
openssl req -x509 -out localhost.crt -keyout localhost.key \
  -newkey rsa:2048 -nodes -sha256 -subj '/CN=localhost' \
  -extensions EXT -config <(printf "[dn]\nCN=localhost\n[req]\ndistinguished_name = dn\n[EXT]\nsubjectAltName=DNS:localhost\nkeyUsage=digitalSignature\nextendedKeyUsage=serverAuth")
```

## Using mkcert (Alternative Method)

For a more trusted certificate setup, you can use mkcert:

1. **Install mkcert:**
   ```bash
   # Fix Homebrew permissions first (if needed)
   sudo chown -R $(whoami) /usr/local/Homebrew
   
   # Then install mkcert
   brew install mkcert
   mkcert -install
   ```

2. **Generate certificate for localhost:**
   ```bash
   mkcert localhost 127.0.0.1 ::1
   ```

3. **Update vite.config.ts to use the generated files:**
   ```typescript
   https: {
     key: fs.readFileSync('./localhost-key.pem'),
     cert: fs.readFileSync('./localhost.pem'),
   }
   ```

## Troubleshooting

### TypeScript Error: "No overload matches this call"
This was fixed by using proper SSL certificate files instead of `https: true`.

### Facebook SDK Error
If you see "FB.login can no longer be called from http pages", ensure you're accessing:
- ✅ `https://localhost:5173` 
- ❌ Not `http://localhost:5173`

### Certificate Issues
- Clear browser cache and cookies for localhost
- Try incognito/private browsing mode
- Restart the development server with `yarn dev`

### Environment Variables
Make sure you have the Facebook App ID set:
```bash
# In your .env file
REACT_APP_FACEBOOK_APP_ID=349869447413699
```

## Production Deployment

For production, you'll use a real SSL certificate from your hosting provider (CloudFront, etc.). This HTTPS setup is only for local development. 