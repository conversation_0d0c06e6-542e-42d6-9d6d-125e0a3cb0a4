# Notefy Portal

A modern, enterprise-grade React portal for the Notefy note-taking system built with TypeScript, Vite, Tailwind CSS, and Shadcn/ui components.

## Features

- **Modern Tech Stack**: Built with React 19, TypeScript, Vite, and Tailwind CSS
- **Enterprise UI**: Beautiful, accessible components using Shadcn/ui and Radix UI
- **Mobile-First Design**: Responsive design following Apple and Stripe design principles
- **AWS Integration**: Ready for AWS Cognito authentication and AppSync GraphQL API
- **Theme Support**: Light and dark mode with system preference detection
- **Type Safety**: Full TypeScript support with strict type checking
- **Modern Fonts**: Inter font family for clean, readable typography

## Tech Stack

- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with custom design system
- **Components**: Shadcn/ui with Radix UI primitives
- **Routing**: React Router DOM
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Theme**: Next Themes for dark/light mode
- **Package Manager**: Yarn

## Project Structure

```
src/
├── components/
│   └── ui/                 # Reusable UI components
│       ├── button.tsx
│       ├── input.tsx
│       ├── card.tsx
│       ├── navbar.tsx
│       ├── layout.tsx
│       └── ...
├── pages/                  # Page components
│   ├── auth/
│   │   └── LoginPage.tsx
│   ├── DashboardPage.tsx
│   ├── NotesPage.tsx
│   ├── SettingsPage.tsx
│   └── ProfilePage.tsx
├── lib/
│   └── utils.ts           # Utility functions
├── App.tsx                # Main app component
├── main.tsx              # App entry point
└── index.css             # Global styles and CSS variables
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- Yarn package manager

### Installation

1. **Install dependencies**:
   ```bash
   yarn install
   ```

2. **Start the development server**:
   ```bash
   yarn dev
   ```

3. **Open your browser** and navigate to `http://localhost:3000`

### Available Scripts

- `yarn dev` - Start development server
- `yarn build` - Build for production
- `yarn preview` - Preview production build
- `yarn lint` - Run ESLint

## Design System

### Colors

The project uses a comprehensive color system with CSS variables:

- **Primary**: Blue gradient (#007bff to #6f42c1)
- **Secondary**: Purple gradient (#6f42c1 to #007bff) 
- **Accent**: Teal (#20c997)
- **Success**: Green (#28a745)
- **Warning**: Orange (#ffc107)
- **Error**: Red (#dc3545)

### Typography

- **Font Family**: Inter (Google Fonts)
- **Font Weights**: 100-900
- **Font Features**: Ligatures and contextual alternates enabled

### Components

All components follow these principles:

- **Accessibility**: WCAG 2.1 AA compliant
- **Responsive**: Mobile-first design
- **Consistent**: Unified spacing and sizing system
- **Themeable**: Support for light and dark modes
- **Type Safe**: Full TypeScript support

## Authentication

The portal is designed to integrate with AWS Cognito for authentication. The current implementation includes:

- Login page with email/password
- Social login support (Google, Microsoft)
- Forgot password flow
- Protected routes
- User session management

## AWS Integration

The project is structured to integrate with:

- **AWS Cognito**: User authentication and management
- **AWS AppSync**: GraphQL API for data operations
- **AWS Amplify**: Frontend hosting and CI/CD

## Development Guidelines

### Component Creation

1. Use the established component patterns in `src/components/ui/`
2. Follow the naming convention: PascalCase for components
3. Include proper TypeScript interfaces
4. Use class-variance-authority for variant-based styling
5. Include proper accessibility attributes

### Styling

1. Use Tailwind CSS utility classes
2. Leverage CSS variables for theming
3. Follow mobile-first responsive design
4. Use the established spacing and sizing system

### State Management

1. Use React hooks for local state
2. Implement proper error boundaries
3. Follow the established loading state patterns

## Contributing

1. Follow the established code style and patterns
2. Ensure all components are accessible
3. Test in both light and dark modes
4. Verify mobile responsiveness
5. Update documentation as needed

## License

This project is part of the Notefy system and follows the established licensing terms.

## Support

For questions and support, please refer to the project documentation or contact the development team.
