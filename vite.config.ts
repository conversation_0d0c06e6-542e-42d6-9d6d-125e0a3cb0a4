import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import fs from 'fs'

// Check if SSL certificates exist (for local development only)
const sslKeyPath = './localhost.key'
const sslCertPath = './localhost.crt'
const hasSSLCerts = fs.existsSync(sslKeyPath) && fs.existsSync(sslCertPath)

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: {
    // Add Node.js global polyfills for browser compatibility
    global: 'globalThis',
    'process.env': {},
  },
  server: {
    port: 5173,
    host: true,
    // Only use HTTPS if SSL certificates exist (local development)
    ...(hasSSLCerts && {
      // https: {
      //   key: fs.readFileSync(sslKeyPath),
      //   cert: fs.readFileSync(sslCertPath),
      // },
    }),
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})
