{"name": "notefy-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:https": "vite --host --port 5173", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@aws-amplify/auth": "^6.13.3", "@aws-amplify/ui-react": "^6.11.2", "@aws-amplify/ui-react-storage": "^3.11.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-presence": "^1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "aws-amplify": "^6.15.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.522.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.6.2", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.7"}, "devDependencies": {"@aws-amplify/core": "^6.12.3", "@eslint/js": "^9.25.0", "@tailwindcss/typography": "^0.5.10", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}