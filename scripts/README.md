# Notefy Scripts Directory

This directory contains utility scripts for the Notefy React application.

## Environment Configuration Generator

### `generate-env.sh`

**Purpose**: Automatically extracts configuration values from Terraform infrastructure and generates a complete `.env` file for the React application.

**Features**:
- ✅ Extracts all AWS service configurations from Terraform state
- ✅ Handles multiple environments (dev, staging, production)
- ✅ Generates environment-specific values automatically
- ✅ Validates configuration and checks for missing values
- ✅ Colorized output with detailed progress information

### Usage

#### Basic Usage (Development Environment)
```bash
cd notefy-app/scripts
./generate-env.sh
```

#### Specify Environment
```bash
cd notefy-app/scripts
./generate-env.sh dev      # Development environment
./generate-env.sh main     # Production environment
```

### Prerequisites

1. **Terraform Infrastructure**: Your infrastructure must be deployed first
   ```bash
   cd infra
   terraform init
   terraform plan
   terraform apply
   ```

2. **Required Tools**:
   - `terraform` CLI
   - `jq` (for JSON processing)
   - `bash` shell

3. **AWS Configuration**: Ensure your AWS credentials are configured for the appropriate profile

### What Gets Generated

The script generates a comprehensive `.env` file with:

#### AWS Services Configuration
- **Cognito Authentication**: User pools, client IDs, OAuth settings
- **AppSync GraphQL**: Endpoints, API keys, real-time configuration
- **S3 Storage**: Bucket names and regions
- **DynamoDB**: Table names for all services

#### Application Configuration
- **Environment Variables**: NODE_ENV, app name, version
- **Feature Flags**: Enable/disable specific features
- **Domain Configuration**: Environment-specific URLs
- **Security Settings**: CORS, authentication flows

#### Development Settings
- **Debug Mode**: Environment-specific debugging
- **Logging**: Log levels and monitoring
- **Build Configuration**: Source maps, minification

### Environment-Specific Values

| Setting | Development | Production |
|---------|-------------|------------|
| NODE_ENV | `development` | `production` |
| Domains | `*.dev.notefy.app` | `*.notefy.app` |
| Debug Mode | `true` | `false` |
| Source Maps | `true` | `false` |
| Analytics | `false` | `true` |
| Log Level | `debug` | `error` |

### Generated File Structure

```env
# =============================================================================
# Notefy React Application Environment Configuration
# Generated from Terraform Infrastructure (@/infra)
# =============================================================================

# Environment Configuration
NODE_ENV=development
VITE_APP_NAME=notefy
VITE_ENVIRONMENT=dev

# AWS Cognito Authentication Configuration
VITE_AWS_COGNITO_USER_POOL_ID=me-central-1_XXXXXXXXX
VITE_AWS_COGNITO_WEB_CLIENT_ID=XXXXXXXXXXXXXXXXXXXXXXXXXX
# ... (50+ environment variables)
```

### Troubleshooting

#### Common Issues

1. **"No Terraform state found"**
   ```bash
   cd infra
   terraform init
   terraform apply
   ```

2. **"Infrastructure directory not found"**
   - Ensure you're running the script from `notefy-app/scripts/`
   - Check that `../infra` directory exists

3. **"Placeholder values (XXXXXXXXXX) found"**
   - Some Terraform outputs may be missing
   - Ensure all infrastructure is deployed
   - Check Terraform workspace selection

4. **"jq command not found"**
   ```bash
   # macOS
   brew install jq
   
   # Ubuntu/Debian
   sudo apt-get install jq
   
   # CentOS/RHEL
   sudo yum install jq
   ```

### Script Output Example

```bash
🚀 Notefy Environment Configuration Generator
=============================================

📋 Selecting Terraform workspace: dev
🔍 Extracting configuration from Terraform...
🔐 Extracting Cognito configuration...
📊 Extracting AppSync GraphQL configuration...
🗄️ Extracting S3 storage configuration...
🗃️ Extracting DynamoDB table configuration...
💬 Extracting WhatsApp configuration...
🛍️ Extracting Shopify integration configuration...
📝 Generating .env file...

✅ Environment configuration generated successfully!
📍 File location: ../.env
🔧 Workspace: dev

📊 Configuration Summary:
Environment: dev
Node ENV: development
Portal Domain: app.dev.notefy.app
GraphQL Endpoint: https://abc123.appsync.me-central-1.amazonaws.com/graphql
User Pool ID: me-central-1_ABC123DEF
Web Client ID: 1234567890abcdef1234567890

🔍 Checking for placeholder values...
✅ All configuration values extracted successfully!

🚀 Next steps:
1. Review the generated .env file
2. Ensure all values are correct (no XXXXXXXXXX placeholders)
3. Start your development server: yarn dev
4. For production: ./generate-env.sh main

🎉 Environment configuration complete!
```

### Security Considerations

- ✅ **No Sensitive Data**: Script only extracts public configuration values
- ✅ **Local Generation**: Environment file is generated locally
- ✅ **Gitignore**: `.env` files are excluded from version control
- ⚠️ **API Keys**: AppSync API keys are included but have limited scope

### Integration with Development Workflow

1. **Initial Setup**:
   ```bash
   cd notefy-app/scripts
   ./generate-env.sh dev
   ```

2. **Development**:
   ```bash
   cd notefy-app
   yarn dev
   ```

3. **Production Deployment**:
   ```bash
   cd notefy-app/scripts
   ./generate-env.sh main
   cd ..
   yarn build
   ```

### Future Enhancements

- [ ] Support for additional cloud providers
- [ ] Validation of generated configuration
- [ ] Integration with CI/CD pipelines
- [ ] Automatic environment detection
- [ ] Configuration encryption support

---

## Additional Scripts

### Future Scripts (Planned)

- `deploy.sh` - Automated deployment script
- `test-env.sh` - Environment configuration testing
- `backup-config.sh` - Configuration backup utility
- `migrate-env.sh` - Environment migration script

---

## Contributing

When adding new scripts:

1. Make scripts executable: `chmod +x script-name.sh`
2. Add comprehensive documentation
3. Include error handling and validation
4. Use consistent output formatting
5. Test across different environments

---

## Support

For issues with the environment generation script:

1. Check the troubleshooting section above
2. Verify Terraform infrastructure is deployed
3. Ensure all required tools are installed
4. Review the generated `.env` file for missing values

**Note**: This script is designed to work with the specific Terraform infrastructure in the `@/infra` directory. Modifications may be needed for different infrastructure setups. 