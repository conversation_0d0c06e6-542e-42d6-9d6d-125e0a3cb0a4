import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MessageSquare, ArrowRight, Shield, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PhoneInput } from '@/components/ui/phone-input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { isValidPhoneNumber } from 'react-phone-number-input';
import { NotificationContainer, useNotifications } from '@/components/ui/notification';
import { useAuth } from '@/hooks/useAuth';

// Simple validation for phone number only
const loginSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Phone number is required')
    .refine(isValidPhoneNumber, {
      message: 'Please enter a valid phone number with country code',
    }),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const WhatsAppLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const notifications = useNotifications();
  
  const { 
    signIn,
    isLoading, 
    isAuthenticated,
    error,
    clearError,
  } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Extract navigation state (use useMemo to stabilize dependencies)
  const from = (location.state as any)?.from?.pathname || '/portal/analytics';
  
  const successMessage = (location.state as any)?.message;
  const prefillPhoneNumber = (location.state as any)?.phoneNumber;

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      phoneNumber: prefillPhoneNumber || '',
    },
  });

  const watchedPhoneNumber = watch('phoneNumber');

  // Set prefilled phone number when component mounts
  useEffect(() => {
    if (prefillPhoneNumber) {
      setValue('phoneNumber', prefillPhoneNumber);
    }
  }, [prefillPhoneNumber, setValue]);

  // Redirect if already authenticated (only when user is actually on this page)
  useEffect(() => {
    if (isAuthenticated && location.pathname === '/auth/login') {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from, location.pathname]);

  // Show success message from navigation state
  useEffect(() => {
    if (successMessage) {
      notifications.success('Success', successMessage);
    }
  }, [successMessage, notifications]);

  // Handle auth errors
  useEffect(() => {
    if (error) {
      const formattedError = (error as any).message || 'Authentication Error';
      notifications.error('Authentication Error', formattedError);
      clearError();
    }
  }, [error, notifications, clearError]);

  const handleWhatsAppLogin = async (data: LoginFormData) => {
    setIsSubmitting(true);
    clearError();

    try {
      // Initiate passwordless WhatsApp authentication
      const result = await signIn({
        phoneNumber: data.phoneNumber,
      });

      if (result.requiresMFA && result.challengeName === 'CUSTOM_CHALLENGE') {
        // Successfully initiated WhatsApp OTP - move to verification
        notifications.success(
          'WhatsApp Code Sent!', 
          `Please check your WhatsApp on ${data.phoneNumber} for the verification code.`
        );
        navigate('/auth/verify', { 
          state: { 
            phoneNumber: data.phoneNumber,
            from,
            verificationType: 'signin'
          } 
        });
      } else if (result.success) {
        notifications.success('Welcome back!', 'You have been signed in successfully.');
        navigate(from, { replace: true });
      } else {
        notifications.error('Sign In Failed', result.error || 'Unable to send WhatsApp verification code.');
      }
    } catch (error: any) {
      console.error('WhatsApp login error:', error);
      const formattedError = (error as any).message || 'Sign In Failed';
      notifications.error('Sign In Failed', formattedError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        {/* Header Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-blue-500/5"></div>
          <div className="relative px-6 py-8">
            <div className="max-w-md mx-auto text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-lg shadow-emerald-500/25 mb-6">
                <span className="text-2xl font-bold text-white">N</span>
              </div>
              <div className="space-y-2">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Welcome to Notefy
                </h1>
                <p className="text-slate-600 text-lg">
                  Enterprise WhatsApp Business Platform
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center px-6 py-8">
          <div className="w-full max-w-md">
            <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-sm ring-1 ring-slate-200/50">
              <CardHeader className="text-center pb-6 space-y-4">
                <div className="flex items-center justify-center gap-2">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 px-3 py-1">
                    <Shield className="w-3 h-3 mr-1" />
                    Passwordless Login
                  </Badge>
                </div>
                <div className="space-y-2">
                  <CardTitle className="text-2xl font-semibold text-slate-900">
                    Sign In
                  </CardTitle>
                  <CardDescription className="text-slate-600 leading-relaxed">
                    Enter your WhatsApp number to receive a secure verification code
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit(handleWhatsAppLogin)} className="space-y-6">
                  
                  {/* Phone Number Input */}
                  <div className="space-y-2">
                    <Controller
                      name="phoneNumber"
                      control={control}
                      render={({ field }) => (
                        <div className="space-y-3">
                          <label htmlFor="phoneNumber" className="block text-sm font-semibold text-slate-700 tracking-tight">
                            WhatsApp Phone Number
                          </label>
                          <PhoneInput
                            {...field}
                            international
                            defaultCountry="AE"
                            placeholder="Enter your phone number"
                            error={errors.phoneNumber?.message}
                            disabled={isSubmitting || isLoading}
                          />
                          <p className="text-xs text-slate-500 flex items-center gap-2 leading-relaxed">
                            <MessageSquare className="w-3 h-3 text-emerald-500 flex-shrink-0" />
                            Ensure WhatsApp is installed on this number
                          </p>
                        </div>
                      )}
                    />
                  </div>

                  {/* WhatsApp Login Button */}
                  <Button
                    type="submit"
                    className="w-full h-13 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-medium shadow-lg shadow-emerald-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] transform"
                    disabled={isSubmitting || isLoading || !watchedPhoneNumber}
                  >
                    {isSubmitting || isLoading ? (
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Sending verification code...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-3">
                        <MessageSquare className="w-5 h-5" />
                        <span>Send WhatsApp Code</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </div>
                    )}
                  </Button>
                </form>

                {/* Security Features */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-slate-200" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-3 text-slate-500 font-medium">Secure & Passwordless</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col items-center p-4 rounded-lg bg-slate-50/50 border border-slate-100">
                    <Shield className="w-6 h-6 text-emerald-600 mb-2" />
                    <span className="text-xs font-medium text-slate-700">End-to-End</span>
                    <span className="text-xs text-slate-500">Encrypted</span>
                  </div>
                  <div className="flex flex-col items-center p-4 rounded-lg bg-slate-50/50 border border-slate-100">
                    <Sparkles className="w-6 h-6 text-blue-600 mb-2" />
                    <span className="text-xs font-medium text-slate-700">No Password</span>
                    <span className="text-xs text-slate-500">Required</span>
                  </div>
                </div>

                {/* Sign Up Link */}
                <div className="text-center pt-4 border-t border-slate-100">
                  <p className="text-sm text-slate-600">
                    New to Notefy?{' '}
                    <button
                      onClick={() => navigate('/auth/signup', { state: { phoneNumber: watchedPhoneNumber, from } })}
                      className="text-emerald-600 hover:text-emerald-700 font-medium transition-colors hover:underline"
                    >
                      Create your account
                    </button>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-slate-500">
                By signing in, you agree to our{' '}
                <button className="text-emerald-600 hover:underline">Terms of Service</button>
                {' '}and{' '}
                <button className="text-emerald-600 hover:underline">Privacy Policy</button>
              </p>
            </div>
          </div>
        </div>
      </div>

      <NotificationContainer
        notifications={notifications.notifications}
        onDismiss={notifications.removeNotification}
      />
    </>
  );
};
