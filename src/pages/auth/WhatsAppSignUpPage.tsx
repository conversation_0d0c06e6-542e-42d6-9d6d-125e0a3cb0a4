import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MessageSquare, User, Mail, UserPlus, ArrowRight, Shield, Building, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { PhoneInput } from '@/components/ui/phone-input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { isValidPhoneNumber } from 'react-phone-number-input';
import { NotificationContainer, useNotifications } from '@/components/ui/notification';
import { useAuth } from '@/hooks/useAuth';

// Simple signup schema for passwordless authentication
const signUpSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Phone number is required')
    .refine(isValidPhoneNumber, {
      message: 'Please enter a valid phone number with country code',
    }),
  email: z
    .string()
    .email('Please enter a valid email address')
    .or(z.literal('')), // Allow empty string for optional email
  givenName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name is too long')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  familyName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name is too long')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  // Temporary password for Cognito (not used in passwordless flow)
  password: z.string().min(8),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
});

type SignUpFormData = z.infer<typeof signUpSchema>;

export const WhatsAppSignUpPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const notifications = useNotifications();
  
  const { 
    signUp,
    isLoading, 
    isAuthenticated,
    error,
    clearError,
  } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Extract navigation state
  const from = (location.state as any)?.from?.pathname || '/portal/analytics';
  const prefillPhoneNumber = (location.state as any)?.phoneNumber;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      phoneNumber: prefillPhoneNumber || '',
      email: '',
      givenName: '',
      familyName: '',
      agreeToTerms: false,
      password: 'TempPassword123!', // Hidden temporary password
    },
  });

  const watchedPhoneNumber = watch('phoneNumber');

  // Set prefilled phone number when component mounts
  useEffect(() => {
    if (prefillPhoneNumber) {
      setValue('phoneNumber', prefillPhoneNumber);
    }
  }, [prefillPhoneNumber, setValue]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // Handle auth errors
  useEffect(() => {
    if (error) {
      notifications.error('Registration Error', error);
      clearError();
    }
  }, [error, notifications, clearError]);

  const handleWhatsAppSignUp = async (data: SignUpFormData) => {
    setIsSubmitting(true);
    clearError();

    try {
      const result = await signUp({
        phoneNumber: data.phoneNumber,
        businessName: `${data.givenName} ${data.familyName}`, // Combine first and last name
        email: data.email || undefined,
        termsAccepted: data.agreeToTerms,
      });

      if (result.success) {
        notifications.success(
          'Account Created!', 
          'Please check your WhatsApp for the verification code.'
        );
        navigate('/auth/verify', { 
          state: { 
            phoneNumber: data.phoneNumber,
            from,
            isNewUser: true,
            verificationType: 'signup'
          } 
        });
      } else {
        notifications.error('Registration Failed', result.error || 'Unable to create account.');
      }
    } catch (error: any) {
      console.error('WhatsApp signup error:', error);
      notifications.error('Registration Failed', 'Unable to create account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-emerald-50/30">
        {/* Header Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-blue-500/5"></div>
          <div className="relative px-6 py-8">
            <div className="max-w-lg mx-auto text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-lg shadow-emerald-500/25 mb-6">
                <span className="text-2xl font-bold text-white">N</span>
              </div>
              <div className="space-y-2">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Join Notefy Today
                </h1>
                <p className="text-slate-600 text-lg">
                  Create your enterprise WhatsApp business account
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center px-6 py-8">
          <div className="w-full max-w-lg">
            <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-sm ring-1 ring-slate-200/50">
              <CardHeader className="text-center pb-6 space-y-4">
                <div className="flex items-center justify-center gap-2">
                  <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 px-3 py-1">
                    <Building className="w-3 h-3 mr-1" />
                    Enterprise Account
                  </Badge>
                </div>
                <div className="space-y-2">
                  <CardTitle className="text-2xl font-semibold text-slate-900">
                    Create Account
                  </CardTitle>
                  <CardDescription className="text-slate-600 leading-relaxed">
                    Set up your business profile with passwordless WhatsApp authentication
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit(handleWhatsAppSignUp)} className="space-y-6">
                  
                  {/* Personal Information Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2">
                      <User className="w-4 h-4 text-slate-600" />
                      <h3 className="text-sm font-medium text-slate-700">Personal Information</h3>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        id="givenName"
                        type="text"
                        label="First Name"
                        placeholder="Enter your first name"
                        error={errors.givenName?.message}
                        disabled={isSubmitting || isLoading}
                        leftIcon={<User className="w-4 h-4" />}
                        {...register('givenName')}
                      />
                      
                      <Input
                        id="familyName"
                        type="text"
                        label="Last Name"
                        placeholder="Enter your last name"
                        error={errors.familyName?.message}
                        disabled={isSubmitting || isLoading}
                        leftIcon={<User className="w-4 h-4" />}
                        {...register('familyName')}
                      />
                    </div>
                  </div>

                  {/* Contact Information Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2">
                      <MessageSquare className="w-4 h-4 text-slate-600" />
                      <h3 className="text-sm font-medium text-slate-700">Contact Information</h3>
                    </div>

                    {/* Phone Number Input */}
                    <div className="space-y-2">
                      <Controller
                        name="phoneNumber"
                        control={control}
                        render={({ field }) => (
                          <div className="space-y-3">
                            <label htmlFor="phoneNumber" className="block text-sm font-semibold text-slate-700 tracking-tight">
                              WhatsApp Business Number
                            </label>
                            <PhoneInput
                              {...field}
                              international
                              defaultCountry="AE"
                              placeholder="Enter your WhatsApp number"
                              error={errors.phoneNumber?.message}
                              disabled={isSubmitting || isLoading}
                            />
                            <p className="text-xs text-slate-500 flex items-center gap-2 leading-relaxed">
                              <Shield className="w-3 h-3 text-emerald-500 flex-shrink-0" />
                              This will be your primary login method
                            </p>
                          </div>
                        )}
                      />
                    </div>

                    {/* Email Input (Optional) */}
                    <Input
                      id="email"
                      type="email"
                      label="Email Address (Optional)"
                      placeholder="Enter your email address"
                      description="For account recovery and important notifications"
                      error={errors.email?.message}
                      disabled={isSubmitting || isLoading}
                      leftIcon={<Mail className="w-4 h-4" />}
                      {...register('email')}
                    />
                  </div>

                  {/* Terms Agreement */}
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3 p-4 bg-slate-50/50 rounded-lg border border-slate-200">
                      <Checkbox
                        {...register('agreeToTerms')}
                        disabled={isSubmitting || isLoading}
                        className="mt-0.5"
                      />
                      <div className="text-sm text-slate-600 leading-relaxed">
                        <span>I acknowledge that I have read and agree to the </span>
                        <button
                          type="button"
                          className="text-emerald-600 hover:text-emerald-700 font-medium hover:underline transition-colors"
                          onClick={() => window.open('/terms', '_blank')}
                        >
                          Terms of Service
                        </button>
                        <span> and </span>
                        <button
                          type="button"
                          className="text-emerald-600 hover:text-emerald-700 font-medium hover:underline transition-colors"
                          onClick={() => window.open('/privacy', '_blank')}
                        >
                          Privacy Policy
                        </button>
                        <span>. I understand this creates an enterprise business account.</span>
                      </div>
                    </div>
                    {errors.agreeToTerms && (
                      <p className="text-red-500 text-sm flex items-center gap-1">
                        <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                        {errors.agreeToTerms.message}
                      </p>
                    )}
                  </div>

                  {/* Sign Up Button */}
                  <Button
                    type="submit"
                    className="w-full h-13 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-medium shadow-lg shadow-emerald-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] transform"
                    disabled={isSubmitting || isLoading}
                  >
                    {isSubmitting || isLoading ? (
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Creating your account...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-3">
                        <UserPlus className="w-5 h-5" />
                        <span>Create Enterprise Account</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </div>
                    )}
                  </Button>
                </form>

                {/* Features Overview */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-slate-200" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-3 text-slate-500 font-medium">What You Get</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  {[
                    { icon: MessageSquare, text: "WhatsApp Business API Integration" },
                    { icon: Shield, text: "Enterprise-grade Security" },
                    { icon: Building, text: "Multi-channel Customer Support" },
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-slate-50/50 border border-slate-100">
                      <feature.icon className="w-5 h-5 text-emerald-600 flex-shrink-0" />
                      <span className="text-sm text-slate-700">{feature.text}</span>
                      <Check className="w-4 h-4 text-emerald-600 ml-auto" />
                    </div>
                  ))}
                </div>

                {/* Sign In Link */}
                <div className="text-center pt-4 border-t border-slate-100">
                  <p className="text-sm text-slate-600">
                    Already have an account?{' '}
                    <button
                      onClick={() => navigate('/auth/login', { state: { phoneNumber: watchedPhoneNumber, from } })}
                      className="text-emerald-600 hover:text-emerald-700 font-medium transition-colors hover:underline"
                    >
                      Sign in to your account
                    </button>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-slate-500">
                Protected by enterprise-grade security.{' '}
                <button className="text-emerald-600 hover:underline">Learn more</button>
              </p>
            </div>
          </div>
        </div>
      </div>

      <NotificationContainer
        notifications={notifications.notifications}
        onDismiss={notifications.removeNotification}
      />
    </>
  );
};
