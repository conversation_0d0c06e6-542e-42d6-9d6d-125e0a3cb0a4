import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MessageSquare, Shield, RefreshCw, ArrowLeft, Clock, CheckCircle, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { NotificationContainer, useNotifications } from '@/components/ui/notification';
import { useAuth } from '@/hooks/useAuth';
import { ErrorUtils } from '@/lib/utils';

// Validation for 6-digit WhatsApp OTP code
const verifySchema = z.object({
  code: z
    .string()
    .length(6, 'Verification code must be exactly 6 digits')
    .regex(/^\d{6}$/, 'Verification code must contain only numbers'),
});

type VerifyFormData = z.infer<typeof verifySchema>;

export const WhatsAppVerifyPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const notifications = useNotifications();
  
  const { 
    confirmSignIn,
    confirmSignUp,
    signIn,
    resendConfirmationCode,
    isLoading, 
    error,
    clearError,
  } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Extract navigation state (use useMemo to stabilize dependencies)
  const from = useMemo(() => {
    return (location.state as any)?.from?.pathname || '/portal/analytics';
  }, [location.state]);
  
  const phoneNumber = (location.state as any)?.phoneNumber;
  const verificationType = (location.state as any)?.verificationType || 'signin'; // 'signup' or 'signin'

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<VerifyFormData>({
    resolver: zodResolver(verifySchema),
    mode: 'onChange',
  });

  const code = watch('code');

  // Redirect if no phone number provided
  useEffect(() => {
    if (!phoneNumber) {
      navigate('/auth/login');
    }
  }, [phoneNumber, navigate]);

  // Note: Manual redirect handling in handleVerifyCode to avoid race conditions

  // Handle auth errors
  useEffect(() => {
    if (error) {
      const formattedError = ErrorUtils.formatErrorMessage(error);
      notifications.error('Verification Error', formattedError);
      clearError();
    }
  }, [error, notifications, clearError]);

  // Resend cooldown timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const handleVerifyCode = async (data: VerifyFormData) => {
    setIsSubmitting(true);
    clearError();

    try {
      if (!phoneNumber) {
        notifications.error("Verification Failed", "Phone number is missing. Please try signing up again.");
        navigate("/auth/signup", { replace: true });
        return;
      }

      let result;
      if (verificationType === 'signup') {
        // Use confirmSignUp for account verification after signup
        result = await confirmSignUp(phoneNumber, data.code);
        
        if (result.success) {
          notifications.success('Account Verified!', 'Your account has been verified. Please sign in to continue.');
          navigate('/auth/login', { 
            state: { phoneNumber },
            replace: true 
          });
        } else {
          notifications.error('Verification Failed', result.error || 'Invalid verification code. Please try again.');
        }
      } else {
        // Use confirmSignIn for MFA verification after signin
        result = await confirmSignIn(data.code);
        
        if (result.success) {
          notifications.success('Welcome to Notefy!', 'You have been successfully authenticated.');
          // Force redirect immediately - use window.location for reliable navigation
          window.location.href = from;
        } else {
          notifications.error('Verification Failed', result.error || 'Invalid verification code. Please try again.');
        }
      }
    } catch (error: any) {
      console.error('Code verification error:', error);
      const formattedError = ErrorUtils.formatErrorMessage(error);
      notifications.error('Verification Failed', formattedError);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (!phoneNumber || resendCooldown > 0) return;
    
    setIsSubmitting(true);
    clearError();

    try {
      if (!phoneNumber) {
        notifications.error("Verification Failed", "Phone number is missing. Please try signing up again.");
        navigate("/auth/signup", { replace: true });
        return;
      }

      if (verificationType === 'signup') {
        // Use resendConfirmationCode for account verification
        const success = await resendConfirmationCode(phoneNumber);
        
        if (success) {
          setResendCooldown(60); // 60 second cooldown
          notifications.success('Code Resent', 'A new verification code has been sent to your WhatsApp.');
        } else {
          notifications.error('Resend Failed', 'Unable to resend verification code. Please try again.');
        }
      } else {
        // Use signIn to trigger new MFA challenge
        const result = await signIn({ phoneNumber });
        
        if (result.requiresMFA) {
          setResendCooldown(60); // 60 second cooldown
          notifications.success('Code Resent', 'A new verification code has been sent to your WhatsApp.');
        } else {
          notifications.error('Resend Failed', 'Unable to resend verification code. Please try again.');
        }
      }
    } catch (error: any) {
      console.error('Resend code error:', error);
      const formattedError = ErrorUtils.formatErrorMessage(error);
      notifications.error('Resend Failed', formattedError);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-format verification code input (only digits, max 6)
  const handleCodeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setValue('code', value, { shouldValidate: true });
  };

  // Mask phone number for display
  const maskedPhoneNumber = phoneNumber ? 
    phoneNumber.replace(/(\+\d{1,3})(\d{2,3})(\d+)(\d{4})/, '$1$2***$4') : '';

  return (
    <>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-emerald-50/30">
        {/* Header Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-blue-500/5"></div>
          <div className="relative px-6 py-8">
            <div className="max-w-md mx-auto">
              {/* Back Button */}
              <button
                onClick={() => navigate(verificationType === 'signup' ? '/auth/signup' : '/auth/login')}
                className="inline-flex items-center text-sm text-slate-600 hover:text-slate-900 transition-colors group mb-6"
              >
                <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
                Back to {verificationType === 'signup' ? 'signup' : 'login'}
              </button>
              
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-lg shadow-emerald-500/25 mb-6">
                  <MessageSquare className="w-8 h-8 text-white" />
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                    {verificationType === 'signup' ? 'Verify Your Account' : 'WhatsApp Verification'}
                  </h1>
                  <p className="text-slate-600 text-lg">
                    Enter the 6-digit code sent to your WhatsApp
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center px-6 py-8">
          <div className="w-full max-w-md">
            <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-sm ring-1 ring-slate-200/50">
              <CardHeader className="text-center pb-6 space-y-4">
                <div className="flex items-center justify-center gap-2">
                  <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 px-3 py-1">
                    <Shield className="w-3 h-3 mr-1" />
                    Secure Verification
                  </Badge>
                </div>
                
                {/* Phone Number Display */}
                <div className="flex items-center justify-center gap-2 p-3 bg-slate-50/50 rounded-lg border border-slate-200">
                  <Phone className="w-4 h-4 text-slate-600" />
                  <span className="text-sm font-medium text-slate-700">{maskedPhoneNumber}</span>
                </div>
                
                <div className="space-y-2">
                  <CardTitle className="text-xl font-semibold text-slate-900">
                    Enter Verification Code
                  </CardTitle>
                  <CardDescription className="text-slate-600 leading-relaxed">
                    {verificationType === 'signup' 
                      ? 'We sent a 6-digit verification code to your WhatsApp to verify your account.'
                      : 'We sent a 6-digit verification code to your WhatsApp for secure login.'
                    }
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit(handleVerifyCode)} className="space-y-6">
                  
                  {/* Verification Code Input */}
                  <div className="space-y-4">
                    <div className="space-y-4">
                      <label htmlFor="code" className="block text-sm font-semibold text-slate-700 text-center tracking-tight">
                        6-Digit Verification Code
                      </label>
                                              <Input
                          id="code"
                          type="text"
                          placeholder="000000"
                          value={code || ''}
                          onChange={handleCodeInput}
                          maxLength={6}
                          className="text-center text-3xl tracking-[0.75em] font-mono h-16 pr-8"
                          disabled={isSubmitting || isLoading}
                          autoComplete="one-time-code"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          error={errors.code?.message}
                        />
                      <p className="text-xs text-slate-500 text-center flex items-center justify-center gap-2 leading-relaxed">
                        <MessageSquare className="w-3 h-3 text-emerald-500 flex-shrink-0" />
                        Check your WhatsApp messages
                      </p>
                    </div>
                  </div>

                  {/* Verify Button */}
                  <Button
                    type="submit"
                    className="w-full h-13 bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white font-medium shadow-lg shadow-emerald-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] transform"
                    disabled={isSubmitting || isLoading || !code || code.length !== 6}
                  >
                    {isSubmitting || isLoading ? (
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Verifying code...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-3">
                        <CheckCircle className="w-5 h-5" />
                        <span>Verify & Continue</span>
                      </div>
                    )}
                  </Button>
                </form>

                {/* Resend Code Section */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-slate-200" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-3 text-slate-500 font-medium">Need a new code?</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={handleResendCode}
                  disabled={resendCooldown > 0 || isSubmitting}
                  className="w-full h-12 border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all duration-200"
                >
                  {resendCooldown > 0 ? (
                    <div className="flex items-center space-x-3">
                      <Clock className="w-4 h-4" />
                      <span>Resend available in {resendCooldown}s</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <RefreshCw className="w-4 h-4" />
                      <span>Resend WhatsApp Code</span>
                    </div>
                  )}
                </Button>

                {/* Help Information */}
                <div className="bg-emerald-50/50 border border-emerald-200/50 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-emerald-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-emerald-800">
                      <p className="font-medium mb-1">Secure WhatsApp verification</p>
                      <ul className="text-xs space-y-1 text-emerald-700">
                        <li>• Code expires in 5 minutes</li>
                        <li>• Check your WhatsApp messages</li>
                        <li>• Enter the 6-digit code you received</li>
                        <li>• Request a new code if needed</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-slate-500">
                Having trouble?{' '}
                <button className="text-emerald-600 hover:underline">Contact support</button>
              </p>
            </div>
          </div>
        </div>
      </div>

      <NotificationContainer
        notifications={notifications.notifications}
        onDismiss={notifications.removeNotification}
      />
    </>
  );
};
