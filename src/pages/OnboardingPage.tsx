import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { OnboardingLayout, OnboardingCompletion } from '@/components/onboarding/OnboardingLayout';
import { WelcomeStep } from '@/components/onboarding/WelcomeStep';
import { ShopifyStep } from '@/components/onboarding/ShopifyStep';
import { WhatsAppStep } from '@/components/onboarding/WhatsAppStep';
import { ConfigurationStep } from '@/components/onboarding/ConfigurationStep';
import { createOnboardingSteps } from '@/components/ui/onboarding-progress-badge';
import { useCurrentUser } from '@/hooks/useGraphQL';
import { useResponsiveBreakpoint } from '@/components/ui/responsive-grid';

// ============================================================================
// Main Onboarding Page Component
// ============================================================================

export const OnboardingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { data: user, loading: userLoading, error: userError } = useCurrentUser();
  const { isMobile } = useResponsiveBreakpoint();
  
  // Get current step from URL params, default to 'welcome'
  const currentStep = searchParams.get('step') || 'welcome';
  
  // Initialize onboarding steps
  const steps = createOnboardingSteps([
    { id: 'welcome', completed: true }, // Auto-completed after signup
  ]);

  // Track if onboarding is complete
  const [isComplete, setIsComplete] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userLoading && !user && !userError) {
      navigate('/auth/login', { replace: true });
    }
  }, [user, userLoading, userError, navigate]);

  // Handle step changes
  const handleStepChange = (stepId: string) => {
    setSearchParams({ step: stepId });
  };

  // Handle completing onboarding
  const handleComplete = () => {
    setIsComplete(true);
    setSearchParams({ step: 'complete' });
  };

  // Handle skipping onboarding
  const handleSkip = () => {
    navigate('/portal/analytics', { replace: true });
  };

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case 'welcome':
        return <WelcomeStep />;
      case 'shopify':
        return <ShopifyStep />;
      case 'whatsapp':
        return <WhatsAppStep />;
      case 'configuration':
        return <ConfigurationStep />;
      case 'complete':
        return <OnboardingCompletion />;
      default:
        return <WelcomeStep />;
    }
  };

  // Show loading state while checking authentication
  if (userLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 flex items-center justify-center p-4">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show completion component if onboarding is complete
  if (isComplete || currentStep === 'complete') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 flex items-center justify-center p-4">
        <div className="max-w-2xl w-full">
          <OnboardingCompletion />
        </div>
      </div>
    );
  }

  return (
    <OnboardingLayout
      currentStep={currentStep}
      onStepChange={handleStepChange}
      onComplete={handleComplete}
      onSkip={handleSkip}
      steps={steps}
      showProgressBadge={true}
      showStepIndicator={true}
      allowSkip={true}
      className={isMobile ? "mobile-optimized" : ""}
    >
      {renderStepContent()}
    </OnboardingLayout>
  );
};

export default OnboardingPage; 