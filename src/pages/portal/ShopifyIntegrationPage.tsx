// @ts-nocheck
// Shopify Integration Management Page
// Enterprise-grade Shopify store connection and webhook management

import React, { useState, useEffect } from 'react';
import {
  Store,
  Settings,
  Plus,
  CheckCircle,
  AlertCircle,
  Zap,
  Users,
  Webhook,
  ShoppingCart,
  TrendingUp,
  Bell,
  Package,
  RefreshCw,
  Globe,
  Palette,
  DollarSign,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useShopifyStores } from '@/hooks/useGraphQL';
import { ShopifyConnectionForm } from '@/components/ShopifyConnectionForm';

export const ShopifyIntegrationPage: React.FC = () => {
  const [showOAuthModal, setShowOAuthModal] = useState(false);
  const { data: stores, loading } = useShopifyStores();

  // Mock enhanced store data for demonstration
  const mockStores = [
    {
      id: '1',
      shopifyDomain: 'blntek-test.myshopify.com',
      storeName: 'BlnTek Test Store',
      storeUrl: 'https://blntek-test.myshopify.com',
      status: 'CONNECTED',
      connectedAt: '2024-01-15T10:30:00Z',
      plan: 'Professional',
      totalProducts: 156,
      totalOrders: 847,
      monthlyRevenue: 12340,
      currency: 'USD',
      owner: {
        name: 'Test Owner',
        email: '<EMAIL>'
      },
      theme: {
        name: 'Dawn',
        primaryColor: '#6366f1',
        accentColor: '#8b5cf6'
      },
      integration: {
        whatsappNotifications: true,
        realTimeSync: true,
        orderTracking: true,
        customerSync: true
      },
      metrics: {
        notificationsSent: 1240,
        notificationSuccess: 95.2,
        averageResponse: '2.3s',
        conversionRate: 3.8
      }
    }
  ];

  // Combine real and mock data for demonstration
  const displayStores = stores ? [...stores, ...mockStores] : mockStores;
  const totalRevenue = displayStores.reduce((acc, s) => acc + (s.monthlyRevenue || 0), 0);
  const totalNotifications = displayStores.reduce((acc, s) => acc + ((s.metrics && s.metrics.notificationsSent) || 0), 0);
  const avgSuccessRate = displayStores.length 
    ? displayStores.reduce((acc, s) => acc + ((s.metrics && s.metrics.notificationSuccess) || 0), 0) / displayStores.length
    : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'bg-green-100 text-green-800';
      case 'PENDING_WEBHOOKS':
        return 'bg-yellow-100 text-yellow-800';
      case 'DISCONNECTED':
        return 'bg-red-100 text-red-800';
      case 'ERROR':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'PENDING_WEBHOOKS':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'DISCONNECTED':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'ERROR':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getWebhookStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleOAuthConnection = () => {
    console.log('Starting Shopify OAuth connection...');
    setShowOAuthModal(true);
  };

  const handleConfigureWebhooks = (storeId: string) => {
    console.log('Configuring webhooks for store:', storeId);
  };

  const handleExtractTheme = (storeId: string) => {
    console.log('Extracting theme for store:', storeId);
  };

  const handleToggleNotifications = (storeId: string) => {
    console.log('Toggling notifications for store:', storeId);
  };

  const handleDisconnectStore = (storeId: string) => {
    console.log('Disconnecting store:', storeId);
  };

  const handleSyncProducts = (storeId: string) => {
    console.log('Syncing products for store:', storeId);
  };

  // Calculate totals for metrics
  const totalStores = displayStores.length;
  const connectedStores = displayStores.filter(s => s.status === 'CONNECTED').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shopify Integration</h1>
          <p className="text-gray-600 mt-1">
            Connect your Shopify stores with WhatsApp Business API for automated order notifications
          </p>
        </div>
        <Button onClick={handleOAuthConnection} className="mt-4 sm:mt-0">
          <Plus className="h-4 w-4 mr-2" />
          Connect Shopify Store
        </Button>
      </div>

      {/* Enterprise Features Banner */}
      <Card className="p-6 bg-gradient-to-r from-purple-50 to-green-50 border-purple-200">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-purple-100 rounded-lg">
            <Store className="h-8 w-8 text-purple-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">Enterprise Shopify Integration</h3>
            <p className="text-gray-600 mt-1">
              OAuth2 authentication • Real-time webhooks • Theme extraction • Order notifications • Customer sync
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">Real-time Sync</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">SOC Compliant</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-50 rounded-lg">
              <Store className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Connected Stores</p>
              <p className="text-2xl font-bold text-gray-900">{connectedStores}</p>
              <p className="text-xs text-gray-500">of {totalStores} total</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-50 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${totalRevenue.toLocaleString()}</p>
              <p className="text-xs text-gray-500">across all stores</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Bell className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Notifications Sent</p>
              <p className="text-2xl font-bold text-gray-900">{totalNotifications.toLocaleString()}</p>
              <p className="text-xs text-gray-500">WhatsApp messages</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <TrendingUp className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{avgSuccessRate.toFixed(1)}%</p>
              <p className="text-xs text-gray-500">notification delivery</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Stores List */}
      <div className="space-y-4">
        {loading ? (
          <Card className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading Shopify stores...</p>
          </Card>
        ) : displayStores.length === 0 ? (
          <Card className="p-8 text-center">
            <Store className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Shopify stores connected</h3>
            <p className="text-gray-600 mb-6">
              Connect your first Shopify store to start sending automated WhatsApp notifications
            </p>
            <Button onClick={handleOAuthConnection} className="bg-purple-600 hover:bg-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              Connect Your Store
            </Button>
          </Card>
        ) : (
          displayStores.map((store) => (
            <Card key={store.id} className="p-6">
              <div className="space-y-6">
                {/* Store Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <Store className="h-6 w-6 text-purple-600" />
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-3 mb-1">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {store.storeName}
                        </h3>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(store.status)}
                          <Badge className={getStatusColor(store.status)}>
                            {store.status.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Globe className="h-4 w-4" />
                          <span>{store.shopifyDomain}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Package className="h-4 w-4" />
                          <span>{store.plan}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{store.totalProducts} products</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {store.status === 'CONNECTED' && (
                      <>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleSyncProducts(store.id)}
                        >
                          <RefreshCw className="h-4 w-4 mr-1" />
                          Sync
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleExtractTheme(store.id)}
                        >
                          <Palette className="h-4 w-4 mr-1" />
                          Theme
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleConfigureWebhooks(store.id)}
                        >
                          <Webhook className="h-4 w-4 mr-1" />
                          Webhooks
                        </Button>
                      </>
                    )}
                    
                    {store.status === 'PENDING_WEBHOOKS' && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleConfigureWebhooks(store.id)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Setup Webhooks
                      </Button>
                    )}
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleDisconnectStore(store.id)}
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Manage
                    </Button>
                  </div>
                </div>

                {/* Theme Preview */}
                {store.theme && (
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Palette className="h-4 w-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-700">Brand Theme</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: store.theme.primaryColor }}
                        />
                        <span className="text-xs text-gray-500">{store.theme.primaryColor}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: store.theme.accentColor }}
                        />
                        <span className="text-xs text-gray-500">{store.theme.accentColor}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">Theme: {store.theme.name}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Integration Features */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <div className={`p-1 rounded ${store.integration?.whatsappNotifications ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Bell className={`h-3 w-3 ${store.integration?.whatsappNotifications ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <span className="text-xs text-gray-600">WhatsApp Notifications</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`p-1 rounded ${store.integration?.realTimeSync ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Zap className={`h-3 w-3 ${store.integration?.realTimeSync ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <span className="text-xs text-gray-600">Real-time Sync</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`p-1 rounded ${store.integration?.orderTracking ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Package className={`h-3 w-3 ${store.integration?.orderTracking ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <span className="text-xs text-gray-600">Order Tracking</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`p-1 rounded ${store.integration?.customerSync ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Users className={`h-3 w-3 ${store.integration?.customerSync ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <span className="text-xs text-gray-600">Customer Sync</span>
                  </div>
                </div>

                {/* Webhooks Status */}
                {store.status === 'CONNECTED' && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Webhook className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">Webhook Configuration</span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {(store.webhooks || []).map((webhook) => (
                        <div key={webhook.id} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs font-medium text-gray-700">
                              {webhook.type.replace('/', ' › ')}
                            </span>
                            <Badge className={getWebhookStatusColor(webhook.status)}>
                              {webhook.status}
                            </Badge>
                          </div>
                          <div className="text-xs text-gray-600 space-y-1">
                            <div>{webhook.totalDeliveries} deliveries</div>
                            <div>{webhook.successRate}% success rate</div>
                            {webhook.lastDelivery && (
                              <div>Last: {new Date(webhook.lastDelivery).toLocaleString()}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Performance Metrics */}
                {store.status === 'CONNECTED' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-4 border-t">
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 mb-1">
                        <ShoppingCart className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-gray-900">Orders</span>
                      </div>
                      <p className="text-lg font-bold text-gray-900">
                        {store.totalOrders.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">total orders</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 mb-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-900">Revenue</span>
                      </div>
                      <p className="text-lg font-bold text-gray-900">
                        ${store.monthlyRevenue.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">this month</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 mb-1">
                        <Bell className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-gray-900">Notifications</span>
                      </div>
                      <p className="text-lg font-bold text-gray-900">
                        {(store.metrics?.notificationsSent || 0).toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">sent</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 mb-1">
                        <Users className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-gray-900">Success Rate</span>
                      </div>
                      <p className="text-lg font-bold text-gray-900">
                        {store.metrics?.notificationSuccess || 0}%
                      </p>
                      <p className="text-xs text-gray-500">delivery</p>
                    </div>
                  </div>
                )}
                
                {store.connectedAt && (
                  <p className="text-xs text-gray-500 pt-2 border-t">
                    Connected on {new Date(store.connectedAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            </Card>
          ))
        )}
      </div>

      {/* OAuth Connection Modal - Portal Above Everything */}
      {showOAuthModal && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9990] p-4 overflow-y-auto"
          style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
        >
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto my-auto shadow-2xl">
            <div className="sticky top-0 bg-white p-4 border-b flex justify-between items-center z-10 rounded-t-lg">
              <h3 className="text-lg font-semibold">Connect Shopify Store</h3>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowOAuthModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </Button>
            </div>
            <div className="p-0 overflow-y-auto">
              <ShopifyConnectionForm 
                onSuccess={(storeData) => {
                  console.log('Store connected:', storeData);
                  setShowOAuthModal(false);
                  // The original code had refetch(), but useShopifyStores doesn't have a refetch option.
                  // For now, we'll just close the modal and rely on the next render to show the new data.
                }}
                className="border-0 shadow-none"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShopifyIntegrationPage; 