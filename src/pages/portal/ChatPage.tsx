import React from "react"
// import { AppFrame } from '../../components/whatsapp';
// import { ThemeProvider } from '../../components/ui/theme-provider';
import { ChatLayout } from "@/components/chat"
import { PortalFloatingNavbar } from "@/components/ui/floating-navbar"

const ChatPage: React.FC = () => {
  return (
    <>
      <div className="h-screen bg-[#0b141a] flex flex-col overflow-hidden">
        {/* Main Content - Same pattern as PortalLayout */}
        <main className="flex-1 overflow-auto pb-28 ">
          <div className="h-full">
            <ChatLayout />
          </div>
        </main>
      </div>

      {/* Floating Navigation - Outside Layout Container, same as PortalLayout */}
      <PortalFloatingNavbar
        variant={"whatsapp"}
        badgeCounts={{
          conversations: 12, // This would come from real-time data
          shopifyIntegration: 3, // Shopify webhook notifications
          whatsappConnections: 1 // WhatsApp connection issues
        }}
      />
    </>
  )
}

export default ChatPage
