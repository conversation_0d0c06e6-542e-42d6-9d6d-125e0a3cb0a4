import React, { useState, useEffect, useMemo } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  MessageCircle,
  AlertCircle,
  MoreHorizontal,
  DollarSign,
  TrendingUp,
  Star,
  Activity,
  Heart,
  Zap
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useCRM } from '@/hooks/useCRM';
import { useAuth } from '@/hooks/useAuth';
import { Customer } from '@/lib/graphql/queries/crmQueries';
import { CreateCustomerInput } from '@/lib/graphql/mutations/crmMutations';
import { StartConversationDialog } from '@/components/chat/outbound/StartConversationDialog';
import { CreateCustomerModal } from '@/components/crm/CreateCustomerModal';
import { PhoneUtils } from '@/lib/utils';
import { parseLastMessageContent } from '@/utils/messageContentParser';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


// Utility function to safely handle customer tags
const getCustomerTags = (customer: Customer): string[] => {
  return customer.tags || [];
};

export const CRMPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { 
    customers, 
    error, 
    totalCount, 
    searchCustomers, 
    clearFilters,
    createNewCustomer
  } = useCRM();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'archived'>('all');
  const [filterValue, setFilterValue] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [showCreateCustomer, setShowCreateCustomer] = useState(false);
  const [showStartConversation, setShowStartConversation] = useState(false);

  // Debug auth state
  useEffect(() => {
    console.log('🔍 CRM Page: Auth state debug:', {
      isAuthenticated,
      authLoading,
      user: user ? {
        userId: user.userId,
        organizationId: user.organizationId,
        organizationRole: user.organizationRole,
        email: user.email
      } : null
    });
  }, [isAuthenticated, authLoading, user]);

  // Advanced analytics calculations
  const analytics = useMemo(() => {
    const activeCustomers = customers.filter(c => c.conversation_status === 'active');
    const shopifyLinkedCustomers = customers.filter(c => c.shopify_customer_id);
    const totalRevenue = customers.reduce((sum, c) => sum + (c.shopify_total_spent || 0), 0);
    const totalOrders = customers.reduce((sum, c) => sum + (c.shopify_orders_count || 0), 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const highValueCustomers = customers.filter(c => (c.shopify_total_spent || 0) > 500).length;
    const recentlyActive = customers.filter(c => {
      if (!c.last_message_timestamp) return false;
      const daysSinceLastMessage = (Date.now() - new Date(c.last_message_timestamp).getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceLastMessage <= 7;
    }).length;

    return {
      activeCustomers: activeCustomers.length,
      shopifyLinked: shopifyLinkedCustomers.length,
      totalRevenue,
      avgOrderValue,
      highValueCustomers,
      recentlyActive,
      responseRate: 92 // This would come from actual analytics
    };
  }, [customers]);

  // Show auth loading state
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          <p className="mt-4 text-muted-foreground">Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Show organization ID missing error
  if (isAuthenticated && !user?.organizationId) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="p-8 text-center">
          <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">Organization Setup Required</h2>
          <p className="text-muted-foreground mb-6">
            Your account is not associated with an organization. This may be because:
          </p>
          <ul className="text-left max-w-md mx-auto mb-6 space-y-2">
            <li>• Your registration process is still completing</li>
            <li>• You need to be invited to an organization</li>
            <li>• There was an issue during account setup</li>
          </ul>
          <div className="flex gap-4 justify-center">
            <Button onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
            <Button variant="outline" onClick={() => navigate('/auth/login')}>
              Re-login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      searchCustomers(value);
    } else {
      clearFilters();
    }
  };

  // Filter customers by status and value
  const filteredCustomers = customers.filter(customer => {
    if (filterStatus !== 'all' && customer.conversation_status !== filterStatus) return false;
    
    if (filterValue !== 'all') {
      const customerValue = customer.shopify_total_spent || 0;
      if (filterValue === 'high' && customerValue <= 500) return false;
      if (filterValue === 'medium' && (customerValue <= 100 || customerValue > 500)) return false;
      if (filterValue === 'low' && customerValue > 100) return false;
    }
    
    return true;
  });

  // Start conversation with customer
  const handleStartConversation = (customer: Customer, businessPhone: string) => {
    const conversationId = `${customer.organization_id}#${businessPhone}#${customer.customer_phone}`;
    
    toast({
      title: "Starting conversation",
      description: `Opening chat with ${customer.customer_name}`,
    });

    navigate(`/portal/chat?conversation=${encodeURIComponent(conversationId)}&customer=${encodeURIComponent(customer.customer_name || '')}`);
  };

  // Handle create customer
  const handleCreateCustomer = async (input: CreateCustomerInput) => {
    try {
      await createNewCustomer(input);
      toast({
        title: "Success",
        description: "Customer created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create customer. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="p-8 text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">CRM Data Loading Error</h2>
          <p className="text-muted-foreground mb-4">
            We encountered an issue loading your customer data:
          </p>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="font-mono text-sm text-red-800">{error}</p>
          </div>
          {error.includes('Organization ID not found') && (
            <div className="text-left max-w-md mx-auto mb-6">
              <p className="font-medium mb-2">Common solutions:</p>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Wait a few minutes for organization setup to complete</li>
                <li>• Try logging out and logging back in</li>
                <li>• Contact support if the issue persists</li>
              </ul>
            </div>
          )}
          <div className="flex gap-4 justify-center">
            <Button onClick={() => window.location.reload()}>
              Retry
            </Button>
            <Button variant="outline" onClick={() => navigate('/auth/login')}>
              Re-login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Customer Management</h2>
          <p className="text-gray-600 mt-1">
            Manage your customers and their communication history
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button 
            onClick={() => setShowCreateCustomer(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Customer
          </Button>
          <Button 
            variant="outline"
            onClick={() => setShowStartConversation(true)}
            className="flex items-center gap-2"
          >
            <MessageCircle className="h-4 w-4" />
            Start Conversation
          </Button>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold">{totalCount}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-green-600 flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                +12% this month
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Conversations</p>
                <p className="text-2xl font-bold">{analytics.activeCustomers}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-green-600 flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {analytics.responseRate}% response rate
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold">${analytics.totalRevenue.toFixed(0)}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-600">Avg: ${analytics.avgOrderValue.toFixed(0)}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">High Value</p>
                <p className="text-2xl font-bold">{analytics.highValueCustomers}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-xs text-green-600 flex items-center gap-1">
                <Zap className="h-3 w-3" />
                {analytics.recentlyActive} active this week
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, phone, tags, or Shopify ID..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <Select value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-gray-400" />
              <Select value={filterValue} onValueChange={(value: any) => setFilterValue(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Value</SelectItem>
                  <SelectItem value="high">High ($500+)</SelectItem>
                  <SelectItem value="medium">Medium ($100-$500)</SelectItem>
                  <SelectItem value="low">Low ($0-$100)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </Card>

      {/* Customer Table */}
      <Card className="overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Customer</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>E-commerce</TableHead>
              <TableHead>Messages</TableHead>
              <TableHead>Last Contact</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCustomers.map((customer) => (
              <TableRow key={customer.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                        {(customer.customer_name || 'U').charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{customer.customer_name}</div>
                      <div className="text-sm text-muted-foreground">{PhoneUtils.formatForDisplay(customer.customer_phone)}</div>
                      {getCustomerTags(customer).length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getCustomerTags(customer).slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {getCustomerTags(customer).length > 2 && (
                            <Badge variant="outline" className="text-xs">+{getCustomerTags(customer).length - 2}</Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={customer.conversation_status === 'active' ? 'default' : 'secondary'}>
                    {customer.conversation_status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">${(customer.shopify_total_spent || 0).toFixed(0)}</span>
                    <span className="text-xs text-muted-foreground">{customer.shopify_orders_count || 0} orders</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{customer.message_count}</span>
                    <span className="text-xs text-muted-foreground">messages</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className="text-sm">{customer.last_message_timestamp ? new Date(customer.last_message_timestamp).toLocaleDateString() : 'N/A'}</span>
                    <span className="text-xs text-muted-foreground line-clamp-1">{parseLastMessageContent(customer.last_message_content)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleStartConversation(customer, customer.display_phone_number || '')}>
                        <MessageCircle className="mr-2 h-4 w-4" />
                        Start Chat
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Create Customer Modal */}
      <CreateCustomerModal
        isOpen={showCreateCustomer}
        onClose={() => setShowCreateCustomer(false)}
        onCreateCustomer={handleCreateCustomer}
      />

      {/* Start Conversation Dialog */}
      <StartConversationDialog
        isOpen={showStartConversation}
        onClose={() => setShowStartConversation(false)}
        onStartConversation={handleStartConversation}
      />
    </div>
  );
}; 