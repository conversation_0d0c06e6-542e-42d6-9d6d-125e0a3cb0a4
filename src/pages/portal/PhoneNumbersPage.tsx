// @ts-nocheck
// WhatsApp Phone Numbers Management Page

import React, { useState } from 'react';
import { 
  Phone, 
  Plus, 
  Settings, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  QrCode,
  MessageSquare,
  Users,
  BarChart3
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useWhatsAppConnections } from '@/hooks/useGraphQL';

export const PhoneNumbersPage: React.FC = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const { data: connections, loading, refetch } = useWhatsAppConnections();

  // Mock data for demo purposes
  const mockConnections = [
    {
      id: '1',
      phoneNumber: '+1234567890',
      businessName: 'Tech Solutions Inc',
      status: 'ACTIVE',
      verifiedAt: '2024-01-15T10:30:00Z',
      totalConversations: 45,
      totalMessages: 312,
      responseRate: 92,
      isVerified: true,
    },
    {
      id: '2',
      phoneNumber: '+1234567891',
      businessName: 'Marketing Agency Pro',
      status: 'PENDING',
      verifiedAt: null,
      totalConversations: 0,
      totalMessages: 0,
      responseRate: 0,
      isVerified: false,
    },
  ];

  const displayConnections = connections || mockConnections;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'DISCONNECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'DISCONNECTED':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleAddNumber = () => {
    console.log('Starting WhatsApp embedded signup flow...');
    // This would trigger the embedded signup flow
    setShowAddModal(true);
  };

  const handleVerifyNumber = (phoneNumber: string) => {
    console.log('Verifying number:', phoneNumber);
    // This would trigger verification process
  };

  const handleDisconnectNumber = (phoneNumber: string) => {
    console.log('Disconnecting number:', phoneNumber);
    // This would disconnect the WhatsApp Business API
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">WhatsApp Phone Numbers</h1>
          <p className="text-gray-600 mt-1">Manage your WhatsApp Business API phone numbers</p>
        </div>
        <Button onClick={handleAddNumber} className="mt-4 sm:mt-0">
          <Plus className="h-4 w-4 mr-2" />
          Add Phone Number
        </Button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Phone className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Numbers</p>
              <p className="text-2xl font-bold text-gray-900">{displayConnections.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-50 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Active Numbers</p>
              <p className="text-2xl font-bold text-gray-900">
                {displayConnections.filter(c => c.status === 'ACTIVE').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-50 rounded-lg">
              <MessageSquare className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Messages</p>
              <p className="text-2xl font-bold text-gray-900">
                {displayConnections.reduce((acc, c) => acc + c.totalMessages, 0)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <BarChart3 className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Response Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {displayConnections.length > 0 
                  ? Math.round(displayConnections.reduce((acc, c) => acc + c.responseRate, 0) / displayConnections.length)
                  : 0
                }%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Phone Numbers List */}
      <div className="space-y-4">
        {loading ? (
          <Card className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading phone numbers...</p>
          </Card>
        ) : displayConnections.length === 0 ? (
          <Card className="p-8 text-center">
            <Phone className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No phone numbers connected</h3>
            <p className="text-gray-600 mb-6">Get started by adding your first WhatsApp Business phone number</p>
            <Button onClick={handleAddNumber}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Number
            </Button>
          </Card>
        ) : (
          displayConnections.map((connection) => (
            <Card key={connection.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <Phone className="h-6 w-6 text-gray-600" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {connection.phoneNumber}
                      </h3>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(connection.status)}
                        <Badge className={getStatusColor(connection.status)}>
                          {connection.status}
                        </Badge>
                      </div>
                      {connection.isVerified && (
                        <Badge className="bg-blue-100 text-blue-800">
                          Verified
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{connection.businessName}</p>
                    
                    {connection.status === 'ACTIVE' && (
                      <div className="grid grid-cols-3 gap-6">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {connection.totalConversations} conversations
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MessageSquare className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {connection.totalMessages} messages
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <BarChart3 className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {connection.responseRate}% response rate
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {connection.verifiedAt && (
                      <p className="text-xs text-gray-500 mt-2">
                        Verified on {new Date(connection.verifiedAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {connection.status === 'PENDING' && (
                    <>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleVerifyNumber(connection.phoneNumber)}
                      >
                        <QrCode className="h-4 w-4 mr-2" />
                        Verify
                      </Button>
                    </>
                  )}
                  
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDisconnectNumber(connection.phoneNumber)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Setup Guide */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-start space-x-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <QrCode className="h-6 w-6 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              How to add a WhatsApp Business number
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Click "Add Phone Number" to start the embedded signup flow</p>
              <p>2. Follow the WhatsApp Business API verification process</p>
              <p>3. Configure your business profile and webhook settings</p>
              <p>4. Start receiving and sending messages through Notefy</p>
            </div>
            <Button variant="outline" className="mt-4" size="sm">
              View Setup Guide
            </Button>
          </div>
        </div>
      </Card>

      {/* Add Number Modal Placeholder */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <Card className="max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Add WhatsApp Phone Number
            </h3>
            <p className="text-gray-600 mb-6">
              The embedded signup flow will be integrated here to connect your WhatsApp Business API account.
            </p>
            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                Start Setup
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}; 