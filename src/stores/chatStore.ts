// Zustand Store for WhatsApp-style Chat State Management

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Chat, Message, ChatState, TypingIndicator } from '../types/whatsapp-chat';
import { realChatService } from '../services/realChatService';
import { mockChatService } from '../services/mockChatService';
import { executeSubscription } from '../lib/graphql-client';
import { ON_MESSAGE_RECEIVED, ON_MESSAGE_STATUS_UPDATED } from '../lib/graphql/queries/chat';
import { getCurrentUser } from 'aws-amplify/auth';

interface ChatActions {
  // Chat management
  loadChats: () => Promise<void>;
  selectChat: (chatId: string) => void;
  clearSelection: () => void;
  
  // Message management
  loadMessages: (chatId: string) => Promise<void>;
  sendTextMessage: (chatId: string, text: string) => Promise<void>;
  sendVoiceMessage: (chatId: string, audioBlob: Blob, duration: number) => Promise<void>;
  markMessageAsRead: (chatId: string, messageId: string) => Promise<void>;
  
  // Typing indicators
  setTyping: (chatId: string, isTyping: boolean) => void;
  
  // Voice recording
  startRecording: (chatId: string) => void;
  stopRecording: () => void;
  
  // Search
  setSearchQuery: (query: string) => void;
  
  // Theme
  setTheme: (theme: 'light' | 'dark') => void;

  // Real-time subscriptions
  subscribeToMessages: () => void;
  unsubscribeFromMessages: () => void;

  // UI state
  loading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type ChatStore = ChatState & ChatActions;

export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => {
      let messageSubscription: { unsubscribe: () => void } | null = null;
      let statusSubscription: { unsubscribe: () => void } | null = null;
      let organizationId: string | null = null;

      return {
        // Initial state
        chats: [],
        messages: {},
        currentChatId: null,
        typingIndicators: {},
        isRecording: false,
        recordingChatId: null,
        searchQuery: '',
        theme: 'light',
        loading: false,
        error: null,

      // Actions
      loadChats: async () => {
        try {
          set({ loading: true, error: null });
          // Try real API first, fallback to mock if needed
          const chats = await realChatService.listChats();
          set({ chats, loading: false });
        } catch (error) {
          console.warn('Real API failed, falling back to mock data:', error);
          try {
            const chats = await mockChatService.listChats();
            set({ chats, loading: false });
          } catch (mockError) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load chats',
              loading: false
            });
          }
        }
      },

      selectChat: (chatId: string) => {
        const { loadMessages } = get();
        set({ currentChatId: chatId });
        loadMessages(chatId);
      },

      clearSelection: () => {
        set({ currentChatId: null });
      },

      loadMessages: async (chatId: string) => {
        try {
          set({ loading: true, error: null });
          // Try real API first, fallback to mock if needed
          const messages = await realChatService.getMessages(chatId);
          set(state => ({
            messages: {
              ...state.messages,
              [chatId]: messages
            },
            loading: false
          }));
        } catch (error) {
          console.warn('Real API failed, falling back to mock data:', error);
          try {
            const messages = await mockChatService.getMessages(chatId);
            set(state => ({
              messages: {
                ...state.messages,
                [chatId]: messages
              },
              loading: false
            }));
          } catch (mockError) {
            set({
              error: error instanceof Error ? error.message : 'Failed to load messages',
              loading: false
            });
          }
        }
      },

      sendTextMessage: async (chatId: string, text: string) => {
        try {
          // Try real API first, fallback to mock if needed
          const message = await realChatService.sendText(chatId, text);

          // Add message optimistically
          set(state => ({
            messages: {
              ...state.messages,
              [chatId]: [...(state.messages[chatId] || []), message]
            }
          }));

          // Update chat's last message and timestamp
          set(state => ({
            chats: state.chats.map(chat =>
              chat.id === chatId
                ? { ...chat, lastMessage: message, updatedAt: message.timestamp }
                : chat
            )
          }));
        } catch (error) {
          console.warn('Real API failed, falling back to mock:', error);
          try {
            const message = await mockChatService.sendText(chatId, text);

            // Add message optimistically
            set(state => ({
              messages: {
                ...state.messages,
                [chatId]: [...(state.messages[chatId] || []), message]
              }
            }));

            // Update chat's last message and timestamp
            set(state => ({
              chats: state.chats.map(chat =>
                chat.id === chatId
                  ? { ...chat, lastMessage: message, updatedAt: message.timestamp }
                  : chat
              )
            }));
          } catch (mockError) {
            set({
              error: error instanceof Error ? error.message : 'Failed to send message'
            });
          }
        }
      },

      sendVoiceMessage: async (chatId: string, audioBlob: Blob, duration: number) => {
        try {
          // Try real API first, fallback to mock if needed
          const message = await realChatService.sendVoice(chatId, audioBlob, duration);

          // Add message optimistically
          set(state => ({
            messages: {
              ...state.messages,
              [chatId]: [...(state.messages[chatId] || []), message]
            }
          }));

          // Update chat's last message and timestamp
          set(state => ({
            chats: state.chats.map(chat =>
              chat.id === chatId
                ? { ...chat, lastMessage: message, updatedAt: message.timestamp }
                : chat
            )
          }));
        } catch (error) {
          console.warn('Real API failed, falling back to mock:', error);
          try {
            const message = await mockChatService.sendVoice(chatId, audioBlob, duration);

            // Add message optimistically
            set(state => ({
              messages: {
                ...state.messages,
                [chatId]: [...(state.messages[chatId] || []), message]
              }
            }));

            // Update chat's last message and timestamp
            set(state => ({
              chats: state.chats.map(chat =>
                chat.id === chatId
                  ? { ...chat, lastMessage: message, updatedAt: message.timestamp }
                  : chat
              )
            }));
          } catch (mockError) {
            set({
              error: error instanceof Error ? error.message : 'Failed to send voice message'
            });
          }
        }
      },

      markMessageAsRead: async (chatId: string, messageId: string) => {
        try {
          // Try real API first, fallback to mock if needed
          await realChatService.markAsRead(chatId, messageId);

          // Update message status locally
          set(state => ({
            messages: {
              ...state.messages,
              [chatId]: state.messages[chatId]?.map(msg =>
                msg.id === messageId ? { ...msg, status: 'read' } : msg
              ) || []
            }
          }));
        } catch (error) {
          console.warn('Real API failed, falling back to mock:', error);
          try {
            await mockChatService.markAsRead(chatId, messageId);

            // Update message status locally
            set(state => ({
              messages: {
                ...state.messages,
                [chatId]: state.messages[chatId]?.map(msg =>
                  msg.id === messageId ? { ...msg, status: 'read' } : msg
                ) || []
              }
            }));
          } catch (mockError) {
            console.error('Failed to mark message as read:', mockError);
          }
        }
      },

      setTyping: (chatId: string, isTyping: boolean) => {
        set(state => ({
          typingIndicators: {
            ...state.typingIndicators,
            [chatId]: {
              chatId,
              isTyping,
              timestamp: new Date()
            }
          }
        }));

        // Clear typing indicator after 3 seconds
        if (isTyping) {
          setTimeout(() => {
            set(state => ({
              typingIndicators: {
                ...state.typingIndicators,
                [chatId]: {
                  ...state.typingIndicators[chatId],
                  isTyping: false
                }
              }
            }));
          }, 3000);
        }

        // Notify service (try real API first, fallback to mock)
        try {
          realChatService.setTyping(chatId, isTyping);
        } catch (error) {
          console.warn('Real API failed, falling back to mock:', error);
          mockChatService.setTyping(chatId, isTyping);
        }
      },

      startRecording: (chatId: string) => {
        set({ isRecording: true, recordingChatId: chatId });
      },

      stopRecording: () => {
        set({ isRecording: false, recordingChatId: null });
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },

      setTheme: (theme: 'light' | 'dark') => {
        set({ theme });
        // Update document class for theme switching
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(theme);
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Real-time subscriptions
      subscribeToMessages: async () => {
        try {
          // Get organization ID from current user
          const user = await getCurrentUser();
          organizationId = user.attributes?.['custom:organizationId'] || 'default-org';

          // Subscribe to new messages
          messageSubscription = executeSubscription(
            {
              query: ON_MESSAGE_RECEIVED,
              variables: { organizationId }
            },
            {
              next: (data: any) => {
                const messageData = data.onMessageReceived;
                if (messageData) {
                  const newMessage: Message = {
                    id: messageData.messageId,
                    chatId: messageData.conversationId,
                    content: messageData.content,
                    type: messageData.messageType === 'audio' ? 'voice' : 'text',
                    isOutbound: messageData.direction === 'outbound',
                    status: 'delivered',
                    timestamp: new Date(messageData.timestamp),
                  };

                  // Add message to store
                  set(state => ({
                    messages: {
                      ...state.messages,
                      [messageData.conversationId]: [
                        ...(state.messages[messageData.conversationId] || []),
                        newMessage
                      ]
                    }
                  }));

                  // Update chat's last message
                  set(state => ({
                    chats: state.chats.map(chat =>
                      chat.id === messageData.conversationId
                        ? {
                            ...chat,
                            lastMessage: newMessage,
                            updatedAt: newMessage.timestamp,
                            unreadCount: messageData.direction === 'inbound' ? chat.unreadCount + 1 : chat.unreadCount
                          }
                        : chat
                    )
                  }));
                }
              },
              error: (errors: string[]) => {
                console.error('Message subscription error:', errors);
              }
            }
          );

          // Subscribe to message status updates
          statusSubscription = executeSubscription(
            {
              query: ON_MESSAGE_STATUS_UPDATED,
              variables: { organizationId }
            },
            {
              next: (data: any) => {
                const statusData = data.onMessageStatusUpdated;
                if (statusData) {
                  // Update message status in store
                  set(state => ({
                    messages: {
                      ...state.messages,
                      [statusData.conversationId]: state.messages[statusData.conversationId]?.map(msg =>
                        msg.id === statusData.messageId
                          ? { ...msg, status: statusData.status }
                          : msg
                      ) || []
                    }
                  }));
                }
              },
              error: (errors: string[]) => {
                console.error('Status subscription error:', errors);
              }
            }
          );
        } catch (error) {
          console.error('Failed to setup subscriptions:', error);
        }
      },

      unsubscribeFromMessages: () => {
        if (messageSubscription) {
          messageSubscription.unsubscribe();
          messageSubscription = null;
        }
        if (statusSubscription) {
          statusSubscription.unsubscribe();
          statusSubscription = null;
        }
      },
    };
  },
  {
    name: 'chat-store',
  }
  )
);

// Selectors for computed values
export const useFilteredChats = () => {
  const { chats, searchQuery } = useChatStore();
  
  if (!searchQuery.trim()) return chats;
  
  const query = searchQuery.toLowerCase();
  return chats.filter(chat => 
    chat.name.toLowerCase().includes(query) ||
    chat.lastMessage?.content.toLowerCase().includes(query)
  );
};

export const useCurrentChat = () => {
  const { chats, currentChatId } = useChatStore();
  return chats.find(chat => chat.id === currentChatId) || null;
};

export const useCurrentMessages = () => {
  const { messages, currentChatId } = useChatStore();
  return currentChatId ? messages[currentChatId] || [] : [];
};

export const useIsTyping = (chatId: string) => {
  const { typingIndicators } = useChatStore();
  return typingIndicators[chatId]?.isTyping || false;
};
