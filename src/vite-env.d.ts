/// <reference types="vite/client" />

// Extend Vite's ImportMeta interface to include our custom environment variables
interface ImportMetaEnv {
  // Environment Configuration
  readonly VITE_APP_NAME: string;
  readonly VITE_ENVIRONMENT: string;
  readonly VITE_API_VERSION: string;
  readonly VITE_AWS_REGION: string;
  readonly VITE_AWS_DEFAULT_REGION: string;
  
  // AWS Cognito Configuration
  readonly VITE_USER_POOL_ID: string;
  readonly VITE_USER_POOL_CLIENT_ID: string;
  
  // AWS AppSync Configuration
  readonly VITE_GRAPHQL_ENDPOINT: string;
  readonly VITE_GRAPHQL_REALTIME_ENDPOINT: string;
  readonly VITE_API_KEY: string;
  
  // AWS S3 Configuration
  readonly VITE_S3_BUCKET: string;
  
  // AWS Pinpoint Configuration
  readonly VITE_PINPOINT_APP_ID: string;
  
  // WhatsApp Configuration
  readonly VITE_WHATSAPP_PHONE_NUMBER_ID: string;
  readonly VITE_WHATSAPP_API_VERSION: string;
  readonly VITE_WHATSAPP_WEBHOOK_URL: string;
  
  // Facebook Configuration
  readonly VITE_FACEBOOK_APP_ID: string;
  
  // Feature Flags
  readonly VITE_FEATURE_WHATSAPP_INTEGRATION: string;
  readonly VITE_FEATURE_SHOPIFY_INTEGRATION: string;
  readonly VITE_FEATURE_REAL_TIME_CHAT: string;
  readonly VITE_FEATURE_TEAM_COLLABORATION: string;
  readonly VITE_FEATURE_ANALYTICS: string;
  readonly VITE_FEATURE_MULTI_TENANT: string;
  readonly VITE_FEATURE_DEBUG_MODE: string;
  readonly VITE_FEATURE_MOCK_DATA: string;
  readonly VITE_FEATURE_PERFORMANCE_MONITORING: string;
  
  // Vite built-in environment variables
  readonly DEV: boolean;
  readonly PROD: boolean;
  readonly MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
