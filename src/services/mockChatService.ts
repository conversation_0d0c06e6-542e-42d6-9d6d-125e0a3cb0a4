// Real Chat Service using GraphQL API
// Connects to AWS AppSync GraphQL backend

import { Chat, Message, MockChatService, VoiceMessageData } from '../types/whatsapp-chat';
import { StorageUtils } from '../lib/utils';
import { executeQuery, executeMutation, executeSubscription } from '../lib/graphql-client';
import {
  LIST_ORGANIZATION_CONVERSATIONS,
  GET_CONVERSATION_MESSAGES,
  GET_CONVERSATION
} from '../lib/graphql/queries/chat';
import { getCurrentUser } from 'aws-amplify/auth';

const STORAGE_KEYS = {
  CHATS: 'whatsapp_demo_chats',
  MESSAGES: 'whatsapp_demo_messages',
  LAST_MESSAGE_ID: 'whatsapp_demo_last_message_id',
} as const;

// Generate unique IDs
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Get next message ID
const getNextMessageId = (): string => {
  const lastId = StorageUtils.getItem<number>(STORAGE_KEYS.LAST_MESSAGE_ID) || 0;
  const nextId = lastId + 1;
  StorageUtils.setItem(STORAGE_KEYS.LAST_MESSAGE_ID, nextId);
  return `msg_${nextId}`;
};

// Mock data for initial seeding
const MOCK_CHATS: Chat[] = [
  {
    id: 'chat_1',
    name: 'Ahmed Hassan',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    unreadCount: 2,
    isOnline: true,
    isPinned: true,
    isMuted: false,
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T14:30:00Z'),
  },
  {
    id: 'chat_2',
    name: 'Sara Mohamed',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date('2024-01-15T12:00:00Z'),
    isPinned: false,
    isMuted: false,
    createdAt: new Date('2024-01-14T09:00:00Z'),
    updatedAt: new Date('2024-01-15T12:00:00Z'),
  },
  {
    id: 'chat_3',
    name: 'Omar Ali',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    unreadCount: 1,
    isOnline: true,
    isPinned: false,
    isMuted: true,
    createdAt: new Date('2024-01-13T16:00:00Z'),
    updatedAt: new Date('2024-01-15T13:45:00Z'),
  },
  {
    id: 'chat_4',
    name: 'Fatima Al-Zahra',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date('2024-01-14T18:30:00Z'),
    isPinned: false,
    isMuted: false,
    createdAt: new Date('2024-01-12T11:00:00Z'),
    updatedAt: new Date('2024-01-14T18:30:00Z'),
  },
];

const MOCK_MESSAGES: Record<string, Message[]> = {
  chat_1: [
    {
      id: 'msg_1',
      chatId: 'chat_1',
      content: 'مرحبا! كيف حالك؟',
      type: 'text',
      isOutbound: false,
      status: 'read',
      timestamp: new Date('2024-01-15T10:00:00Z'),
    },
    {
      id: 'msg_2',
      chatId: 'chat_1',
      content: 'أهلاً وسهلاً! الحمد لله بخير',
      type: 'text',
      isOutbound: true,
      status: 'read',
      timestamp: new Date('2024-01-15T10:01:00Z'),
    },
    {
      id: 'msg_3',
      chatId: 'chat_1',
      content: 'هل يمكنك مساعدتي في شيء؟',
      type: 'text',
      isOutbound: false,
      status: 'delivered',
      timestamp: new Date('2024-01-15T14:30:00Z'),
    },
  ],
  chat_2: [
    {
      id: 'msg_4',
      chatId: 'chat_2',
      content: 'Hello! How are you doing today?',
      type: 'text',
      isOutbound: false,
      status: 'read',
      timestamp: new Date('2024-01-15T11:00:00Z'),
    },
    {
      id: 'msg_5',
      chatId: 'chat_2',
      content: 'Hi Sara! I\'m doing great, thanks for asking 😊',
      type: 'text',
      isOutbound: true,
      status: 'read',
      timestamp: new Date('2024-01-15T11:01:00Z'),
    },
  ],
  chat_3: [
    {
      id: 'msg_6',
      chatId: 'chat_3',
      content: 'Voice message example',
      type: 'voice',
      isOutbound: false,
      status: 'delivered',
      timestamp: new Date('2024-01-15T13:45:00Z'),
      voiceData: {
        duration: 15,
        waveform: [0.2, 0.5, 0.8, 0.3, 0.6, 0.9, 0.4, 0.7, 0.2, 0.5],
      },
    },
  ],
  chat_4: [
    {
      id: 'msg_7',
      chatId: 'chat_4',
      content: 'Thank you for your help yesterday!',
      type: 'text',
      isOutbound: false,
      status: 'read',
      timestamp: new Date('2024-01-14T18:30:00Z'),
    },
  ],
};

class MockChatServiceImpl implements MockChatService {
  private initializeData(): void {
    // Initialize with mock data if not exists
    if (!StorageUtils.getItem(STORAGE_KEYS.CHATS)) {
      StorageUtils.setItem(STORAGE_KEYS.CHATS, MOCK_CHATS);
    }
    if (!StorageUtils.getItem(STORAGE_KEYS.MESSAGES)) {
      StorageUtils.setItem(STORAGE_KEYS.MESSAGES, MOCK_MESSAGES);
    }
  }

  async listChats(): Promise<Chat[]> {
    this.initializeData();
    const chats = StorageUtils.getItem<Chat[]>(STORAGE_KEYS.CHATS) || [];
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};

    // Update chats with last message info and ensure dates are proper Date objects
    return chats.map(chat => {
      const chatMessages = messages[chat.id] || [];
      const lastMessage = chatMessages[chatMessages.length - 1];

      // Convert date strings back to Date objects
      const processedChat = {
        ...chat,
        createdAt: new Date(chat.createdAt),
        updatedAt: new Date(chat.updatedAt),
        lastSeen: chat.lastSeen ? new Date(chat.lastSeen) : undefined,
        lastMessage: lastMessage ? {
          ...lastMessage,
          timestamp: new Date(lastMessage.timestamp)
        } : undefined,
      };

      if (lastMessage) {
        processedChat.updatedAt = new Date(lastMessage.timestamp);
      }

      return processedChat;
    }).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  async getMessages(chatId: string): Promise<Message[]> {
    this.initializeData();
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};
    const chatMessages = messages[chatId] || [];

    // Convert date strings back to Date objects
    return chatMessages.map(message => ({
      ...message,
      timestamp: new Date(message.timestamp)
    }));
  }

  async sendText(chatId: string, text: string): Promise<Message> {
    const message: Message = {
      id: getNextMessageId(),
      chatId,
      content: text,
      type: 'text',
      isOutbound: true,
      status: 'sending',
      timestamp: new Date(),
    };

    // Add to messages
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};
    if (!messages[chatId]) messages[chatId] = [];
    messages[chatId].push(message);
    StorageUtils.setItem(STORAGE_KEYS.MESSAGES, messages);

    // Update chat timestamp
    const chats = StorageUtils.getItem<Chat[]>(STORAGE_KEYS.CHATS) || [];
    const chatIndex = chats.findIndex(c => c.id === chatId);
    if (chatIndex !== -1) {
      chats[chatIndex].updatedAt = new Date();
      StorageUtils.setItem(STORAGE_KEYS.CHATS, chats);
    }

    // Simulate status updates
    setTimeout(() => this.updateMessageStatus(message.id, 'sent'), 500);
    setTimeout(() => this.updateMessageStatus(message.id, 'delivered'), 1000);
    setTimeout(() => this.updateMessageStatus(message.id, 'read'), 2000);

    return message;
  }

  async sendVoice(chatId: string, audioBlob: Blob, duration: number): Promise<Message> {
    const audioUrl = URL.createObjectURL(audioBlob);
    
    const message: Message = {
      id: getNextMessageId(),
      chatId,
      content: 'Voice message',
      type: 'voice',
      isOutbound: true,
      status: 'sending',
      timestamp: new Date(),
      voiceData: {
        duration,
        audioBlob,
        audioUrl,
        waveform: Array.from({ length: Math.ceil(duration) }, () => Math.random()),
      },
    };

    // Add to messages
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};
    if (!messages[chatId]) messages[chatId] = [];
    messages[chatId].push(message);
    StorageUtils.setItem(STORAGE_KEYS.MESSAGES, messages);

    // Update chat timestamp
    const chats = StorageUtils.getItem<Chat[]>(STORAGE_KEYS.CHATS) || [];
    const chatIndex = chats.findIndex(c => c.id === chatId);
    if (chatIndex !== -1) {
      chats[chatIndex].updatedAt = new Date();
      StorageUtils.setItem(STORAGE_KEYS.CHATS, chats);
    }

    // Simulate status updates
    setTimeout(() => this.updateMessageStatus(message.id, 'sent'), 500);
    setTimeout(() => this.updateMessageStatus(message.id, 'delivered'), 1000);

    return message;
  }

  async markAsRead(chatId: string, messageId: string): Promise<void> {
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};
    const chatMessages = messages[chatId] || [];
    const messageIndex = chatMessages.findIndex(m => m.id === messageId);
    
    if (messageIndex !== -1) {
      chatMessages[messageIndex].status = 'read';
      StorageUtils.setItem(STORAGE_KEYS.MESSAGES, messages);
    }
  }

  async setTyping(chatId: string, isTyping: boolean): Promise<void> {
    // This would typically send to a real-time service
    console.log(`User is ${isTyping ? 'typing' : 'not typing'} in chat ${chatId}`);
  }

  private updateMessageStatus(messageId: string, status: Message['status']): void {
    const messages = StorageUtils.getItem<Record<string, Message[]>>(STORAGE_KEYS.MESSAGES) || {};
    
    for (const chatId in messages) {
      const messageIndex = messages[chatId].findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        messages[chatId][messageIndex].status = status;
        StorageUtils.setItem(STORAGE_KEYS.MESSAGES, messages);
        break;
      }
    }
  }
}

export const mockChatService = new MockChatServiceImpl();
