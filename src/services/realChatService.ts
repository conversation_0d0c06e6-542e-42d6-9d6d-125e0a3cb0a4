// Real Chat Service using GraphQL API
// Connects to AWS AppSync GraphQL backend for real data

import { Chat, Message, MockChatService, VoiceMessageData } from '../types/whatsapp-chat';
import { executeQuery, executeMutation, executeSubscription } from '../lib/graphql-client';
import {
  LIST_ORGANIZATION_CONVERSATIONS,
  GET_CONVERSATION_MESSAGES,
  GET_CONVERSATION,
  SEND_TEXT_MESSAGE,
  SEND_MEDIA_MESSAGE,
  MARK_MESSAGE_READ
} from '../lib/graphql/queries/chat';
import { getCurrentUser } from 'aws-amplify/auth';

// GraphQL response types
interface ConversationItem {
  id: string;
  conversationId: string;
  organizationId: string;
  businessPhone: string;
  customerPhone: string;
  phoneNumberId: string;
  wabaId: string;
  customerName: string;
  customerProfilePicture?: string;
  status: string;
  assignedUserId?: string;
  messageCount: number;
  unreadCount: number;
  lastMessageAt: string;
  lastMessageContent?: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  shopifyCustomerId?: string;
  storeId?: string;
  storeName?: string;
}

interface MessageItem {
  id: string;
  conversationId: string;
  messageId: string;
  sortKey: string;
  organizationId: string;
  businessPhone: string;
  customerPhone: string;
  phoneNumberId: string;
  wabaId: string;
  fromNumber: string;
  contactName: string;
  messageType: string;
  direction: 'inbound' | 'outbound';
  status: string;
  content: string;
  textContent?: string;
  mediaUrl?: string;
  mediaType?: string;
  fileName?: string;
  caption?: string;
  latitude?: number;
  longitude?: number;
  locationName?: string;
  locationAddress?: string;
  templateName?: string;
  templateLanguage?: string;
  templateParameters?: any;
  timestamp: string;
  createdAt: string;
  updatedAt: string;
  deliveredAt?: string;
  readAt?: string;
  ttl?: number;
}

class RealChatService implements MockChatService {
  private organizationId: string | null = null;
  private currentUser: any = null;

  private async ensureAuthenticated() {
    if (!this.currentUser) {
      try {
        this.currentUser = await getCurrentUser();
        // Extract organization ID from user attributes or use a default
        this.organizationId = this.currentUser.attributes?.['custom:organizationId'] || 'default-org';
      } catch (error) {
        console.error('Authentication failed:', error);
        throw new Error('User not authenticated');
      }
    }
  }

  private convertConversationToChat(conversation: ConversationItem): Chat {
    return {
      id: conversation.conversationId,
      name: conversation.customerName || conversation.customerPhone,
      avatar: conversation.customerProfilePicture,
      unreadCount: conversation.unreadCount || 0,
      isOnline: Math.random() > 0.5, // Mock online status
      lastSeen: conversation.lastMessageAt ? new Date(conversation.lastMessageAt) : undefined,
      isPinned: false, // Could be derived from tags
      isMuted: conversation.tags?.includes('muted') || false,
      createdAt: new Date(conversation.createdAt),
      updatedAt: new Date(conversation.updatedAt),
      lastMessage: conversation.lastMessageContent ? {
        id: `last-${conversation.conversationId}`,
        chatId: conversation.conversationId,
        content: conversation.lastMessageContent,
        type: 'text' as const,
        isOutbound: false, // We'd need to determine this from the actual message
        status: 'read' as const,
        timestamp: new Date(conversation.lastMessageAt),
      } : undefined,
    };
  }

  private convertMessageItemToMessage(messageItem: MessageItem): Message {
    const isVoice = messageItem.messageType === 'audio' || messageItem.mediaType === 'audio';
    
    return {
      id: messageItem.messageId,
      chatId: messageItem.conversationId,
      content: messageItem.textContent || messageItem.content || '',
      type: isVoice ? 'voice' : (messageItem.mediaType ? 'image' : 'text'),
      isOutbound: messageItem.direction === 'outbound',
      status: this.mapMessageStatus(messageItem.status),
      timestamp: new Date(messageItem.timestamp),
      voiceData: isVoice ? {
        duration: 30, // Mock duration - would need to be stored in the message
        audioUrl: messageItem.mediaUrl,
        waveform: Array.from({ length: 20 }, () => Math.random()),
      } : undefined,
      isForwarded: false,
      isStarred: false,
    };
  }

  private mapMessageStatus(status: string): Message['status'] {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'sending':
        return 'sending';
      case 'sent':
        return 'sent';
      case 'delivered':
        return 'delivered';
      case 'read':
        return 'read';
      case 'failed':
        return 'failed';
      default:
        return 'sent';
    }
  }

  async listChats(): Promise<Chat[]> {
    try {
      await this.ensureAuthenticated();
      
      const response = await executeQuery({
        query: LIST_ORGANIZATION_CONVERSATIONS,
        variables: {
          organizationId: this.organizationId,
          limit: 50,
        },
      });

      if (response.errors) {
        console.error('GraphQL errors:', response.errors);
        throw new Error('Failed to fetch conversations');
      }

      const conversations = response.data?.listOrganizationConversations?.items || [];
      return conversations.map(conv => this.convertConversationToChat(conv));
    } catch (error) {
      console.error('Failed to load chats:', error);
      // Fallback to mock data if API fails
      return this.getMockChats();
    }
  }

  async getMessages(chatId: string): Promise<Message[]> {
    try {
      await this.ensureAuthenticated();
      
      const response = await executeQuery({
        query: GET_CONVERSATION_MESSAGES,
        variables: {
          conversationId: chatId,
          limit: 100,
        },
      });

      if (response.errors) {
        console.error('GraphQL errors:', response.errors);
        throw new Error('Failed to fetch messages');
      }

      const messages = response.data?.getConversationMessages?.items || [];
      return messages
        .map(msg => this.convertMessageItemToMessage(msg))
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    } catch (error) {
      console.error('Failed to load messages:', error);
      // Fallback to mock data if API fails
      return this.getMockMessages(chatId);
    }
  }

  async sendText(chatId: string, text: string): Promise<Message> {
    try {
      await this.ensureAuthenticated();

      // Create optimistic message
      const optimisticMessage: Message = {
        id: `temp-${Date.now()}`,
        chatId,
        content: text,
        type: 'text',
        isOutbound: true,
        status: 'sending',
        timestamp: new Date(),
      };

      // Send message via GraphQL mutation
      try {
        const response = await executeMutation({
          query: SEND_TEXT_MESSAGE,
          variables: {
            input: {
              conversationId: chatId,
              content: text,
              organizationId: this.organizationId,
            }
          }
        });

        if (response.errors) {
          console.error('Send message errors:', response.errors);
          // Return optimistic message with failed status
          return { ...optimisticMessage, status: 'failed' };
        }

        // Update optimistic message with real data
        const sentMessage = response.data?.sendTextMessage;
        if (sentMessage) {
          return {
            ...optimisticMessage,
            id: sentMessage.messageId,
            status: 'sent',
            timestamp: new Date(sentMessage.timestamp),
          };
        }
      } catch (mutationError) {
        console.error('Send message mutation failed:', mutationError);
        // Return optimistic message with failed status
        return { ...optimisticMessage, status: 'failed' };
      }

      return optimisticMessage;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  async sendVoice(chatId: string, audioBlob: Blob, duration: number): Promise<Message> {
    try {
      await this.ensureAuthenticated();
      
      // Create optimistic message
      const optimisticMessage: Message = {
        id: `temp-voice-${Date.now()}`,
        chatId,
        content: 'Voice message',
        type: 'voice',
        isOutbound: true,
        status: 'sending',
        timestamp: new Date(),
        voiceData: {
          duration,
          audioBlob,
          audioUrl: URL.createObjectURL(audioBlob),
          waveform: Array.from({ length: Math.ceil(duration) }, () => Math.random()),
        },
      };

      // TODO: Implement actual voice message upload and send
      // For now, simulate sending
      setTimeout(() => {
        // Update status to sent, delivered
      }, 1000);

      return optimisticMessage;
    } catch (error) {
      console.error('Failed to send voice message:', error);
      throw error;
    }
  }

  async markAsRead(chatId: string, messageId: string): Promise<void> {
    try {
      await this.ensureAuthenticated();

      const response = await executeMutation({
        query: MARK_MESSAGE_READ,
        variables: {
          input: {
            conversationId: chatId,
            messageId: messageId,
            organizationId: this.organizationId,
          }
        }
      });

      if (response.errors) {
        console.error('Mark as read errors:', response.errors);
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error);
    }
  }

  async setTyping(chatId: string, isTyping: boolean): Promise<void> {
    try {
      await this.ensureAuthenticated();
      // TODO: Implement typing indicator
      console.log(`Setting typing status to ${isTyping} for chat ${chatId}`);
    } catch (error) {
      console.error('Failed to set typing status:', error);
    }
  }

  // Fallback mock data methods
  private getMockChats(): Chat[] {
    return [
      {
        id: 'chat_1',
        name: 'Ahmed Hassan',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        unreadCount: 2,
        isOnline: true,
        isPinned: true,
        isMuted: false,
        createdAt: new Date('2024-01-15T10:00:00Z'),
        updatedAt: new Date('2024-01-15T14:30:00Z'),
      },
      {
        id: 'chat_2',
        name: 'Sara Mohamed',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        unreadCount: 0,
        isOnline: false,
        lastSeen: new Date('2024-01-15T12:00:00Z'),
        isPinned: false,
        isMuted: false,
        createdAt: new Date('2024-01-14T09:00:00Z'),
        updatedAt: new Date('2024-01-15T12:00:00Z'),
      },
    ];
  }

  private getMockMessages(chatId: string): Message[] {
    return [
      {
        id: 'msg_1',
        chatId,
        content: 'مرحبا! كيف حالك؟',
        type: 'text',
        isOutbound: false,
        status: 'read',
        timestamp: new Date('2024-01-15T10:00:00Z'),
      },
      {
        id: 'msg_2',
        chatId,
        content: 'أهلاً وسهلاً! الحمد لله بخير',
        type: 'text',
        isOutbound: true,
        status: 'read',
        timestamp: new Date('2024-01-15T10:01:00Z'),
      },
    ];
  }
}

export const realChatService = new RealChatService();
