import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

// Initialize AWS Amplify Configuration
import { configureAmplify } from './lib/aws-config'

import App from './App.tsx'

// Configure Amplify before rendering the app
const initializeApp = async () => {
  try {
    console.log('🚀 Initializing Notefy Application...');
    
    // Configure Amplify
    const configured = configureAmplify();
    
    if (!configured) {
      throw new Error('Failed to configure AWS Amplify');
    }
    
    console.log('✅ Amplify configured successfully');
    
    // Render the app
    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        <App />
      </StrictMode>,
    );
    
    console.log('✅ Application initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    
    // Show error to user
    const root = document.getElementById('root');
    if (root) {
      root.innerHTML = `
        <div style="
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background-color: #f8fafc;
        ">
          <div style="
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 400px;
          ">
            <h1 style="
              color: #dc2626;
              margin: 0 0 1rem 0;
              font-size: 1.5rem;
              font-weight: 600;
            ">
              Configuration Error
            </h1>
            <p style="
              color: #6b7280;
              margin: 0 0 1rem 0;
              line-height: 1.5;
            ">
              The application could not be initialized. Please check the console for details.
            </p>
            <button 
              onclick="window.location.reload()" 
              style="
                background: #3b82f6;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.875rem;
                font-weight: 500;
              "
            >
              Reload Page
            </button>
          </div>
        </div>
      `;
    }
  }
};

// Initialize the application
initializeApp();
