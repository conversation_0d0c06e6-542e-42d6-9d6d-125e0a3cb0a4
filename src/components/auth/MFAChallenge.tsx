// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Shield, Smartphone, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';

const mfaSchema = z.object({
  code: z.string().min(6, 'Code must be at least 6 characters').max(8, 'Code must be at most 8 characters'),
});

type MFAFormData = z.infer<typeof mfaSchema>;

interface MFAChallengeProps {
  challenge: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export const MFAChallenge: React.FC<MFAChallengeProps> = ({ challenge, onSuccess, onCancel }) => {
  const { confirmSignIn, isLoading } = useAuth();
  const [resendCooldown, setResendCooldown] = useState(0);

  const challengeName = challenge?.challengeName || 'SOFTWARE_TOKEN_MFA';

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<MFAFormData>({
    resolver: zodResolver(mfaSchema),
    mode: 'onChange',
  });

  const code = watch('code');

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const onSubmit = async (data: MFAFormData) => {
    try {
      const result = await confirmSignIn(data.code);
      if (result) {
        onSuccess();
      }
    } catch (error) {
      console.error('MFA confirmation failed:', error);
    }
  };

  // Auto-format code input
  const handleCodeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 8);
    setValue('code', value, { shouldValidate: true });
  };

  const getMFAInfo = () => {
    switch (challengeName) {
      case 'SMS_MFA':
        return {
          icon: <Smartphone className="h-6 w-6" />,
          title: 'SMS Verification',
          description: 'Enter the 6-digit code sent to your phone number',
          placeholder: 'Enter 6-digit SMS code',
          showResend: true,
        };
      case 'SOFTWARE_TOKEN_MFA':
        return {
          icon: <Shield className="h-6 w-6" />,
          title: 'Authenticator App',
          description: 'Enter the 6-digit code from your authenticator app',
          placeholder: 'Enter 6-digit code',
          showResend: false,
        };
      default:
        return {
          icon: <Shield className="h-6 w-6" />,
          title: 'Two-Factor Authentication',
          description: 'Enter your verification code',
          placeholder: 'Enter verification code',
          showResend: false,
        };
    }
  };

  const mfaInfo = getMFAInfo();

  const handleResendCode = async () => {
    if (challengeName === 'SMS_MFA' && resendCooldown === 0) {
      // In a real implementation, you would call AWS Cognito to resend SMS
      // For now, we'll just simulate the cooldown
      setResendCooldown(60);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto mb-4 h-16 w-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center text-white shadow-lg">
            {mfaInfo.icon}
          </div>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
            {mfaInfo.title}
          </CardTitle>
          <CardDescription className="text-slate-600 dark:text-slate-400">
            {mfaInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* MFA Code Field */}
            <div className="space-y-2">
              <Input
                {...register('code')}
                placeholder={mfaInfo.placeholder}
                onChange={handleCodeInput}
                value={code || ''}
                className="text-center text-lg tracking-widest font-mono"
                maxLength={8}
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 text-center">
                {challengeName === 'SMS_MFA' 
                  ? 'Check your phone for the SMS code'
                  : 'Open your authenticator app to get the code'
                }
              </p>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium shadow-lg transition-all duration-200 hover:shadow-xl disabled:opacity-50"
              disabled={isLoading || !isValid}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Verifying...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Verify Code</span>
                </div>
              )}
            </Button>
          </form>

          {/* Resend Code (SMS only) */}
          {mfaInfo.showResend && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={handleResendCode}
                disabled={resendCooldown > 0}
                className="w-full"
              >
                {resendCooldown > 0 ? (
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="w-4 h-4" />
                    <span>Resend in {resendCooldown}s</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <RefreshCw className="w-4 h-4" />
                    <span>Resend SMS code</span>
                  </div>
                )}
              </Button>
            </div>
          )}

          {/* Help Information */}
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-slate-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-slate-600 dark:text-slate-400">
                <p className="font-medium mb-1">Security verification</p>
                <p>
                  {challengeName === 'SMS_MFA'
                    ? 'This code was sent to your registered phone number and expires in 5 minutes.'
                    : 'This code is generated by your authenticator app and changes every 30 seconds.'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Cancel Button */}
          <div className="text-center pt-4">
            <Button
              variant="ghost"
              onClick={onCancel}
              className="text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200"
            >
              Cancel and try again
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 