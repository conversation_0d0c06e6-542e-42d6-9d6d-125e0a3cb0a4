// @ts-nocheck
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Building } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  description: string;
  backTo?: {
    path: string;
    label: string;
  };
  showLogo?: boolean;
}

const AuthLogo: React.FC = () => (
  <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg glow-primary mb-6">
    <span className="text-white text-2xl font-bold">N</span>
  </div>
);

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  description,
  backTo,
  showLogo = true,
}) => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-8">
              {showLogo && <AuthLogo />}
              <div className="space-y-2">
                <CardTitle className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  {title}
                </CardTitle>
                <CardDescription className="text-slate-600 leading-relaxed">
                  {description}
                </CardDescription>
              </div>
              
              {/* Back Navigation (if provided) */}
              {backTo && (
                <div className="flex justify-start mt-4">
                  <Link
                    to={backTo.path}
                    className="inline-flex items-center text-sm text-slate-600 hover:text-slate-900 transition-colors group"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
                    {backTo.label}
                  </Link>
                </div>
              )}
            </CardHeader>

            <CardContent className="space-y-6">
              {children}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}; 