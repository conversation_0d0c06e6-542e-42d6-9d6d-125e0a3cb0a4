// @ts-nocheck
import React from 'react';
import { CheckCircle, MessageCircle, ShoppingBag, Shield, Zap, Building, Timer, Webhook, FileText, Users, Star, Globe, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OnboardingStepWrapper } from './OnboardingLayout';
import { useAuth } from '@/hooks/useAuth';

// ============================================================================
// Welcome Step Component
// ============================================================================

export const WelcomeStep: React.FC = () => {
  const { user } = useAuth();

  return (
    <OnboardingStepWrapper
      stepId="welcome"
      title="Welcome to Notefy!"
      description="Your enterprise WhatsApp Business API platform is ready. Connect your Shopify store and start automating customer communications."
      showActions={false}
    >
      <div className="space-y-8">
        {/* Welcome Message */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-foreground">
                              Welcome to Notefy, {user?.businessName || user?.email || 'there'}!
            </h3>
            <p className="text-muted-foreground">
              Your enterprise WhatsApp Business API platform is ready. Connect your Shopify store 
              and start automating customer communications with sub-1ms message processing.
            </p>
          </div>
        </div>

        {/* Enterprise Features Banner */}
        <div className="bg-gradient-to-r from-purple-50 to-green-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Building className="h-8 w-8 text-purple-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">Enterprise-Grade WhatsApp Business Platform</h3>
              <p className="text-gray-600 mt-1">
                SOC 2 compliant • Sub-1ms processing • Multi-tenant architecture • Enterprise security
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Timer className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-800">Sub-1ms Processing</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">SOC 2 Compliant</span>
              </div>
            </div>
          </div>
        </div>

        {/* Core Platform Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border border-green-200 bg-green-50/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-green-900 mb-2">
                WhatsApp Business API
              </h4>
              <p className="text-sm text-green-700">
                Official WhatsApp Business API with embedded signup, business verification, and real-time messaging
              </p>
            </CardContent>
          </Card>

          <Card className="border border-blue-200 bg-blue-50/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShoppingBag className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-blue-900 mb-2">
                Shopify Integration
              </h4>
              <p className="text-sm text-blue-700">
                OAuth2 authentication, real-time webhooks, automated order notifications, and customer sync
              </p>
            </CardContent>
          </Card>

          <Card className="border border-purple-200 bg-purple-50/50">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-purple-900 mb-2">
                Real-time Processing
              </h4>
              <p className="text-sm text-purple-700">
                Sub-1ms message processing with 99.9% uptime and enterprise-grade infrastructure
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Technical Capabilities */}
        <div className="space-y-4">
          <h4 className="font-semibold text-foreground">
            Enterprise Platform Capabilities
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Webhook className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Real-time Webhooks</h5>
                  <p className="text-sm text-gray-700">Instant order notifications and updates</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Business Templates</h5>
                  <p className="text-sm text-gray-700">Pre-approved WhatsApp message templates</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Multi-tenant Architecture</h5>
                  <p className="text-sm text-gray-700">Isolated customer data and configurations</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <Activity className="h-4 w-4 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Real-time Analytics</h5>
                  <p className="text-sm text-gray-700">Message delivery rates and response times</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <Shield className="h-4 w-4 text-red-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Business Verification</h5>
                  <p className="text-sm text-gray-700">Embedded WhatsApp business verification</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Globe className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">Global Infrastructure</h5>
                  <p className="text-sm text-gray-700">AWS multi-region deployment</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Status */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            
            <div className="flex-1">
              <h4 className="font-semibold text-green-900 mb-1">
                Enterprise Account Activated
              </h4>
              <p className="text-sm text-green-700">
                Your Notefy enterprise account is verified and ready. You can now connect your Shopify store 
                and configure WhatsApp Business API integration.
              </p>
            </div>
            
            <div className="flex space-x-2">
              <Badge variant="default" className="bg-green-600 text-white">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified
              </Badge>
              <Badge variant="outline" className="border-purple-300 text-purple-700">
                <Building className="h-3 w-3 mr-1" />
                Enterprise
              </Badge>
            </div>
          </div>
        </div>

        {/* Setup Steps Preview */}
        <div className="space-y-4">
          <h4 className="font-semibold text-foreground">
            Setup Your Enterprise Platform
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-blue-800">1</span>
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-blue-900">Connect Shopify Store</h5>
                <p className="text-sm text-blue-700">OAuth2 authentication with real-time webhook configuration</p>
              </div>
              <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
                <Star className="h-3 w-3 mr-1" />
                Recommended
              </Badge>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-green-800">2</span>
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-green-900">WhatsApp Business API</h5>
                <p className="text-sm text-green-700">Embedded signup, business verification, and message templates</p>
              </div>
              <Badge variant="outline" className="text-xs border-green-300 text-green-700">
                <MessageCircle className="h-3 w-3 mr-1" />
                Core Feature
              </Badge>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-purple-800">3</span>
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-purple-900">Enterprise Configuration</h5>
                <p className="text-sm text-purple-700">Templates, webhooks, analytics, and notification settings</p>
              </div>
              <Badge variant="outline" className="text-xs border-purple-300 text-purple-700">
                <Building className="h-3 w-3 mr-1" />
                Enterprise
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </OnboardingStepWrapper>
  );
};

export default WelcomeStep; 