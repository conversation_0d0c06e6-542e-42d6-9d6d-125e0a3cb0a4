// @ts-nocheck
import React, { useState } from 'react';
import { ShoppingBag, Link, CheckCircle, AlertCircle, ArrowRight, ExternalLink, Shield, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OnboardingStepWrapper } from './OnboardingLayout';
import { useNotifications } from '@/components/ui/notification';

// ============================================================================
// Shopify Step Component
// ============================================================================

export const ShopifyStep: React.FC = () => {
  const notifications = useNotifications();
  const [shopifyUrl, setShopifyUrl] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState('');

  const handleConnectShopify = async () => {
    if (!shopifyUrl.trim()) {
      setConnectionError('Please enter your Shopify store URL');
      return;
    }

    // Validate URL format
    const urlPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]\.myshopify\.com$/;
    const domain = shopifyUrl.replace(/^https?:\/\//, '').replace(/\/$/, '');
    
    if (!urlPattern.test(domain)) {
      setConnectionError('Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)');
      return;
    }

    setIsConnecting(true);
    setConnectionError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For now, simulate successful connection
      setIsConnected(true);
      notifications.success('Store Connected!', `Successfully connected to ${domain}`);
    } catch (error) {
      setConnectionError('Failed to connect to your Shopify store. Please check the URL and try again.');
      notifications.error('Connection Failed', 'Unable to connect to your Shopify store');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setShopifyUrl('');
    notifications.success('Store Disconnected', 'Your Shopify store has been disconnected');
  };

  return (
    <OnboardingStepWrapper
      stepId="shopify"
      title="Connect Your Shopify Store"
      description="Link your Shopify store to enable automated order notifications and customer data synchronization."
    >
      <div className="space-y-8">
        {/* Connection Status */}
        {isConnected ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              
              <div className="flex-1">
                <h4 className="font-semibold text-green-900 mb-1">
                  Store Connected Successfully
                </h4>
                <p className="text-sm text-green-700">
                  Your Shopify store is now connected and ready to sync data with Notefy.
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Connected to: {shopifyUrl}
                </p>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDisconnect}
                className="text-green-700 border-green-300 hover:bg-green-100"
              >
                Disconnect
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Store URL Input */}
            <div className="space-y-4">
              <div>
                <label htmlFor="shopify-url" className="block text-sm font-medium text-foreground mb-2">
                  Shopify Store URL
                </label>
                <div className="relative">
                  <Input
                    id="shopify-url"
                    type="text"
                    placeholder="your-store.myshopify.com"
                    value={shopifyUrl}
                    onChange={(e) => {
                      setShopifyUrl(e.target.value);
                      setConnectionError('');
                    }}
                    className={connectionError ? 'border-red-300 focus:border-red-500' : ''}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                  </div>
                </div>
                {connectionError && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {connectionError}
                  </p>
                )}
              </div>

              <Button
                onClick={handleConnectShopify}
                disabled={isConnecting}
                className="w-full gap-2"
              >
                {isConnecting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Connecting...
                  </>
                ) : (
                  <>
                    <Link className="h-4 w-4" />
                    Connect Shopify Store
                  </>
                )}
              </Button>
            </div>

            {/* Help Text */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">
                How to find your Shopify store URL:
              </h4>
              <ol className="text-sm text-blue-700 space-y-1 ml-4">
                <li>1. Go to your Shopify admin dashboard</li>
                <li>2. Look for your store URL in the format: your-store.myshopify.com</li>
                <li>3. Copy and paste it into the field above</li>
              </ol>
            </div>
          </div>
        )}

        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="border border-orange-200 bg-orange-50/50">
            <CardContent className="p-6">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <Zap className="h-6 w-6 text-orange-600" />
              </div>
              <h4 className="font-semibold text-orange-900 mb-2">
                Automated Notifications
              </h4>
              <p className="text-sm text-orange-700">
                Send automatic WhatsApp messages for order confirmations, shipping updates, and delivery notifications.
              </p>
            </CardContent>
          </Card>

          <Card className="border border-purple-200 bg-purple-50/50">
            <CardContent className="p-6">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-purple-900 mb-2">
                Customer Data Sync
              </h4>
              <p className="text-sm text-purple-700">
                Automatically sync customer information and order history for personalized conversations.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Features Preview */}
        <div className="space-y-4">
          <h4 className="font-semibold text-foreground">
            What you'll get after connecting:
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-gray-600" />
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-foreground">Order Notifications</h5>
                <p className="text-sm text-muted-foreground">Automatic WhatsApp messages for new orders</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-gray-600" />
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-foreground">Customer Context</h5>
                <p className="text-sm text-muted-foreground">View customer order history during conversations</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-gray-600" />
              </div>
              <div className="flex-1">
                <h5 className="font-medium text-foreground">Product Catalog</h5>
                <p className="text-sm text-muted-foreground">Sync products for easy sharing in conversations</p>
              </div>
            </div>
          </div>
        </div>

        {/* Skip Option */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-900 mb-1">
                Skip for now?
              </h4>
              <p className="text-sm text-yellow-700">
                You can connect your Shopify store later from the dashboard settings. 
                This integration is optional but recommended for automated order notifications.
              </p>
            </div>
          </div>
        </div>
      </div>
    </OnboardingStepWrapper>
  );
};

export default ShopifyStep; 