// @ts-nocheck
// Mobile-First Onboarding Layout
// Enterprise-grade onboarding system optimized for mobile devices

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, X, SkipForward, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OnboardingStepIndicator } from '@/components/ui/onboarding-step-indicator';
import { OnboardingProgressBadge, OnboardingStep, createOnboardingSteps } from '@/components/ui/onboarding-progress-badge';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';
import { 
  ResponsiveGrid, 
  ResponsiveFlex, 
  ResponsiveStack, 
  ResponsiveShow, 
  ResponsiveSection,
  useResponsiveBreakpoint, 
  mobileFirst 
} from '@/components/ui/responsive-grid';
import { MobileCardLayout, MobileFormLayout } from '@/components/ui/page-layout';

// ============================================================================
// Types
// ============================================================================

interface OnboardingLayoutProps {
  children: React.ReactNode;
  currentStep: string;
  onStepChange: (stepId: string) => void;
  onComplete: () => void;
  onSkip: () => void;
  steps?: OnboardingStep[];
  showProgressBadge?: boolean;
  showStepIndicator?: boolean;
  allowSkip?: boolean;
  className?: string;
}

interface OnboardingContextType {
  steps: OnboardingStep[];
  currentStep: string;
  updateStep: (stepId: string, completed: boolean) => void;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  completeOnboarding: () => void;
  isComplete: boolean;
  isMobile: boolean;
}

// ============================================================================
// Context
// ============================================================================

const OnboardingContext = React.createContext<OnboardingContextType | undefined>(undefined);

export const useOnboarding = () => {
  const context = React.useContext(OnboardingContext);
  if (!context) {
    throw new Error('useOnboarding must be used within an OnboardingLayout');
  }
  return context;
};

// ============================================================================
// Mobile-First Header Component
// ============================================================================

const MobileOnboardingHeader: React.FC<{
  steps: OnboardingStep[];
  currentStep: string;
  onStepChange: (stepId: string) => void;
  onSkip: () => void;
  allowSkip: boolean;
  isMobile: boolean;
}> = ({ steps, currentStep, onStepChange, onSkip, allowSkip, isMobile }) => {
  const navigate = useNavigate();
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;

  return (
    <div className="space-y-4">
      {/* Mobile Header */}
      <ResponsiveShow below="md">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className={cn(mobileFirst.typography.h2, "text-foreground")}>
              Setup
            </h1>
            <Badge variant="secondary" className="text-xs">
              {currentStepIndex + 1} of {steps.length}
            </Badge>
          </div>
          
          <ResponsiveFlex gap="sm">
            {allowSkip && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onSkip}
                className="gap-2"
              >
                <SkipForward className="h-4 w-4" />
                Skip
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/portal/analytics')}
              className="gap-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </ResponsiveFlex>
        </div>
        
        {/* Mobile Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className={cn(mobileFirst.typography.small, "text-muted-foreground")}>
              {currentStepData?.name || 'Step'}
            </span>
            <span className={cn(mobileFirst.typography.small, "text-muted-foreground")}>
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </ResponsiveShow>

      {/* Desktop Header */}
      <ResponsiveShow above="md">
        <div className="text-center space-y-4">
          <h1 className={cn(mobileFirst.typography.h1, "text-foreground")}>
            Welcome to Notefy
          </h1>
          <p className={cn(mobileFirst.typography.body, "text-muted-foreground max-w-2xl mx-auto")}>
            Let's get you set up with your WhatsApp Business integration and Shopify store connection.
            You can skip any step and come back to it later.
          </p>
        </div>
      </ResponsiveShow>
    </div>
  );
};

// ============================================================================
// Mobile-First Step Navigation
// ============================================================================

const MobileStepNavigation: React.FC<{
  steps: OnboardingStep[];
  currentStep: string;
  onPreviousStep: () => void;
  onNextStep: () => void;
  onSkipStep: () => void;
  canGoBack: boolean;
  canGoNext: boolean;
  canSkip: boolean;
  isMobile: boolean;
}> = ({ 
  steps, 
  currentStep, 
  onPreviousStep, 
  onNextStep, 
  onSkipStep, 
  canGoBack, 
  canGoNext, 
  canSkip, 
  isMobile 
}) => {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];
  const isLastStep = currentStepIndex === steps.length - 1;

  return (
    <div className="space-y-4">
      {/* Mobile Navigation */}
      <ResponsiveShow below="md">
        <div className="flex items-center justify-between gap-4">
          <Button
            variant="outline"
            onClick={onPreviousStep}
            disabled={!canGoBack}
            className="gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Back
          </Button>
          
          <div className="flex items-center gap-2">
            {canSkip && currentStepData?.optional && (
              <Button
                variant="ghost"
                onClick={onSkipStep}
                className="gap-2"
              >
                Skip
              </Button>
            )}
            
            <Button
              onClick={onNextStep}
              disabled={!canGoNext}
              className="gap-2"
            >
              {isLastStep ? 'Complete' : 'Next'}
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </ResponsiveShow>

      {/* Desktop Navigation */}
      <ResponsiveShow above="md">
        <ResponsiveFlex justify="center" gap="md">
          {canGoBack && (
            <Button
              variant="outline"
              onClick={onPreviousStep}
              className="gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
          )}
          
          {canSkip && currentStepData?.optional && (
            <Button
              variant="outline"
              onClick={onSkipStep}
              className="gap-2"
            >
              <SkipForward className="h-4 w-4" />
              Skip Step
            </Button>
          )}
          
          <Button
            onClick={onNextStep}
            disabled={!canGoNext}
            className="gap-2"
          >
            {isLastStep ? 'Complete Setup' : 'Continue'}
            <ChevronRight className="h-4 w-4" />
          </Button>
        </ResponsiveFlex>
      </ResponsiveShow>
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

export const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({
  children,
  currentStep,
  onStepChange,
  onComplete,
  onSkip,
  steps: initialSteps,
  showProgressBadge = true,
  showStepIndicator = true,
  allowSkip = true,
  className,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isMobile } = useResponsiveBreakpoint();
  
  // Initialize steps with defaults
  const [steps, setSteps] = useState<OnboardingStep[]>(() => {
    return initialSteps || createOnboardingSteps([
      { id: 'welcome', completed: true }, // Auto-completed after signup
    ]);
  });

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];
  const isComplete = steps.every(step => step.completed || step.optional);

  // Update step completion
  const updateStep = (stepId: string, completed: boolean) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, completed } : step
    ));
  };

  // Navigation functions
  const nextStep = () => {
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < steps.length) {
      onStepChange(steps[nextIndex].id);
    } else {
      completeOnboarding();
    }
  };

  const previousStep = () => {
    const prevIndex = currentStepIndex - 1;
    if (prevIndex >= 0) {
      onStepChange(steps[prevIndex].id);
    }
  };

  const skipStep = () => {
    if (currentStepData?.optional) {
      nextStep();
    }
  };

  const completeOnboarding = () => {
    onComplete();
  };

  // Skip entire onboarding
  const skipOnboarding = () => {
    onSkip();
  };

  // Context value
  const contextValue: OnboardingContextType = {
    steps,
    currentStep,
    updateStep,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
    isComplete,
    isMobile,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      <div className={cn(
        "min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50",
        className
      )}>
        {/* Progress Badge */}
        {showProgressBadge && (
          <OnboardingProgressBadge
            steps={steps}
            currentStep={currentStep}
            onContinue={() => onStepChange(currentStep)}
            onSkip={skipOnboarding}
            isVisible={true}
            mobileOptimized={isMobile}
            hapticFeedback={true}
          />
        )}

        {/* Main Content */}
        <ResponsiveSection spacing="lg" containerSize="4xl">
          <ResponsiveStack spacing="lg">
            {/* Header */}
            <MobileOnboardingHeader
              steps={steps}
              currentStep={currentStep}
              onStepChange={onStepChange}
              onSkip={skipOnboarding}
              allowSkip={allowSkip}
              isMobile={isMobile}
            />

            {/* Step Indicator - Desktop Only */}
            {showStepIndicator && (
              <ResponsiveShow above="md">
                <OnboardingStepIndicator
                  steps={steps}
                  currentStep={currentStep}
                  onStepClick={onStepChange}
                  onNext={nextStep}
                  onPrevious={previousStep}
                  onSkip={skipStep}
                  allowStepJumping={true}
                  interactive={true}
                />
              </ResponsiveShow>
            )}

            {/* Step Content */}
            <MobileCardLayout
              padding="lg"
              shadow="lg"
              className="min-h-[400px] sm:min-h-[500px]"
            >
              {children}
            </MobileCardLayout>

            {/* Navigation */}
            <MobileStepNavigation
              steps={steps}
              currentStep={currentStep}
              onPreviousStep={previousStep}
              onNextStep={nextStep}
              onSkipStep={skipStep}
              canGoBack={currentStepIndex > 0}
              canGoNext={true}
              canSkip={allowSkip}
              isMobile={isMobile}
            />

            {/* Quick Actions - Desktop Only */}
            <ResponsiveShow above="md">
              <ResponsiveFlex justify="center" gap="md">
                {allowSkip && (
                  <Button
                    variant="outline"
                    onClick={skipOnboarding}
                    className="gap-2"
                  >
                    <SkipForward className="h-4 w-4" />
                    Skip Setup
                  </Button>
                )}
                
                <Button
                  onClick={() => navigate('/portal/analytics')}
                  variant="ghost"
                  className="gap-2"
                >
                  Go to Dashboard
                </Button>
              </ResponsiveFlex>
            </ResponsiveShow>
          </ResponsiveStack>
        </ResponsiveSection>
      </div>
    </OnboardingContext.Provider>
  );
};

// ============================================================================
// Mobile-First Step Wrapper
// ============================================================================

export interface OnboardingStepWrapperProps {
  stepId: string;
  title: string;
  description: string;
  children: React.ReactNode;
  onComplete?: () => void;
  onSkip?: () => void;
  showActions?: boolean;
  className?: string;
}

export const OnboardingStepWrapper: React.FC<OnboardingStepWrapperProps> = ({
  stepId,
  title,
  description,
  children,
  onComplete,
  onSkip,
  showActions = true,
  className,
}) => {
  const { 
    steps, 
    updateStep, 
    nextStep, 
    skipStep, 
    isMobile 
  } = useOnboarding();
  
  const currentStepData = steps.find(step => step.id === stepId);

  const handleComplete = () => {
    updateStep(stepId, true);
    if (onComplete) {
      onComplete();
    } else {
      nextStep();
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      skipStep();
    }
  };

  return (
    <MobileFormLayout
      title={title}
      description={description}
      actions={showActions ? (
        <ResponsiveFlex direction="responsive" gap="sm">
          {currentStepData?.optional && (
            <Button
              variant="outline"
              onClick={handleSkip}
              className="gap-2"
            >
              <SkipForward className="h-4 w-4" />
              Skip
            </Button>
          )}
          
          <Button
            onClick={handleComplete}
            className="gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            Complete
          </Button>
        </ResponsiveFlex>
      ) : undefined}
      spacing="default"
      stickyActions={isMobile}
      className={className}
    >
      {children}
    </MobileFormLayout>
  );
};

// ============================================================================
// Mobile-First Completion Component
// ============================================================================

export const OnboardingCompletion: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isMobile } = useResponsiveBreakpoint();

  return (
    <div className="text-center space-y-6">
      <div className="space-y-4">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="h-8 w-8 text-primary" />
        </div>
        
        <h2 className={cn(mobileFirst.typography.h2, "text-foreground")}>
          Setup Complete!
        </h2>
        
        <p className={cn(mobileFirst.typography.body, "text-muted-foreground max-w-md mx-auto")}>
                        Welcome to Notefy, {user?.businessName || 'User'}! Your account is now set up and ready to use.
        </p>
      </div>

      <ResponsiveGrid cols={isMobile ? 1 : 2} gap="sm" className="max-w-md mx-auto">
        <Button
          onClick={() => navigate('/portal/analytics')}
          className="w-full"
        >
          Go to Dashboard
        </Button>
        
        <Button
          onClick={() => navigate('/portal/conversations')}
          variant="outline"
          className="w-full"
        >
          Start Messaging
        </Button>
      </ResponsiveGrid>
    </div>
  );
};

export default OnboardingLayout; 