// @ts-nocheck
import React, { useState } from 'react';
import { 
  Setting<PERSON>, 
  Clock, 
  MessageSquare, 
  Users, 
  CheckCircle, 
  AlertCircle, 
  Plus,
  Edit,
  Trash2,
  Globe,
  Shield,
  Bell,
  Bot
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OnboardingStepWrapper } from './OnboardingLayout';
import { useNotifications } from '@/components/ui/notification';

// ============================================================================
// Types & Interfaces
// ============================================================================

interface BusinessHours {
  enabled: boolean;
  timezone: string;
  schedule: {
    [key: string]: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
}

interface AutoResponse {
  id: string;
  name: string;
  trigger: string;
  response: string;
  enabled: boolean;
  conditions: {
    businessHours: 'always' | 'inside' | 'outside';
    customerType: 'all' | 'new' | 'returning';
  };
}

interface MessageTemplate {
  id: string;
  name: string;
  category: 'order' | 'payment' | 'support' | 'marketing';
  language: 'en' | 'ar';
  content: string;
  variables: string[];
  status: 'draft' | 'pending' | 'approved' | 'rejected';
}

interface ConfigurationSettings {
  businessHours: BusinessHours;
  autoResponses: AutoResponse[];
  messageTemplates: MessageTemplate[];
  teamSettings: {
    maxConcurrentChats: number;
    autoAssignment: boolean;
    escalationTimeout: number;
  };
  notificationSettings: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
  };
}

// ============================================================================
// Configuration Step Component
// ============================================================================

export const ConfigurationStep: React.FC = () => {
  const notifications = useNotifications();
  
  // Configuration State
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const [activeTab, setActiveTab] = useState<'hours' | 'responses' | 'templates' | 'team' | 'notifications'>('hours');
  const [configError, setConfigError] = useState('');
  
  // Configuration Data
  const [configuration, setConfiguration] = useState<ConfigurationSettings>({
    businessHours: {
      enabled: true,
      timezone: 'Asia/Dubai',
      schedule: {
        monday: { enabled: true, start: '09:00', end: '17:00' },
        tuesday: { enabled: true, start: '09:00', end: '17:00' },
        wednesday: { enabled: true, start: '09:00', end: '17:00' },
        thursday: { enabled: true, start: '09:00', end: '17:00' },
        friday: { enabled: true, start: '09:00', end: '17:00' },
        saturday: { enabled: false, start: '09:00', end: '17:00' },
        sunday: { enabled: false, start: '09:00', end: '17:00' }
      }
    },
    autoResponses: [
      {
        id: '1',
        name: 'Welcome Message',
        trigger: 'hello,hi,hey',
        response: 'Hello! Thank you for contacting us. How can I help you today?',
        enabled: true,
        conditions: {
          businessHours: 'always',
          customerType: 'all'
        }
      },
      {
        id: '2',
        name: 'Out of Hours',
        trigger: '*',
        response: 'Thank you for your message. We are currently closed. Our business hours are Monday-Friday 9:00 AM - 5:00 PM UAE time. We will respond to your message as soon as possible.',
        enabled: true,
        conditions: {
          businessHours: 'outside',
          customerType: 'all'
        }
      }
    ],
    messageTemplates: [
      {
        id: '1',
        name: 'Order Confirmation',
        category: 'order',
        language: 'en',
        content: 'Thank you for your order #{{order_number}}! Your order total is {{amount}} and will be delivered to {{address}}. Track your order: {{tracking_url}}',
        variables: ['order_number', 'amount', 'address', 'tracking_url'],
        status: 'approved'
      },
      {
        id: '2',
        name: 'Payment Received',
        category: 'payment',
        language: 'en',
        content: 'Payment received! Thank you {{customer_name}} for your payment of {{amount}} for order #{{order_number}}. Your order is now being processed.',
        variables: ['customer_name', 'amount', 'order_number'],
        status: 'approved'
      }
    ],
    teamSettings: {
      maxConcurrentChats: 10,
      autoAssignment: true,
      escalationTimeout: 30
    },
    notificationSettings: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false
    }
  });

  // ============================================================================
  // Configuration Handlers
  // ============================================================================

  const handleSaveConfiguration = async () => {
    setIsConfiguring(true);
    setConfigError('');

    try {
      // Simulate API call to save configuration
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsConfigured(true);
      notifications.success('Configuration Saved!', 'Your initial configuration has been saved successfully');
    } catch (error) {
      setConfigError('Failed to save configuration. Please try again.');
      notifications.error('Save Failed', 'Unable to save configuration');
    } finally {
      setIsConfiguring(false);
    }
  };

  const handleSkipConfiguration = () => {
    setIsConfigured(true);
    notifications.success('Configuration Skipped', 'You can configure these settings later from the dashboard');
  };

  const handleUpdateBusinessHours = (day: string, field: string, value: any) => {
    setConfiguration(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        schedule: {
          ...prev.businessHours.schedule,
          [day]: {
            ...prev.businessHours.schedule[day],
            [field]: value
          }
        }
      }
    }));
  };

  const handleAddAutoResponse = () => {
    const newResponse: AutoResponse = {
      id: Date.now().toString(),
      name: 'New Auto Response',
      trigger: '',
      response: '',
      enabled: true,
      conditions: {
        businessHours: 'always',
        customerType: 'all'
      }
    };
    
    setConfiguration(prev => ({
      ...prev,
      autoResponses: [...prev.autoResponses, newResponse]
    }));
  };

  const handleUpdateAutoResponse = (id: string, field: string, value: any) => {
    setConfiguration(prev => ({
      ...prev,
      autoResponses: prev.autoResponses.map(response => 
        response.id === id ? { ...response, [field]: value } : response
      )
    }));
  };

  const handleDeleteAutoResponse = (id: string) => {
    setConfiguration(prev => ({
      ...prev,
      autoResponses: prev.autoResponses.filter(response => response.id !== id)
    }));
  };

  // ============================================================================
  // Render Business Hours Tab
  // ============================================================================

  const renderBusinessHoursTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-foreground">Business Hours</h4>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={configuration.businessHours.enabled}
            onChange={(e) => setConfiguration(prev => ({
              ...prev,
              businessHours: { ...prev.businessHours, enabled: e.target.checked }
            }))}
            className="rounded border-gray-300"
          />
          <span className="text-sm">Enable business hours</span>
        </label>
      </div>

      {configuration.businessHours.enabled && (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1">Timezone</label>
            <select
              value={configuration.businessHours.timezone}
              onChange={(e) => setConfiguration(prev => ({
                ...prev,
                businessHours: { ...prev.businessHours, timezone: e.target.value }
              }))}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="Asia/Dubai">Asia/Dubai (UAE)</option>
              <option value="Asia/Riyadh">Asia/Riyadh (Saudi Arabia)</option>
              <option value="Europe/London">Europe/London (UK)</option>
              <option value="America/New_York">America/New_York (US East)</option>
            </select>
          </div>

          <div className="space-y-2">
            {Object.entries(configuration.businessHours.schedule).map(([day, schedule]) => (
              <div key={day} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-20">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={schedule.enabled}
                      onChange={(e) => handleUpdateBusinessHours(day, 'enabled', e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm font-medium capitalize">{day}</span>
                  </label>
                </div>
                {schedule.enabled && (
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="time"
                      value={schedule.start}
                      onChange={(e) => handleUpdateBusinessHours(day, 'start', e.target.value)}
                      className="p-1 border border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-500">to</span>
                    <input
                      type="time"
                      value={schedule.end}
                      onChange={(e) => handleUpdateBusinessHours(day, 'end', e.target.value)}
                      className="p-1 border border-gray-300 rounded"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // ============================================================================
  // Render Auto Responses Tab
  // ============================================================================

  const renderAutoResponsesTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-foreground">Auto Responses</h4>
        <Button onClick={handleAddAutoResponse} size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          Add Response
        </Button>
      </div>

      <div className="space-y-3">
        {configuration.autoResponses.map((response) => (
          <Card key={response.id} className="border border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={response.enabled}
                    onChange={(e) => handleUpdateAutoResponse(response.id, 'enabled', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Input
                    value={response.name}
                    onChange={(e) => handleUpdateAutoResponse(response.id, 'name', e.target.value)}
                    className="font-medium"
                    placeholder="Response name"
                  />
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteAutoResponse(response.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium mb-1">Trigger Keywords</label>
                  <Input
                    value={response.trigger}
                    onChange={(e) => handleUpdateAutoResponse(response.id, 'trigger', e.target.value)}
                    placeholder="hello,hi,hey"
                  />
                  <p className="text-xs text-gray-500 mt-1">Separate with commas. Use * for all messages</p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Conditions</label>
                  <div className="flex gap-2">
                    <select
                      value={response.conditions.businessHours}
                      onChange={(e) => handleUpdateAutoResponse(response.id, 'conditions', {
                        ...response.conditions,
                        businessHours: e.target.value
                      })}
                      className="flex-1 p-2 text-sm border border-gray-300 rounded"
                    >
                      <option value="always">Always</option>
                      <option value="inside">Business Hours</option>
                      <option value="outside">Outside Hours</option>
                    </select>
                    <select
                      value={response.conditions.customerType}
                      onChange={(e) => handleUpdateAutoResponse(response.id, 'conditions', {
                        ...response.conditions,
                        customerType: e.target.value
                      })}
                      className="flex-1 p-2 text-sm border border-gray-300 rounded"
                    >
                      <option value="all">All Customers</option>
                      <option value="new">New Customers</option>
                      <option value="returning">Returning Customers</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className="mt-3">
                <label className="block text-sm font-medium mb-1">Response Message</label>
                <textarea
                  value={response.response}
                  onChange={(e) => handleUpdateAutoResponse(response.id, 'response', e.target.value)}
                  placeholder="Enter your auto response message..."
                  className="w-full p-2 border border-gray-300 rounded-md resize-none"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  // ============================================================================
  // Render Message Templates Tab
  // ============================================================================

  const renderMessageTemplatesTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-foreground">Message Templates</h4>
        <Badge variant="outline" className="gap-1">
          <CheckCircle className="h-3 w-3" />
          {configuration.messageTemplates.filter(t => t.status === 'approved').length} Approved
        </Badge>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 className="font-medium text-blue-900 mb-2">Pre-configured Templates</h5>
        <p className="text-sm text-blue-700">
          We've set up essential templates for order notifications and customer communication. 
          You can customize these or create new ones from the dashboard.
        </p>
      </div>

      <div className="space-y-3">
        {configuration.messageTemplates.map((template) => (
          <Card key={template.id} className="border border-gray-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  {template.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {template.category}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {template.language.toUpperCase()}
                  </Badge>
                  <Badge 
                    variant={template.status === 'approved' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {template.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-700">{template.content}</p>
              </div>
              {template.variables.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-gray-500 mb-1">Variables:</p>
                  <div className="flex flex-wrap gap-1">
                    {template.variables.map((variable) => (
                      <Badge key={variable} variant="outline" className="text-xs">
                        {`{{${variable}}}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  // ============================================================================
  // Render Team Settings Tab
  // ============================================================================

  const renderTeamSettingsTab = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-foreground">Team Settings</h4>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Max Concurrent Chats per Agent</label>
          <input
            type="number"
            value={configuration.teamSettings.maxConcurrentChats}
            onChange={(e) => setConfiguration(prev => ({
              ...prev,
              teamSettings: { ...prev.teamSettings, maxConcurrentChats: parseInt(e.target.value) }
            }))}
            className="w-full p-2 border border-gray-300 rounded-md"
            min="1"
            max="50"
          />
          <p className="text-xs text-gray-500 mt-1">Recommended: 5-10 for optimal response quality</p>
        </div>

        <div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={configuration.teamSettings.autoAssignment}
              onChange={(e) => setConfiguration(prev => ({
                ...prev,
                teamSettings: { ...prev.teamSettings, autoAssignment: e.target.checked }
              }))}
              className="rounded border-gray-300"
            />
            <span className="text-sm font-medium">Auto-assign conversations to available agents</span>
          </label>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Escalation Timeout (minutes)</label>
          <input
            type="number"
            value={configuration.teamSettings.escalationTimeout}
            onChange={(e) => setConfiguration(prev => ({
              ...prev,
              teamSettings: { ...prev.teamSettings, escalationTimeout: parseInt(e.target.value) }
            }))}
            className="w-full p-2 border border-gray-300 rounded-md"
            min="5"
            max="120"
          />
          <p className="text-xs text-gray-500 mt-1">Time before escalating unanswered conversations</p>
        </div>
      </div>
    </div>
  );

  // ============================================================================
  // Render Notifications Tab
  // ============================================================================

  const renderNotificationsTab = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-foreground">Notification Settings</h4>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Bell className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-medium">Email Notifications</p>
              <p className="text-sm text-gray-500">Receive email alerts for new messages</p>
            </div>
          </div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={configuration.notificationSettings.emailNotifications}
              onChange={(e) => setConfiguration(prev => ({
                ...prev,
                notificationSettings: { ...prev.notificationSettings, emailNotifications: e.target.checked }
              }))}
              className="rounded border-gray-300"
            />
          </label>
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-medium">Push Notifications</p>
              <p className="text-sm text-gray-500">Browser and mobile app notifications</p>
            </div>
          </div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={configuration.notificationSettings.pushNotifications}
              onChange={(e) => setConfiguration(prev => ({
                ...prev,
                notificationSettings: { ...prev.notificationSettings, pushNotifications: e.target.checked }
              }))}
              className="rounded border-gray-300"
            />
          </label>
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <MessageSquare className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-medium">SMS Notifications</p>
              <p className="text-sm text-gray-500">SMS alerts for urgent messages</p>
            </div>
          </div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={configuration.notificationSettings.smsNotifications}
              onChange={(e) => setConfiguration(prev => ({
                ...prev,
                notificationSettings: { ...prev.notificationSettings, smsNotifications: e.target.checked }
              }))}
              className="rounded border-gray-300"
            />
          </label>
        </div>
      </div>
    </div>
  );

  // ============================================================================
  // Main Render
  // ============================================================================

  if (isConfigured) {
    return (
      <OnboardingStepWrapper
        stepId="configuration"
        title="Initial Configuration"
        description="Set up your basic preferences and automation settings."
      >
        <div className="text-center space-y-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">Configuration Complete!</h3>
            <p className="text-muted-foreground">
              Your initial configuration has been saved. You can modify these settings anytime from the dashboard.
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">What's configured:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Business hours and timezone</li>
              <li>• Auto-response rules</li>
              <li>• Message templates</li>
              <li>• Team settings</li>
              <li>• Notification preferences</li>
            </ul>
          </div>
        </div>
      </OnboardingStepWrapper>
    );
  }

  return (
    <OnboardingStepWrapper
      stepId="configuration"
      title="Initial Configuration"
      description="Set up your basic preferences and automation settings to get started quickly."
    >
      <div className="space-y-6">
        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-2">
          {[
            { id: 'hours', label: 'Business Hours', icon: Clock },
            { id: 'responses', label: 'Auto Responses', icon: Bot },
            { id: 'templates', label: 'Templates', icon: MessageSquare },
            { id: 'team', label: 'Team Settings', icon: Users },
            { id: 'notifications', label: 'Notifications', icon: Bell }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Icon className="h-4 w-4" />
              {label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          {activeTab === 'hours' && renderBusinessHoursTab()}
          {activeTab === 'responses' && renderAutoResponsesTab()}
          {activeTab === 'templates' && renderMessageTemplatesTab()}
          {activeTab === 'team' && renderTeamSettingsTab()}
          {activeTab === 'notifications' && renderNotificationsTab()}
        </div>

        {/* Error Message */}
        {configError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-sm text-red-700">{configError}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center gap-4 pt-4 border-t border-gray-200">
          <Button
            onClick={handleSaveConfiguration}
            disabled={isConfiguring}
            className="flex-1 gap-2"
          >
            {isConfiguring ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving Configuration...
              </>
            ) : (
              <>
                <Settings className="h-4 w-4" />
                Save Configuration
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleSkipConfiguration}
            disabled={isConfiguring}
            className="gap-2"
          >
            Skip for Now
          </Button>
        </div>

        {/* Skip Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-900 mb-1">Optional Configuration</h4>
              <p className="text-sm text-yellow-700">
                This configuration is optional and can be set up later. We recommend configuring at least 
                business hours and auto-responses for better customer experience.
              </p>
            </div>
          </div>
        </div>
      </div>
    </OnboardingStepWrapper>
  );
};

export default ConfigurationStep; 