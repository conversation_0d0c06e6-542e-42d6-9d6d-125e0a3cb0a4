import React, { useState } from 'react';
import { CheckCircle, MessageSquare } from 'lucide-react';
import { OnboardingStepWrapper } from './OnboardingLayout';
import { WhatsAppOnboarding } from '@/components/whatsapp/WhatsAppOnboarding';
import { useNotifications } from '@/components/ui/notification';

// ============================================================================
// WhatsApp Step Component
// ============================================================================

export const WhatsAppStep: React.FC = () => {
  const notifications = useNotifications();
  const [isCompleted, setIsCompleted] = useState(false);
  const [connectionData, setConnectionData] = useState<any>(null);

  const handleOnboardingComplete = (data: any) => {
    setConnectionData(data);
    setIsCompleted(true);
    notifications.success('WhatsApp Setup Complete!', 'Your WhatsApp Business integration is ready to use');
  };

  const handleOnboardingError = (error: string) => {
    notifications.error('WhatsApp Setup Failed', error);
  };

  if (isCompleted) {
    return (
      <OnboardingStepWrapper
        stepId="whatsapp"
        title="WhatsApp Business Integration Complete"
        description="Your WhatsApp Business account has been successfully connected"
      >
        <div className="text-center space-y-6">
          <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-10 h-10 text-green-600" />
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-green-900 mb-2">
              WhatsApp Connected Successfully!
            </h3>
            <p className="text-green-700">
              You can now start messaging customers and managing conversations through your WhatsApp Business account.
            </p>
          </div>

          {connectionData?.whatsapp && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-center space-x-2">
                <MessageSquare className="w-5 h-5 text-green-600" />
                <span className="font-medium text-green-900">
                  Business Account ID: {connectionData.whatsapp.waba_id}
                </span>
              </div>
            </div>
          )}

          {connectionData?.store && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="font-medium text-blue-900">
                Store Connected: {connectionData.store.storeName}
              </p>
            </div>
          )}
        </div>
      </OnboardingStepWrapper>
    );
  }

  return (
    <OnboardingStepWrapper
      stepId="whatsapp"
      title="WhatsApp Business Integration"
      description="Connect your WhatsApp Business account to enable real-time customer communication and automated order notifications"
      showActions={false}
    >
      <WhatsAppOnboarding
        onComplete={handleOnboardingComplete}
        onError={handleOnboardingError}
      />
    </OnboardingStepWrapper>
  );
};

export default WhatsAppStep; 