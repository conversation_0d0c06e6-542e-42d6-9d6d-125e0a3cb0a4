// @ts-nocheck
// Portal Layout Component
// Main layout for the Notefy business application with floating navbar

import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { PortalFloatingNavbar } from '@/components/ui/floating-navbar';
import { createOnboardingSteps } from '@/components/ui/onboarding-progress-badge';
import { CollapsibleOnboardingBanner } from '@/components/ui/collapsible-onboarding-banner';
import { useCurrentUser } from '@/hooks/useGraphQL';
import { useResponsiveBreakpoint } from '@/components/ui/responsive-grid';

// ============================================================================
// Main Portal Layout Component
// ============================================================================

export const PortalLayout: React.FC = () => {
  const { data: user, loading: userLoading } = useCurrentUser();
  const navigate = useNavigate();
  const { isMobile } = useResponsiveBreakpoint();
  
  // Onboarding progress state
  const [onboardingSteps, setOnboardingSteps] = useState(() => createOnboardingSteps([
    { id: 'welcome', completed: true }, // Auto-completed after signup
  ]));
  
  const [showOnboardingBanner, setShowOnboardingBanner] = useState(false);

  // Check if there are incomplete REQUIRED steps (ignore optional steps)
  const hasIncompleteRequiredSteps = onboardingSteps.some(step => !step.completed && !step.optional);

  // Show onboarding banner only if there are incomplete required steps
  useEffect(() => {
    setShowOnboardingBanner(hasIncompleteRequiredSteps);
  }, [hasIncompleteRequiredSteps]);

  // Handle onboarding step navigation
  const handleStepClick = (stepId: string) => {
    navigate(`/onboarding?step=${stepId}`);
  };

  // Handle continuing onboarding
  const handleContinueOnboarding = () => {
    const nextIncompleteStep = onboardingSteps.find(step => !step.completed);
    if (nextIncompleteStep) {
      navigate(`/onboarding?step=${nextIncompleteStep.id}`);
    } else {
      navigate('/onboarding');
    }
  };

  // Handle skipping onboarding
  const handleSkipOnboarding = () => {
    setShowOnboardingBanner(false);
    // Mark all optional steps as completed/skipped
    setOnboardingSteps(prev => prev.map(step => ({ ...step, completed: true })));
  };

  // Handle dismissing the banner
  const handleDismissBanner = () => {
    setShowOnboardingBanner(false);
  };

  // Handle onboarding completion
  const handleOnboardingComplete = () => {
    setShowOnboardingBanner(false);
    navigate('/portal/analytics');
  };

  // Update step completion (could be called from onboarding pages)
  const updateStepCompletion = (stepId: string, completed: boolean) => {
    setOnboardingSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, completed } : step
    ));
  };

  if (userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading portal...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="h-screen bg-white flex flex-col overflow-hidden">
        {/* Collapsible Onboarding Banner */}
        {showOnboardingBanner && (
          <CollapsibleOnboardingBanner
            steps={onboardingSteps}
            currentStep={onboardingSteps.find(step => !step.completed)?.id}
            onStepClick={handleStepClick}
            onContinue={handleContinueOnboarding}
            onSkip={handleSkipOnboarding}
            onDismiss={handleDismissBanner}
            onComplete={handleOnboardingComplete}
            isVisible={true}
            mobileOptimized={isMobile}
            hapticFeedback={true}
            defaultExpanded={false}
          />
        )}

        {/* Main Content - With Bottom Padding for Floating Navbar */}
        <main className="flex-1 overflow-auto pb-24">
          <div className="container mx-auto px-4 py-6">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Floating Navigation - Outside Layout Container */}
      <PortalFloatingNavbar
        badgeCounts={{
          conversations: 12, // This would come from real-time data
          shopifyIntegration: 3, // Shopify webhook notifications
          whatsappConnections: 1, // WhatsApp connection issues
        }}
      />
    </>
  );
}; 