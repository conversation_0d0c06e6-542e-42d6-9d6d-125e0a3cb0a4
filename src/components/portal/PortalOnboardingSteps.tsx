// @ts-nocheck
import React, { useState } from 'react';
import { <PERSON>Circle, ArrowRight, Settings, ShoppingBag, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MobileContainer, MobileHStack, MobileVStack } from '@/components/ui/mobile-spacing';
import { MobileInteraction, hapticFeedback } from '@/components/ui/mobile-interactions';
import { cn } from '@/lib/utils';
import { OnboardingStep } from '@/components/ui/onboarding-progress-badge';

// ============================================================================
// Types
// ============================================================================

interface PortalOnboardingStepsProps {
  steps: OnboardingStep[];
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  onContinue?: () => void;
  onSkip?: () => void;
  className?: string;
  /** Mobile-optimized variant with better touch targets */
  mobileOptimized?: boolean;
  /** Enable haptic feedback on mobile devices */
  hapticFeedback?: boolean;
}

// ============================================================================
// Mobile-First Step Icons
// ============================================================================

const getStepIcon = (stepId: string) => {
  switch (stepId) {
    case 'shopify':
      return ShoppingBag;
    case 'whatsapp':
      return MessageSquare;
    case 'configuration':
      return Settings;
    default:
      return Settings;
  }
};

// ============================================================================
// Mobile-First Step Indicator
// ============================================================================

const MobileStepIndicator: React.FC<{
  step: OnboardingStep;
  index: number;
  isActive: boolean;
  isClickable: boolean;
  onStepClick?: (stepId: string) => void;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}> = ({ step, index, isActive, isClickable, onStepClick, mobileOptimized = false, hapticFeedback: enableHaptic = false }) => {
  const Icon = getStepIcon(step.id);
  
  const handleClick = () => {
    if (isClickable && onStepClick) {
      if (enableHaptic) hapticFeedback.light();
      onStepClick(step.id);
    }
  };

  return (
    <MobileInteraction
      enableTap={isClickable}
      hapticIntensity="light"
      className={cn(
        "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-xs",
        mobileOptimized && "min-h-[44px] px-4 py-3 text-sm",
        isClickable && "cursor-pointer active:scale-95",
        step.completed && "bg-green-100 text-green-700 border border-green-200",
        isActive && "bg-blue-100 text-blue-700 shadow-sm border border-blue-200",
        !step.completed && !isActive && "bg-gray-100 text-gray-500 border border-gray-200",
        isClickable && "hover:bg-muted/50 hover:border-muted-foreground/20"
      )}
      onClick={handleClick}
    >
      {/* Step Icon */}
      {step.completed ? (
        <CheckCircle className={cn(
          "text-green-600",
          mobileOptimized ? "h-5 w-5" : "h-4 w-4"
        )} />
      ) : (
        <Icon className={cn(
          mobileOptimized ? "h-5 w-5" : "h-4 w-4"
        )} />
      )}
      
      {/* Step Name */}
      <span className={cn(
        "font-medium truncate",
        mobileOptimized ? "text-sm" : "text-xs"
      )}>
        {step.name}
      </span>
      
      {/* Optional Badge */}
      {step.optional && (
        <Badge 
          variant="outline" 
          className={cn(
            "text-xs scale-75",
            mobileOptimized && "scale-90"
          )}
        >
          Optional
        </Badge>
      )}
    </MobileInteraction>
  );
};

// ============================================================================
// Mobile-First Progress Bar
// ============================================================================

const MobileProgressBar: React.FC<{
  completedCount: number;
  totalCount: number;
  mobileOptimized?: boolean;
}> = ({ completedCount, totalCount, mobileOptimized = false }) => {
  const progressPercentage = (completedCount / totalCount) * 100;
  
  return (
    <div className="flex items-center gap-2">
      <div className={cn(
        "bg-gray-200 rounded-full overflow-hidden",
        mobileOptimized ? "w-24 h-3" : "w-24 h-2"
      )}>
        <div 
          className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
      <span className={cn(
        "font-medium text-muted-foreground",
        mobileOptimized ? "text-sm" : "text-xs"
      )}>
        {Math.round(progressPercentage)}%
      </span>
    </div>
  );
};

// ============================================================================
// Mobile-First Action Buttons
// ============================================================================

const MobileActionButtons: React.FC<{
  onContinue?: () => void;
  onSkip?: () => void;
  nextIncompleteStep?: OnboardingStep;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}> = ({ onContinue, onSkip, nextIncompleteStep, mobileOptimized = false, hapticFeedback: enableHaptic = false }) => {
  const handleContinue = () => {
    if (enableHaptic) hapticFeedback.light();
    onContinue?.();
  };

  const handleSkip = () => {
    if (enableHaptic) hapticFeedback.medium();
    onSkip?.();
  };

  return (
    <div className="flex items-center gap-2">
      {/* Continue Button */}
      {nextIncompleteStep && onContinue && (
        <Button
          variant="default"
          size={mobileOptimized ? "sm" : "sm"}
          onClick={handleContinue}
          className={cn(
            "gap-1 text-xs font-medium",
            mobileOptimized && "min-h-[36px] px-4 py-2"
          )}
        >
          Continue Setup
          <ArrowRight className="h-3 w-3" />
        </Button>
      )}
      
      {/* Skip Button */}
      {onSkip && (
        <Button
          variant="ghost"
          size={mobileOptimized ? "sm" : "sm"}
          onClick={handleSkip}
          className={cn(
            "text-xs text-muted-foreground hover:text-foreground",
            mobileOptimized && "min-h-[36px] px-4 py-2"
          )}
        >
          Skip All
        </Button>
      )}
    </div>
  );
};

// ============================================================================
// Mobile-First Current Step Info
// ============================================================================

const MobileCurrentStepInfo: React.FC<{
  currentStep?: string;
  steps: OnboardingStep[];
  mobileOptimized?: boolean;
}> = ({ currentStep, steps, mobileOptimized = false }) => {
  const currentStepData = steps.find(step => step.id === currentStep);
  
  if (!currentStepData) return null;

  return (
    <div className={cn(
      "p-3 bg-blue-50 border border-blue-200 rounded-lg",
      mobileOptimized && "p-4"
    )}>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
        <span className={cn(
          "text-blue-800 font-medium",
          mobileOptimized ? "text-sm" : "text-xs"
        )}>
          Current: {currentStepData.name}
        </span>
      </div>
      <p className={cn(
        "text-blue-700 mt-1",
        mobileOptimized ? "text-sm" : "text-xs"
      )}>
        {currentStepData.description}
      </p>
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

export const PortalOnboardingSteps: React.FC<PortalOnboardingStepsProps> = ({
  steps,
  currentStep,
  onStepClick,
  onContinue,
  onSkip,
  className,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
}) => {
  const completedCount = steps.filter(step => step.completed).length;
  const totalCount = steps.length;
  const nextIncompleteStep = steps.find(step => !step.completed);

  // Mobile-first responsive behavior
  const [isMobile, setIsMobile] = React.useState(false);
  
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const isOptimizedForMobile = mobileOptimized || isMobile;

  return (
    <div className={cn(
      "bg-white border-b border-border/50",
      isOptimizedForMobile && "px-4 py-3",
      className
    )}>
      <MobileContainer 
        padding={isOptimizedForMobile ? "mobile-sm" : "component"}
        className="mx-auto max-w-7xl"
      >
        {/* Mobile Layout */}
        {isOptimizedForMobile ? (
          <MobileVStack space="mobile-sm">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <h3 className="text-sm font-medium text-foreground">
                  Setup Progress
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {completedCount} of {totalCount}
                </Badge>
              </div>
              
              <MobileActionButtons
                onContinue={onContinue}
                onSkip={onSkip}
                nextIncompleteStep={nextIncompleteStep}
                mobileOptimized={isOptimizedForMobile}
                hapticFeedback={enableHaptic}
              />
            </div>

            {/* Progress Bar */}
            <MobileProgressBar
              completedCount={completedCount}
              totalCount={totalCount}
              mobileOptimized={isOptimizedForMobile}
            />

            {/* Steps - Mobile Grid */}
            <div className="grid grid-cols-1 gap-2">
              {steps.map((step, index) => {
                const isClickable = onStepClick && (step.completed || step.id === currentStep);
                const isActive = step.id === currentStep;
                
                return (
                  <MobileStepIndicator
                    key={step.id}
                    step={step}
                    index={index}
                    isActive={isActive}
                    isClickable={isClickable}
                    onStepClick={onStepClick}
                    mobileOptimized={isOptimizedForMobile}
                    hapticFeedback={enableHaptic}
                  />
                );
              })}
            </div>

            {/* Current Step Info */}
            <MobileCurrentStepInfo
              currentStep={currentStep}
              steps={steps}
              mobileOptimized={isOptimizedForMobile}
            />
          </MobileVStack>
        ) : (
          /* Desktop Layout */
          <div className="flex items-center justify-between">
            {/* Left: Steps Progress */}
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-3">
                <h3 className="text-sm font-medium text-foreground">
                  Setup Progress
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {completedCount} of {totalCount}
                </Badge>
              </div>
              
              {/* Steps Indicators */}
              <div className="flex items-center gap-2">
                {steps.map((step, index) => {
                  const isClickable = onStepClick && (step.completed || step.id === currentStep);
                  const isActive = step.id === currentStep;
                  
                  return (
                    <React.Fragment key={step.id}>
                      <MobileStepIndicator
                        step={step}
                        index={index}
                        isActive={isActive}
                        isClickable={isClickable}
                        onStepClick={onStepClick}
                        mobileOptimized={false}
                        hapticFeedback={enableHaptic}
                      />
                      
                      {index < steps.length - 1 && (
                        <ArrowRight className="h-3 w-3 text-muted-foreground" />
                      )}
                    </React.Fragment>
                  );
                })}
              </div>
            </div>

            {/* Right: Progress Bar and Actions */}
            <div className="flex items-center gap-4">
              {/* Progress Bar */}
              <MobileProgressBar
                completedCount={completedCount}
                totalCount={totalCount}
                mobileOptimized={false}
              />

              {/* Action Buttons */}
              <MobileActionButtons
                onContinue={onContinue}
                onSkip={onSkip}
                nextIncompleteStep={nextIncompleteStep}
                mobileOptimized={false}
                hapticFeedback={enableHaptic}
              />
            </div>
          </div>
        )}
      </MobileContainer>
    </div>
  );
};

// ============================================================================
// Mobile-First Compact Variant
// ============================================================================

export interface CompactOnboardingStepsProps extends PortalOnboardingStepsProps {
  showStepNames?: boolean;
}

export const CompactOnboardingSteps: React.FC<CompactOnboardingStepsProps> = ({
  showStepNames = false,
  mobileOptimized = true,
  ...props
}) => {
  return (
    <PortalOnboardingSteps
      {...props}
      mobileOptimized={mobileOptimized}
      className={cn("py-2", props.className)}
    />
  );
};

// ============================================================================
// Mobile-First Inline Variant
// ============================================================================

export interface InlineOnboardingStepsProps extends PortalOnboardingStepsProps {
  orientation?: 'horizontal' | 'vertical';
}

export const InlineOnboardingSteps: React.FC<InlineOnboardingStepsProps> = ({
  orientation = 'horizontal',
  mobileOptimized = true,
  ...props
}) => {
  const completedCount = props.steps.filter(step => step.completed).length;
  const totalCount = props.steps.length;
  
  return (
    <div className={cn(
      "p-3 bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg",
      mobileOptimized && "min-h-[56px] touch-manipulation",
      props.className
    )}>
      <div className={cn(
        "flex items-center gap-3",
        orientation === 'vertical' && "flex-col items-start"
      )}>
        {/* Progress Summary */}
        <div className="flex items-center gap-2">
          <Settings className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Setup</span>
          <Badge variant="secondary" className="text-xs">
            {completedCount}/{totalCount}
          </Badge>
        </div>

        {/* Progress Bar */}
        <MobileProgressBar
          completedCount={completedCount}
          totalCount={totalCount}
          mobileOptimized={mobileOptimized}
        />

        {/* Actions */}
        <MobileActionButtons
          onContinue={props.onContinue}
          onSkip={props.onSkip}
          nextIncompleteStep={props.steps.find(step => !step.completed)}
          mobileOptimized={mobileOptimized}
          hapticFeedback={props.hapticFeedback}
        />
      </div>
    </div>
  );
};

export default PortalOnboardingSteps; 