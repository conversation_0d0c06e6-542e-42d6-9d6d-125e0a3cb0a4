import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare, 
  Phone, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Activity,
  Shield,
  Loader2,
  Plus,
  Trash2,
  Edit
} from 'lucide-react';
import { WhatsAppOnboarding } from './WhatsAppOnboarding';
import { useWhatsApp } from '@/hooks/useWhatsApp';
import { useAuth } from '@/hooks/useAuth';
import { type WhatsAppConnection } from '@/lib/graphql/queries/whatsapp';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// ============================================================================
// Types & Interfaces
// ============================================================================

interface WhatsAppManagerProps {
  className?: string;
}

// ============================================================================
// Production WhatsApp Manager Component
// ============================================================================

export const WhatsAppManager: React.FC<WhatsAppManagerProps> = ({ className }) => {
  // ========================================
  // State Management
  // ========================================
  
  const { user } = useAuth();
  const { 
    isLoading, 
    error, 
    getUserConnections, 
    deleteConnection,
    verifyConnection,
    clearError 
  } = useWhatsApp();
  
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [connections, setConnections] = useState<WhatsAppConnection[]>([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<{
    delete?: string;
    edit?: string;
    verify?: string;
  }>({});

  // ========================================
  // Data Fetching
  // ========================================

  const fetchConnections = useCallback(async () => {
    try {
      const userConnections = await getUserConnections();
      setConnections(userConnections);
    } catch (error) {
      console.error('Failed to fetch WhatsApp connections:', error);
      toast.error('Failed to load WhatsApp connections');
    } finally {
      setInitialLoading(false);
    }
  }, [getUserConnections]);

  // Fetch connections on component mount and when user changes
  useEffect(() => {
    if (user?.userId) {
      fetchConnections();
    }
  }, [user?.userId, fetchConnections]);

  // ========================================
  // Event Handlers
  // ========================================

  const handleAddNumber = useCallback(() => {
    setShowOnboarding(true);
    clearError();
  }, [clearError]);

  const handleOnboardingComplete = useCallback((connectionData: any) => {
    setShowOnboarding(false);
    
    // Refresh connections list
    fetchConnections();
    
    toast.success(
      'WhatsApp number connected successfully!',
      { description: `Phone number: ${connectionData.displayPhoneNumber || 'Unknown'}` }
    );
  }, [fetchConnections]);

  const handleOnboardingError = useCallback((error: string) => {
    console.error('WhatsApp onboarding failed:', error);
    toast.error('WhatsApp connection failed', { description: error });
    // Keep onboarding modal open for retry
  }, []);

  const handleEditConnection = useCallback(async (connectionId: string) => {
    setActionLoading(prev => ({ ...prev, edit: connectionId }));
    
    try {
      // For now, just show a placeholder - implement edit modal later
      toast.info('Edit functionality', { description: 'Coming soon - contact support for changes' });
    } catch (error) {
      console.error('Failed to edit connection:', error);
      toast.error('Failed to edit connection');
    } finally {
      setActionLoading(prev => ({ ...prev, edit: undefined }));
    }
  }, []);

  const handleDeleteConnection = useCallback(async (connectionId: string) => {
    if (!confirm('Are you sure you want to delete this WhatsApp connection? This action cannot be undone.')) {
      return;
    }

    setActionLoading(prev => ({ ...prev, delete: connectionId }));
    
    try {
      const success = await deleteConnection(connectionId);
      
      if (success) {
        // Remove from local state
        setConnections(prev => prev.filter(conn => conn.id !== connectionId));
        toast.success('WhatsApp connection deleted successfully');
      } else {
        throw new Error('Delete operation failed');
      }
    } catch (error) {
      console.error('Failed to delete connection:', error);
      toast.error('Failed to delete WhatsApp connection');
    } finally {
      setActionLoading(prev => ({ ...prev, delete: undefined }));
    }
  }, [deleteConnection]);

  const handleVerifyConnection = useCallback(async (connectionId: string) => {
    setActionLoading(prev => ({ ...prev, verify: connectionId }));
    
    try {
      const updatedConnection = await verifyConnection(connectionId);
      
      if (updatedConnection) {
        // Update local state
        setConnections(prev => 
          prev.map(conn => 
            conn.id === connectionId ? updatedConnection : conn
          )
        );
        toast.success('Connection verified successfully');
      } else {
        throw new Error('Verification failed');
      }
    } catch (error) {
      console.error('Failed to verify connection:', error);
      toast.error('Failed to verify connection');
    } finally {
      setActionLoading(prev => ({ ...prev, verify: undefined }));
    }
  }, [verifyConnection]);

  // ========================================
  // Utility Functions
  // ========================================

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
      case 'disconnected': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'suspended': return <AlertCircle className="w-4 h-4" />;
      case 'disconnected': return <AlertCircle className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  // Calculate analytics
  const activeConnections = connections.filter(conn => conn.status === 'ACTIVE').length;
  const totalConversations = connections.reduce((sum, conn) => sum + conn.totalConversations, 0);
  const activeConversations = connections.reduce((sum, conn) => sum + conn.activeConversations, 0);
  const verifiedConnections = connections.filter(conn => conn.isVerified).length;

  // ========================================
  // Render Methods
  // ========================================

  if (showOnboarding) {
    return (
      <div className={cn("container mx-auto px-4 py-8", className)}>
        <div className="mb-6">
          <Button 
            variant="outline" 
            onClick={() => setShowOnboarding(false)}
            className="mb-4"
          >
            ← Back to Manager
          </Button>
        </div>
        <WhatsAppOnboarding 
          onComplete={handleOnboardingComplete}
          onError={handleOnboardingError}
        />
      </div>
    );
  }

  if (initialLoading) {
    return (
      <div className={cn("container mx-auto px-4 py-8", className)}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">Loading WhatsApp connections...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("container mx-auto px-4 py-8 space-y-6", className)}>
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">WhatsApp Business Manager</h1>
          <p className="text-gray-600 mt-2">
            Manage your WhatsApp Business accounts and monitor performance
          </p>
          {error && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-red-800 text-sm">{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="ml-auto text-red-600 hover:text-red-800"
              >
                Dismiss
              </Button>
            </div>
          )}
        </div>
        <Button 
          onClick={handleAddNumber} 
          className="flex items-center gap-2"
          disabled={isLoading}
        >
          <Plus className="w-4 h-4" />
          Add WhatsApp Number
        </Button>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Numbers</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeConnections}</div>
            <p className="text-xs text-muted-foreground">
              {connections.length} total connections
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConversations.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {activeConversations} currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Verified Numbers</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{verifiedConnections}</div>
            <p className="text-xs text-muted-foreground">
              Out of {connections.length} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {activeConnections > 0 ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-sm font-medium">Operational</span>
                </>
              ) : (
                <>
                  <Clock className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm font-medium">No Active Numbers</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Connections List */}
      <Card>
        <CardHeader>
          <CardTitle>WhatsApp Business Numbers</CardTitle>
          <CardDescription>
            Manage your connected WhatsApp Business phone numbers and their settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {connections.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No WhatsApp numbers connected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by connecting your first WhatsApp Business number.
              </p>
              <div className="mt-6">
                <Button onClick={handleAddNumber}>
                  <Plus className="w-4 h-4 mr-2" />
                  Connect Your First Number
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {connections.map((connection) => (
                <div key={connection.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                          <Phone className="h-6 w-6 text-green-600" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {connection.displayName}
                          </p>
                          <Badge className={cn("flex items-center gap-1", getStatusColor(connection.status))}>
                            {getStatusIcon(connection.status)}
                            {connection.status}
                          </Badge>
                          {connection.isVerified && (
                            <Badge variant="outline" className="text-green-700 border-green-300">
                              <Shield className="w-3 h-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-500">{connection.phoneNumber}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <p className="text-xs text-gray-400">
                            Business ID: {connection.businessAccountId}
                          </p>
                          {connection.businessProfile?.displayName && (
                            <p className="text-xs text-gray-400">
                              Business: {connection.businessProfile.displayName}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {connection.totalConversations.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-500">Total conversations</p>
                      </div>
                      
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {connection.activeConversations.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-500">Active chats</p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {!connection.isVerified && connection.status === 'PENDING' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleVerifyConnection(connection.id)}
                            disabled={actionLoading.verify === connection.id}
                          >
                            {actionLoading.verify === connection.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <Shield className="h-4 w-4 mr-1" />
                                Verify
                              </>
                            )}
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditConnection(connection.id)}
                          disabled={actionLoading.edit === connection.id}
                        >
                          {actionLoading.edit === connection.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Edit className="h-4 w-4" />
                          )}
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteConnection(connection.id)}
                          disabled={actionLoading.delete === connection.id}
                          className="text-red-600 hover:text-red-800"
                        >
                          {actionLoading.delete === connection.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {connection.businessProfile?.about && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <p className="text-xs text-gray-600">
                        <span className="font-medium">About:</span> {connection.businessProfile.about}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};