import React, { useState, useEffect, useCallback } from 'react';
import { MessageSquare, Store, CheckCircle, AlertCircle, Loader2, ArrowRight, ExternalLink, Shield, Phone, Building } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '../ui/card';
import { cn } from '@/lib/utils';
import { useWhatsApp } from '../../hooks/useWhatsApp';
import { useAuth } from '../../hooks/useAuth';
import { useUserShopifyStores } from '../../hooks/useGraphQL';
import { useToast } from '../../hooks/use-toast';

// ============================================================================
// Types & Interfaces
// ============================================================================

interface WhatsAppOnboardingProps {
  onComplete?: (data: OnboardingCompletionData) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
}

interface EmbeddedSignupResponse {
  phone_number_id: string;
  waba_id: string;
  business_id: string;
}

interface StoreConnectionData {
  storeId: string;
  storeName: string;
  shopDomain: string;
}

interface OnboardingCompletionData {
  whatsapp: EmbeddedSignupResponse | null;
  store: StoreConnectionData | null;
  connection: any | null;
  complete?: boolean; // ✅ Add complete field for completion status
}

// Extended Window interface for Facebook SDK
declare global {
  interface Window {
    FB: any;
    fbAsyncInit: () => void;
  }
}

// ============================================================================
// Meta Facebook SDK Configuration
// ============================================================================

const FACEBOOK_CONFIG = {
  APP_ID: '349869447413699', // Your Facebook App ID
  API_VERSION: 'v23.0', // Latest Graph API version
  SDK_URL: 'https://connect.facebook.net/en_US/sdk.js',
  // Updated with the correct config ID from Facebook App Dashboard
  CONFIG_ID: '370641605638743', // WhatsApp Business embedded signup configuration
} as const;

// ============================================================================
// Main Component
// ============================================================================

export const WhatsAppOnboarding: React.FC<WhatsAppOnboardingProps> = ({
  onComplete,
  onError,
  className,
}) => {
  // ===== Hooks =====
  const { user } = useAuth();
  const { processEmbeddedSignup, connectToStore, isLoading, error } = useWhatsApp();
  const { data: userStores, loading: storesLoading, error: storesError } = useUserShopifyStores();
  const { toast } = useToast();

  // ===== State Management =====
  const [currentStep, setCurrentStep] = useState<'embed' | 'store-connect' | 'complete'>('embed');
  const [isSDKLoaded, setIsSDKLoaded] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [embeddedSignupData, setEmbeddedSignupData] = useState<EmbeddedSignupResponse | null>(null);
  const [selectedStore, setSelectedStore] = useState<StoreConnectionData | null>(null);
  const [authorizationCode, setAuthorizationCode] = useState<string | null>(null);
  
  // ✅ PERSISTENT CACHE: Use sessionStorage instead of React state for user data caching
  // This persists across component remounts during Facebook embedded signup flow
  const cacheUserData = useCallback((userData: { userId: string; organizationId: string; organizationRole: string }) => {
    sessionStorage.setItem('whatsapp_signup_user_cache', JSON.stringify(userData));
  }, []);
  
  const getCachedUserData = useCallback((): { userId: string; organizationId: string; organizationRole: string } | null => {
    try {
      const cached = sessionStorage.getItem('whatsapp_signup_user_cache');
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Failed to parse cached user data:', error);
      return null;
    }
  }, []);
  
  const clearCachedUserData = useCallback(() => {
    sessionStorage.removeItem('whatsapp_signup_user_cache');
  }, []);

  // Convert user stores to the format expected by the component
  const availableStores: StoreConnectionData[] = React.useMemo(() => {
    if (!userStores) return [];
    
    return userStores.map(store => ({
      storeId: store.id,
      storeName: store.storeName,
      shopDomain: store.shopDomain,
    }));
  }, [userStores]);

  // ===== Facebook SDK Initialization =====
  useEffect(() => {
    const initializeFacebookSDK = async () => {
      try {
        // Check if SDK is already loaded
        if (window.FB) {
          setIsSDKLoaded(true);
          setIsInitializing(false);
          return;
        }

        // Load Facebook SDK script
        const script = document.createElement('script');
        script.src = FACEBOOK_CONFIG.SDK_URL;
        script.async = true;
        script.defer = true;
        script.crossOrigin = 'anonymous';

        // SDK initialization callback
        window.fbAsyncInit = () => {
          window.FB.init({
            appId: FACEBOOK_CONFIG.APP_ID,
            autoLogAppEvents: true,
            xfbml: true,
            version: FACEBOOK_CONFIG.API_VERSION
          });
          setIsSDKLoaded(true);
          setIsInitializing(false);
        };

        // Handle script loading
        script.onload = () => {
          if (window.fbAsyncInit) {
            window.fbAsyncInit();
          }
        };

        script.onerror = () => {
          console.error('Failed to load Facebook SDK');
          setIsInitializing(false);
          onError?.('Failed to load Facebook SDK');
        };

        document.head.appendChild(script);

        // Cleanup
        return () => {
          if (script.parentNode) {
            script.parentNode.removeChild(script);
          }
        };
      } catch (error) {
        console.error('SDK initialization error:', error);
        setIsInitializing(false);
        onError?.('SDK initialization failed');
      }
    };

    initializeFacebookSDK();
  }, [onError]);

  // ===== Message Event Listener for Embedded Signup =====
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Security check: ensure message is from facebook.com
      if (!event.origin.endsWith('facebook.com')) return;

      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'WA_EMBEDDED_SIGNUP') {
          console.log('📨 WhatsApp Embedded Signup Message:', data);
          
          switch (data.event) {
            case 'FINISH':
            case 'FINISH_ONLY_WABA':
            case 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING':
              handleEmbeddedSignupSuccess(data.data);
              break;
            case 'CANCEL':
              handleEmbeddedSignupCancel(data.data);
              break;
            default:
              console.log('Unknown embedded signup event:', data.event);
          }
        }
      } catch (parseError) {
        // Handle non-JSON messages
        console.log('Non-JSON message from Facebook:', event.data);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // ===== Event Handlers =====
  const handleEmbeddedSignupSuccess = useCallback(async (data: EmbeddedSignupResponse) => {
    try {
      console.log('✅ Embedded signup successful:', data);
      
      // ✅ USE CACHED USER DATA: Use cached data instead of potentially null live user
      const currentUser = getCachedUserData() || user; // Fallback to live user if cache is empty
      
      // Process the signup data with our backend
      console.log('🔍 DEBUG: User object at WhatsApp signup:', {
        userId: currentUser?.userId,
        organizationId: currentUser?.organizationId,
        organizationRole: currentUser?.organizationRole,
        liveUser: user,
        cachedUser: getCachedUserData(),
        usingCachedData: !!getCachedUserData()
      });

      // ✅ COMPREHENSIVE DEBUG: Check organizationId value
      const orgId = currentUser?.organizationId;
      console.log('🔍 CRITICAL DEBUG - organizationId analysis:', {
        orgId_value: orgId,
        orgId_type: typeof orgId,
        orgId_length: orgId?.length,
        orgId_trimmed: orgId?.trim(),
        is_empty_string: orgId === '',
        is_undefined: orgId === undefined,
        is_null: orgId === null,
        is_falsy: !orgId,
        data_source: getCachedUserData() ? 'cached' : 'live'
      });

      if (!orgId || orgId.trim() === '') {
        throw new Error(`FRONTEND DEBUG: organizationId is missing or empty. Value: "${orgId}", Type: ${typeof orgId}, Source: ${getCachedUserData() ? 'cached' : 'live'}`);
      }
      
      const result = await processEmbeddedSignup({
        wabaId: data.waba_id,
        phoneNumberId: data.phone_number_id,
        businessId: data.business_id,
        user_id: currentUser?.userId,           // ✅ Use cached user data
        organizationId: currentUser?.organizationId, // ✅ Use cached user data
        authorizationCode: authorizationCode || undefined // Pass the authorization code
      });

      if (result && result.success) {
        setEmbeddedSignupData(data);
        setCurrentStep('store-connect');
        toast({
          title: 'Success',
          description: 'WhatsApp Business connected successfully!',
        });
      } else {
        throw new Error(result?.errorMessage || 'Failed to process embedded signup');
      }
    } catch (error) {
      console.error('❌ Failed to process embedded signup:', error);
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
      onError?.(message);
      // ✅ Clear cached data on error to prevent stale data
      clearCachedUserData();
    }
  }, [processEmbeddedSignup, getCachedUserData, user, onError, authorizationCode, toast, clearCachedUserData]); // ✅ Use cachedUserData instead of user properties

  const handleEmbeddedSignupCancel = useCallback((data: any) => {
    console.log('❌ Embedded signup cancelled:', data);
    setCurrentStep('embed');
    
    if (data && data.error_message) {
      toast({
        title: 'Error',
        description: data.error_message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: 'Error',
        description: 'Setup was cancelled',
        variant: 'destructive',
      });
    }
    // ✅ Clear cached data on cancellation
    clearCachedUserData();
  }, [toast, clearCachedUserData]);

  const launchEmbeddedSignup = useCallback(() => {
    // Debug logging for troubleshooting
    console.log('🔍 DEBUG: Launch WhatsApp signup - User context check:', {
      user: user,
      userId: user?.userId,
      organizationId: user?.organizationId,
      isSDKLoaded: isSDKLoaded
    });

    if (!isSDKLoaded) {
      console.error('❌ SDK not loaded');
      toast({
        title: 'Error',
        description: 'WhatsApp SDK not loaded. Please wait and try again.',
        variant: 'destructive',
      });
      return;
    }

    if (!user?.userId) {
      console.error('❌ User not authenticated or user context not loaded:', user);
      toast({
        title: 'Error', 
        description: 'User authentication not ready. Please wait for login to complete.',
        variant: 'destructive',
      });
      return;
    }

    if (!user?.organizationId || user.organizationId.trim() === '') {
      console.error('❌ User organization context not loaded or empty:', user);
      toast({
        title: 'Error',
        description: 'Organization context not ready. Please wait for setup to complete or try refreshing.',
        variant: 'destructive', 
      });
      return;
    }

    // ✅ CACHE USER DATA: Store user data before Facebook flow starts
    // This prevents auth context loss during Facebook embedded signup
    cacheUserData({
      userId: user.userId,
      organizationId: user.organizationId,
      organizationRole: user.organizationRole
    });
    
    console.log('✅ Cached user data before Facebook flow:', {
      userId: user.userId,
      organizationId: user.organizationId,
      organizationRole: user.organizationRole
    });

    try {
      // Facebook login callback
      const fbLoginCallback = (response: any) => {
        if (response.authResponse) {
          const code = response.authResponse.code;
          console.log('🔑 Authorization code received:', code);
          setAuthorizationCode(code); // Store the authorization code
        } else {
          console.log('🚫 Facebook login failed or cancelled:', response);
          handleEmbeddedSignupCancel({ current_step: 'LOGIN_FAILED' });
        }
      };
    //   {
    // "phone_number_id": "114357818272016",
    // "waba_id": "1080158976805627",
    // "business_id": "456354381573617"
// }

      // Launch Meta's embedded signup flow (v3 API)
      window.FB.login(fbLoginCallback, {
        config_id: FACEBOOK_CONFIG.CONFIG_ID,
        response_type: 'code',
        override_default_response_type: true,
        extras: {
          version: '3', // Required for v3 API
          setup: {
            // Optional: Pre-fill business data if available
            ...(user?.businessName && {
              business: {
                name: user.businessName,
                email: user.email,
              }
            })
          },
          sessionInfoVersion: '3' // Version 3 for latest features
        }
      });
    } catch (error) {
      console.error('Failed to launch embedded signup:', error);
      toast({
        title: 'Error',
        description: 'Failed to start WhatsApp setup',
        variant: 'destructive',
      });
    }
  }, [isSDKLoaded, user, handleEmbeddedSignupCancel, cacheUserData]);

  const handleStoreConnection = useCallback(async () => {
    if (!embeddedSignupData || !selectedStore) return;

    try {
      const result = await connectToStore({
        wabaId: embeddedSignupData.waba_id,
        phoneNumberId: embeddedSignupData.phone_number_id,
        storeId: selectedStore.storeId,
        organizationId: user?.organizationId // ✅ Use proper organizationId instead of userId
      });

      if (result && result.success) {
        setCurrentStep('complete');
        toast({
          title: 'Success',
          description: 'Store connected successfully!',
        });
        onComplete?.({
          whatsapp: embeddedSignupData,
          store: selectedStore,
          connection: result
        });
        // ✅ Clear cached data on successful completion
        clearCachedUserData();
      } else {
        throw new Error(result?.message || 'Failed to connect store');
      }
    } catch (error) {
      console.error('❌ Failed to connect store:', error);
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    }
  }, [embeddedSignupData, selectedStore, connectToStore, user?.organizationId, onComplete, toast, clearCachedUserData]);

  const handleSkipStoreConnection = useCallback(() => {
    if (!embeddedSignupData) return;
    
    setCurrentStep('complete');
    onComplete?.({
      whatsapp: embeddedSignupData,
      store: null,
      connection: null
    });
    toast({
      title: 'Success',
      description: 'WhatsApp setup completed!',
    });
    // ✅ Clear cached data on successful completion
    clearCachedUserData();
  }, [embeddedSignupData, onComplete, toast, clearCachedUserData]);

  // ===== Render Helpers =====
  const renderEmbeddedSignupStep = () => (
    <Card className="w-full">
      <CardHeader className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <MessageSquare className="w-8 h-8 text-green-600" />
        </div>
        <CardTitle className="text-2xl">Connect WhatsApp Business</CardTitle>
        <CardDescription className="text-base">
          Connect your WhatsApp Business account to start sending messages to your customers
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center space-y-2">
            <Shield className="w-6 h-6 text-blue-600 mx-auto" />
            <p className="text-sm font-medium">Secure Connection</p>
            <p className="text-xs text-muted-foreground">Direct integration with Meta</p>
          </div>
          <div className="text-center space-y-2">
            <Phone className="w-6 h-6 text-green-600 mx-auto" />
            <p className="text-sm font-medium">Business Messaging</p>
            <p className="text-xs text-muted-foreground">Professional WhatsApp features</p>
          </div>
          <div className="text-center space-y-2">
            <Building className="w-6 h-6 text-purple-600 mx-auto" />
            <p className="text-sm font-medium">Business Verification</p>
            <p className="text-xs text-muted-foreground">Verified business badge</p>
          </div>
        </div>

        {/* Action Button */}
        <div className="space-y-4">
          <Button
            onClick={launchEmbeddedSignup}
            disabled={!isSDKLoaded || isLoading}
            size="lg"
            className="w-full h-12 text-base"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <MessageSquare className="w-5 h-5 mr-2" />
                Connect with Facebook
                <ExternalLink className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>

          <p className="text-xs text-center text-muted-foreground">
            By connecting, you agree to Meta's terms and conditions for WhatsApp Business API
          </p>
        </div>

        {/* Loading State */}
        {isInitializing && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span className="text-sm text-muted-foreground">Loading Facebook SDK...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderStoreConnectionStep = () => (
    <Card className="w-full">
      <CardHeader className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Store className="w-8 h-8 text-blue-600" />
        </div>
        <CardTitle className="text-2xl">Connect to Store</CardTitle>
        <CardDescription className="text-base">
          Optional: Connect your WhatsApp number to a Shopify store for automated notifications
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Connected WhatsApp Info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div>
              <p className="font-medium text-green-900">WhatsApp Connected</p>
              <p className="text-sm text-green-700">
                Phone: {embeddedSignupData?.phone_number_id || 'Connected'}
              </p>
            </div>
          </div>
        </div>

        {/* Store Selection */}
        <div className="space-y-4">
          <h3 className="font-medium">Available Stores</h3>
          
          {storesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
              <span className="ml-2 text-muted-foreground">Loading stores...</span>
            </div>
          ) : storesError ? (
            <div className="text-center py-8 text-destructive">
              <AlertCircle className="w-12 h-12 mx-auto mb-4" />
              <p>Failed to load stores</p>
              <p className="text-sm">{storesError}</p>
            </div>
          ) : availableStores.length > 0 ? (
            <div className="space-y-2">
              {availableStores.map((store) => (
                <div
                  key={store.storeId}
                  className={cn(
                    "p-4 border rounded-lg cursor-pointer transition-colors",
                    selectedStore?.storeId === store.storeId
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => setSelectedStore(store)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{store.storeName}</p>
                      <p className="text-sm text-muted-foreground">{store.shopDomain}</p>
                    </div>
                    {selectedStore?.storeId === store.storeId && (
                      <CheckCircle className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Store className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No Shopify stores found</p>
              <p className="text-sm">Connect a Shopify store first to enable this feature</p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            onClick={handleSkipStoreConnection}
            variant="outline"
            className="flex-1"
          >
            Skip for now
          </Button>
          
          <Button
            onClick={handleStoreConnection}
            disabled={!selectedStore || isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                Connect Store
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>

        <p className="text-xs text-center text-muted-foreground">
          You can connect stores later from the dashboard
        </p>
      </CardContent>
    </Card>
  );

  const renderCompleteStep = () => (
    <Card className="w-full">
      <CardHeader className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <CardTitle className="text-2xl">Setup Complete!</CardTitle>
        <CardDescription className="text-base">
          Your WhatsApp Business integration is ready to use
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium">WhatsApp Business Connected</span>
          </div>
          
          {selectedStore && (
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-blue-600" />
              <span className="font-medium">Store Connected: {selectedStore.storeName}</span>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-4">
            You can now start messaging customers and managing conversations
          </p>
          
          <Button
            onClick={() => onComplete?.({
              whatsapp: embeddedSignupData,
              store: selectedStore,
              connection: null, // ✅ Add required connection field
              complete: true
            })}
            size="lg"
            className="w-full"
          >
            Go to Dashboard
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  // ===== Main Render =====
  return (
    <div className={cn("max-w-2xl mx-auto", className)}>
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-center space-x-4">
          {[
            { step: 'embed', label: 'Connect WhatsApp', icon: MessageSquare },
            { step: 'store-connect', label: 'Connect Store', icon: Store },
            { step: 'complete', label: 'Complete', icon: CheckCircle }
          ].map((item, index) => {
            const Icon = item.icon;
            const isActive = currentStep === item.step;
            const isCompleted = 
              (item.step === 'embed' && ['store-connect', 'complete'].includes(currentStep)) ||
              (item.step === 'store-connect' && currentStep === 'complete');
            
            return (
              <div key={item.step} className="flex items-center">
                <div className={cn(
                  "flex items-center space-x-2 px-3 py-2 rounded-full transition-colors",
                  isActive && "bg-blue-100 text-blue-700",
                  isCompleted && "bg-green-100 text-green-700",
                  !isActive && !isCompleted && "bg-gray-100 text-gray-500"
                )}>
                  <Icon className="w-4 h-4" />
                  <span className="text-sm font-medium hidden sm:inline">{item.label}</span>
                </div>
                
                {index < 2 && (
                  <ArrowRight className={cn(
                    "w-4 h-4 mx-2",
                    isCompleted ? "text-green-600" : "text-gray-300"
                  )} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'embed' && renderEmbeddedSignupStep()}
      {currentStep === 'store-connect' && renderStoreConnectionStep()}
      {currentStep === 'complete' && renderCompleteStep()}
    </div>
  );
}; 