import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Plus, X, Phone, Mail, Building, MapPin, Calendar, Globe } from 'lucide-react';

interface ContactData {
  name: string;
  phoneNumber?: string;
  email?: string;
  organization?: string;
  address?: string;
  birthday?: string;
  url?: string;
}

interface ContactPickerProps {
  value: ContactData[];
  onChange: (contacts: ContactData[]) => void;
  disabled?: boolean;
}

export const ContactPicker: React.FC<ContactPickerProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [newContact, setNewContact] = useState<ContactData>({ 
    name: '',
    phoneNumber: '',
    email: '',
    organization: '',
    address: '',
    birthday: '',
    url: ''
  });

  const addContact = () => {
    if (!newContact.name.trim()) {
      return;
    }

    // Clean up empty fields
    const cleanContact: ContactData = {
      name: newContact.name.trim()
    };
    
    if (newContact.phoneNumber?.trim()) {
      cleanContact.phoneNumber = newContact.phoneNumber.trim();
    }
    if (newContact.email?.trim()) {
      cleanContact.email = newContact.email.trim();
    }
    if (newContact.organization?.trim()) {
      cleanContact.organization = newContact.organization.trim();
    }
    if (newContact.address?.trim()) {
      cleanContact.address = newContact.address.trim();
    }
    if (newContact.birthday?.trim()) {
      cleanContact.birthday = newContact.birthday.trim();
    }
    if (newContact.url?.trim()) {
      cleanContact.url = newContact.url.trim();
    }

    onChange([...value, cleanContact]);
    setNewContact({ 
      name: '',
      phoneNumber: '',
      email: '',
      organization: '',
      address: '',
      birthday: '',
      url: ''
    });
    setIsAdding(false);
  };

  const removeContact = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  const formatPhoneNumber = (phone: string) => {
    // Basic phone number formatting
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length >= 10) {
      return `+${cleaned}`;
    }
    return phone;
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateUrl = (url: string) => {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-gray-700">
        Share Contacts
      </div>

      {/* Selected Contacts */}
      {value.length > 0 && (
        <div className="space-y-3">
          {value.map((contact, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-2">
                  {/* Name */}
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{contact.name}</span>
                  </div>

                  {/* Contact details */}
                  <div className="space-y-1 pl-6">
                    {contact.phoneNumber && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="h-3 w-3" />
                        <span>{formatPhoneNumber(contact.phoneNumber)}</span>
                      </div>
                    )}
                    
                    {contact.email && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-3 w-3" />
                        <span>{contact.email}</span>
                      </div>
                    )}
                    
                    {contact.organization && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Building className="h-3 w-3" />
                        <span>{contact.organization}</span>
                      </div>
                    )}
                    
                    {contact.address && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <MapPin className="h-3 w-3" />
                        <span>{contact.address}</span>
                      </div>
                    )}
                    
                    {contact.birthday && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-3 w-3" />
                        <span>{contact.birthday}</span>
                      </div>
                    )}
                    
                    {contact.url && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Globe className="h-3 w-3" />
                        <a 
                          href={contact.url.startsWith('http') ? contact.url : `https://${contact.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          {contact.url}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* Summary badges */}
                  <div className="flex flex-wrap gap-1 pl-6">
                    {contact.phoneNumber && (
                      <Badge variant="secondary" className="text-xs">
                        Phone
                      </Badge>
                    )}
                    {contact.email && (
                      <Badge variant="secondary" className="text-xs">
                        Email
                      </Badge>
                    )}
                    {contact.organization && (
                      <Badge variant="secondary" className="text-xs">
                        Business
                      </Badge>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeContact(index)}
                  disabled={disabled}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Add New Contact Button */}
      {!isAdding && (
        <Button
          variant="outline"
          onClick={() => setIsAdding(true)}
          disabled={disabled}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      )}

      {/* Add Contact Form */}
      {isAdding && (
        <Card className="p-4 space-y-4">
          <div className="text-sm font-medium">Add New Contact</div>
          
          {/* Name (Required) */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Full Name *
            </label>
            <Input
              placeholder="John Doe"
              value={newContact.name}
              onChange={(e) => setNewContact(prev => ({ ...prev, name: e.target.value }))}
              disabled={disabled}
            />
          </div>

          {/* Phone Number */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Phone Number
            </label>
            <Input
              placeholder="+971501234567"
              value={newContact.phoneNumber}
              onChange={(e) => setNewContact(prev => ({ ...prev, phoneNumber: e.target.value }))}
              disabled={disabled}
            />
          </div>

          {/* Email */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Email Address
            </label>
            <Input
              placeholder="<EMAIL>"
              type="email"
              value={newContact.email}
              onChange={(e) => setNewContact(prev => ({ ...prev, email: e.target.value }))}
              disabled={disabled}
            />
            {newContact.email && !validateEmail(newContact.email) && (
              <p className="text-xs text-red-500 mt-1">Please enter a valid email address</p>
            )}
          </div>

          {/* Organization */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Organization
            </label>
            <Input
              placeholder="Acme Corp"
              value={newContact.organization}
              onChange={(e) => setNewContact(prev => ({ ...prev, organization: e.target.value }))}
              disabled={disabled}
            />
          </div>

          {/* Address */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Address
            </label>
            <Input
              placeholder="123 Main St, City, Country"
              value={newContact.address}
              onChange={(e) => setNewContact(prev => ({ ...prev, address: e.target.value }))}
              disabled={disabled}
            />
          </div>

          {/* Birthday */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Birthday
            </label>
            <Input
              placeholder="1990-01-01 or January 1, 1990"
              value={newContact.birthday}
              onChange={(e) => setNewContact(prev => ({ ...prev, birthday: e.target.value }))}
              disabled={disabled}
            />
          </div>

          {/* Website/URL */}
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Website
            </label>
            <Input
              placeholder="https://example.com"
              value={newContact.url}
              onChange={(e) => setNewContact(prev => ({ ...prev, url: e.target.value }))}
              disabled={disabled}
            />
            {newContact.url && !validateUrl(newContact.url) && (
              <p className="text-xs text-red-500 mt-1">Please enter a valid URL</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex gap-2">
            <Button
              onClick={addContact}
              disabled={
                !newContact.name.trim() || 
                disabled ||
                (!!newContact.email && !validateEmail(newContact.email)) ||
                (!!newContact.url && !validateUrl(newContact.url))
              }
              className="flex-1"
            >
              Add Contact
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                setIsAdding(false);
                setNewContact({ 
                  name: '',
                  phoneNumber: '',
                  email: '',
                  organization: '',
                  address: '',
                  birthday: '',
                  url: ''
                });
              }}
              disabled={disabled}
            >
              Cancel
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            <p>Only the name is required. Add additional details as needed.</p>
          </div>
        </Card>
      )}

      {/* Instructions */}
      {value.length === 0 && !isAdding && (
        <div className="text-center p-6 text-gray-500">
          <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No contacts selected</p>
          <p className="text-xs">Add contacts to share them via WhatsApp</p>
        </div>
      )}
    </div>
  );
}; 