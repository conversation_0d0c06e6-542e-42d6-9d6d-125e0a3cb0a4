import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { MapPin, Navigation, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export interface LocationData {
  latitude: number;
  longitude: number;
  name?: string;
  address?: string;
}

interface LocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLocationSelect: (location: LocationData) => void;
}

export const LocationDialog: React.FC<LocationDialogProps> = ({
  open,
  onOpenChange,
  onLocationSelect
}) => {
  const [location, setLocation] = useState<LocationData>({
    latitude: 0,
    longitude: 0,
    name: '',
    address: ''
  });
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const { toast } = useToast();

  const getCurrentLocation = useCallback(() => {
    if (!navigator.geolocation) {
      toast({
        title: "Geolocation not supported",
        description: "Your browser doesn't support geolocation",
        variant: "destructive",
      });
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setLocation(prev => ({
          ...prev,
          latitude,
          longitude
        }));
        
        // Try to get address from coordinates using reverse geocoding
        reverseGeocode(latitude, longitude);
        setIsGettingLocation(false);
        
        toast({
          title: "✅ Location detected",
          description: "Current location has been detected",
        });
      },
      (error) => {
        setIsGettingLocation(false);
        let errorMessage = "Failed to get location";
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access denied by user";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out";
            break;
        }
        
        toast({
          title: "Location Error",
          description: errorMessage,
          variant: "destructive",
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  }, [toast]);

  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      // Using a simple reverse geocoding service (in production, you might want to use Google Maps or similar)
      const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`);
      const data = await response.json();
      
      if (data && data.locality) {
        setLocation(prev => ({
          ...prev,
          name: data.locality,
          address: data.display_name || `${data.locality}, ${data.countryName}`
        }));
      }
    } catch (error) {
      console.log('Reverse geocoding failed, user can enter manually');
    }
  };

  const handleSend = () => {
    if (!location.latitude || !location.longitude) {
      toast({
        title: "Invalid location",
        description: "Please enter valid coordinates or use current location",
        variant: "destructive",
      });
      return;
    }

    onLocationSelect(location);
    onOpenChange(false);
    
    // Reset form
    setLocation({
      latitude: 0,
      longitude: 0,
      name: '',
      address: ''
    });
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    setLocation({
      latitude: 0,
      longitude: 0,
      name: '',
      address: ''
    });
  };

  const isValid = location.latitude !== 0 && location.longitude !== 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-orange-600" />
            Share Location
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Current Location Button */}
          <div className="flex flex-col space-y-2">
            <Button
              onClick={getCurrentLocation}
              disabled={isGettingLocation}
              className="w-full h-12 bg-orange-600 hover:bg-orange-700 text-white"
            >
              {isGettingLocation ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Getting location...
                </>
              ) : (
                <>
                  <Navigation className="h-4 w-4 mr-2" />
                  Use Current Location
                </>
              )}
            </Button>
            <p className="text-xs text-gray-500 text-center">
              Allow location access to automatically detect your position
            </p>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500">Or enter manually</span>
            </div>
          </div>

          {/* Manual Entry */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="latitude" className="text-sm font-medium">
                Latitude
              </Label>
              <Input
                id="latitude"
                type="number"
                step="any"
                placeholder="0.000000"
                value={location.latitude || ''}
                onChange={(e) => setLocation(prev => ({
                  ...prev,
                  latitude: parseFloat(e.target.value) || 0
                }))}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="longitude" className="text-sm font-medium">
                Longitude
              </Label>
              <Input
                id="longitude"
                type="number"
                step="any"
                placeholder="0.000000"
                value={location.longitude || ''}
                onChange={(e) => setLocation(prev => ({
                  ...prev,
                  longitude: parseFloat(e.target.value) || 0
                }))}
                className="mt-1"
              />
            </div>
          </div>

          {/* Optional Details */}
          <div>
            <Label htmlFor="location-name" className="text-sm font-medium">
              Place Name (Optional)
            </Label>
            <Input
              id="location-name"
              placeholder="e.g., Home, Office, Restaurant"
              value={location.name}
              onChange={(e) => setLocation(prev => ({
                ...prev,
                name: e.target.value
              }))}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="location-address" className="text-sm font-medium">
              Address (Optional)
            </Label>
            <Textarea
              id="location-address"
              placeholder="Full address or description"
              value={location.address}
              onChange={(e) => setLocation(prev => ({
                ...prev,
                address: e.target.value
              }))}
              className="mt-1 resize-none"
              rows={2}
            />
          </div>

          {/* Location Preview */}
          {isValid && (
            <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {location.name || 'Shared Location'}
                  </p>
                  <p className="text-xs text-gray-600">
                    {location.address || `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleSend}
            disabled={!isValid}
            className="bg-orange-600 hover:bg-orange-700 text-white"
          >
            Send Location
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 