import { useState, useCallback, useRef, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { executeSubscription } from '@/lib/graphql-client';
import { ON_MEDIA_UPLOAD_STATUS_CHANGED } from '@/lib/graphql-operations';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { uploadFileToS3 } from '@/lib/s3/upload';
import { 
  SEND_MESSAGE, 
  SendMessageInput, 
  SendMessageResponse 
} from '@/lib/graphql/mutations/sendMessage';
import { 
  Plus, 
  Send, 
  Smile,
  Mic,
  Image,
  Loader2,
  CheckCircle,
  X,
  FileImage,
  Upload,
  ZoomIn
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface MessageComposerProps {
  conversationId: string;
  onMessageSent?: (message: any) => void;
  disabled?: boolean;
  className?: string;
}

// Enhanced media upload status tracking
interface MediaUpload {
  s3Key: string;
  fileName: string;
  mediaId?: string;
  status: 'UPLOADING' | 'PROCESSING' | 'READY' | 'FAILED';
  fileSize: number;
  mediaType: string; // Store the actual file MIME type
  progress?: number;
  previewUrl?: string;
}

export const MessageComposer: React.FC<MessageComposerProps> = ({
  conversationId,
  onMessageSent,
  disabled = false,
  className = ""
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // State
  const [textContent, setTextContent] = useState('');
  const [sending, setSending] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaUpload, setMediaUpload] = useState<MediaUpload | null>(null);
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  
  // Initialize Amplify GraphQL client
  const client = generateClient();

  // Subscribe to media upload status updates
  useEffect(() => {
    if (!mediaUpload || mediaUpload.status === 'UPLOADING') return;

    console.log('🎬 Setting up media upload subscription for conversation:', conversationId);
    
    const subscription = executeSubscription(
      {
        query: ON_MEDIA_UPLOAD_STATUS_CHANGED,
        variables: { conversationId }
      },
      {
        next: (data: any) => {
          console.log('🎬 Media upload status update:', data);
          const update = data.onMediaUploadStatusChanged;
          console.log(`🔍 Comparing s3Keys - Frontend: "${mediaUpload.s3Key}" vs Backend: "${update?.s3Key}"`);
          
          // Match by filename since s3Key formats may differ (uploads/ vs public/uploads/)
          const frontendFilename = mediaUpload.s3Key.split('/').pop();
          const backendFilename = update?.s3Key.split('/').pop();
          
          if (update && frontendFilename && backendFilename && frontendFilename === backendFilename) {
            setMediaUpload(prev => prev ? {
              ...prev,
              status: update.status, // Keep backend status format (uppercase)
              mediaId: update.mediaId || prev.mediaId,
              fileName: update.fileName || prev.fileName,
              fileSize: update.fileSize !== undefined ? update.fileSize : prev.fileSize // Preserve original fileSize if not provided
            } : null);
            
            console.log(`🎬 Updated media upload status to: ${update.status}`, update);
            
            // Show success toast when ready
            if (update.status === 'READY') {
              console.log('🎉 Image is READY! Showing toast...');
              toast({
                title: "✅ Image Ready",
                description: "Your image is ready to send!",
              });
            }
          }
        },
        error: (error: any) => {
          console.error('Media upload subscription error:', error);
          toast({
            title: "Connection Error",
            description: "Lost connection to upload updates",
            variant: "destructive",
          });
        }
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, [mediaUpload?.s3Key, conversationId, toast]);

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
      textarea.style.height = newHeight + 'px';
    }
  }, []);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user?.organizationId) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please select an image file (PNG, JPG, etc.)",
        variant: "destructive",
      });
      return;
    }

    if (file.size > 16 * 1024 * 1024) { // 16MB limit
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 16MB",
        variant: "destructive",
      });
      return;
    }

    // Extract phoneNumberId from conversationId (format: orgId#phoneNumberId#customerPhone)
    const conversationParts = conversationId.split('#');
    const phoneNumberId = conversationParts.length >= 2 ? conversationParts[1] : '';
    
    if (!phoneNumberId) {
      toast({
        title: "Upload Failed",
        description: "Could not determine phone number ID from conversation.",
        variant: "destructive",
      });
      return;
    }

    // Create preview URL for internal use
    const previewUrl = URL.createObjectURL(file);

    // Set initial upload state - no preview shown until READY
    setMediaUpload({
      s3Key: '', // Will be set after upload
      fileName: file.name,
      mediaType: file.type, // Store actual MIME type (e.g., 'image/png', 'image/jpeg')
      status: 'UPLOADING',
      fileSize: file.size,
      progress: 0,
      previewUrl // Store it but UI will only show when status is READY
    });

    toast({
      title: "📤 Uploading Image",
      description: "Preparing your image for upload...",
    });
    
    const uploadResult = await uploadFileToS3(file, user.organizationId, conversationId, phoneNumberId, (progress) => {
      console.log(`Upload progress:`, progress);
      if (progress && progress.loaded !== undefined && progress.total !== undefined) {
        const percentage = Math.round((progress.loaded / progress.total) * 100);
        setMediaUpload(prev => prev ? {
          ...prev,
          progress: percentage
        } : null);
      }
    });

    if (uploadResult.success && uploadResult.key) {
      // Update to processing state
      setMediaUpload(prev => prev ? {
        ...prev,
        s3Key: uploadResult.key!,
        status: 'PROCESSING',
        progress: 100
      } : null);

      toast({
        title: "🔄 Processing Image",
        description: "Optimizing your image for WhatsApp...",
      });
    } else {
      // Clean up preview URL
      URL.revokeObjectURL(previewUrl);
      setMediaUpload(null);
      
      toast({
        title: "Upload Failed",
        description: "Could not upload the image. Please try again.",
        variant: "destructive",
      });
    }
    
    // Reset file input to allow re-uploading the same file
    if(fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleRemoveMedia = useCallback(() => {
    if (mediaUpload?.previewUrl) {
      URL.revokeObjectURL(mediaUpload.previewUrl);
    }
    setMediaUpload(null);
    toast({
      title: "Media Removed",
      description: "Image removed from message",
    });
  }, [mediaUpload, toast]);

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextContent(e.target.value);
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  const handleSend = useCallback(async () => {
    const hasText = textContent.trim().length > 0;
    const hasMedia = mediaUpload?.s3Key && (mediaUpload.status === 'READY' || mediaUpload.status === 'PROCESSING'); // Allow sending when S3 upload is complete

    if ((!hasText && !hasMedia) || !user?.organizationId || sending) return;
    
    setSending(true);
    
    try {
      const input: SendMessageInput = {
        conversationId,
        organizationId: user.organizationId,
        messageType: hasMedia ? 'IMAGE' : 'TEXT',
        text: hasText ? textContent.trim() : undefined,
        mediaUrl: hasMedia ? mediaUpload.s3Key : undefined, // Use S3 key instead of mediaId
        mediaType: hasMedia ? mediaUpload.mediaType : undefined, // Use actual file MIME type
        fileName: hasMedia ? mediaUpload.fileName : undefined,
        fileSize: hasMedia ? mediaUpload.fileSize : undefined,
        caption: hasMedia && hasText ? textContent.trim() : undefined,
      };

      if(hasMedia && hasText) {
        input.text = undefined; // Caption is used instead of text for media messages
      }
      
      const result = await client.graphql({
        query: SEND_MESSAGE,
        variables: { input }
      });
      
      const response = (result as any).data?.sendMessage as SendMessageResponse;
      
      if (response?.success) {
        setTextContent('');
        
        // Clean up media upload
        if (mediaUpload?.previewUrl) {
          URL.revokeObjectURL(mediaUpload.previewUrl);
        }
        setMediaUpload(null);
        
        if (textareaRef.current) {
          textareaRef.current.style.height = '40px';
        }
        onMessageSent?.(response.message);
        
        toast({
          title: "✅ Message Sent",
          description: "Your message has been delivered",
        });
      } else {
        toast({
          title: "Failed to send message",
          description: response?.error?.message || 'Failed to send message',
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error?.message || 'An unexpected error occurred',
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  }, [textContent, user?.organizationId, conversationId, client, onMessageSent, toast, sending, mediaUpload]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  const handleVoiceRecord = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      toast({
        title: "Voice recording stopped",
        description: "Voice message functionality coming soon",
      });
    } else {
      // Start recording
      setIsRecording(true);
      toast({
        title: "Voice recording started",
        description: "Voice message functionality coming soon",
      });
    }
  }, [isRecording, toast]);

  const canSend = !sending && !disabled && user?.organizationId && 
                  (textContent.trim().length > 0 || (mediaUpload?.s3Key && (mediaUpload.status === 'READY' || mediaUpload.status === 'PROCESSING')));
  const showMicButton = !textContent.trim() && !sending && !mediaUpload;
  const isUploading = mediaUpload?.status === 'UPLOADING';

  const getAttachButtonIcon = () => {
    // Only show static icons, no spinners in attach button
    if (mediaUpload?.status === 'READY') return <CheckCircle className="h-6 w-6 text-green-600" />;
    if (mediaUpload) return <Image className="h-6 w-6" />; // Static image icon for upload states
    return <Image className="h-6 w-6" />;
  };

  const getAttachButtonTitle = () => {
    if (mediaUpload?.status === 'UPLOADING') return `Uploading... ${mediaUpload?.progress || 0}%`;
    if (mediaUpload?.status === 'PROCESSING') return "Processing image...";
    if (mediaUpload?.status === 'READY') return "Image ready to send";
    return "Attach image";
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusDisplay = () => {
    if (!mediaUpload) return null;

    console.log(`🎨 Rendering status display for: ${mediaUpload.status}`);
    switch (mediaUpload.status) {
      case 'UPLOADING':
        return (
          <div className="flex items-center gap-2 text-blue-600">
            <span>Uploading... {mediaUpload.progress || 0}%</span>
          </div>
        );
      case 'PROCESSING':
        return (
          <div className="flex items-center gap-2 text-orange-600">
            <span>Processing for WhatsApp...</span>
          </div>
        );
      case 'READY':
        return (
          <div className="flex items-center gap-2 text-green-600">
            <span>Ready to send!</span>
          </div>
        );
      default:
        return null;
    }
  };

  const handleImagePreviewClick = useCallback((url: string) => {
    // Allow preview at any time once thumbnail is available
    setPreviewImageUrl(url);
    setIsImagePreviewOpen(true);
  }, []);

  const handleCloseImagePreview = useCallback(() => {
    setIsImagePreviewOpen(false);
    setPreviewImageUrl(null);
  }, []);

  return (
    <div className={`bg-white border-t border-gray-200 ${className}`}>
      {/* Enhanced Media Preview */}
      {mediaUpload && (
        <div className="px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
          <div className="flex items-start gap-3">
            {/* Media thumbnail */}
            <div className="relative flex-shrink-0">
              <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-200">
                {mediaUpload.previewUrl && (mediaUpload.status === 'READY' || mediaUpload.status === 'PROCESSING') ? (
                  <div className="relative w-full h-full cursor-pointer group" onClick={() => handleImagePreviewClick(mediaUpload.previewUrl!)}>
                    <img 
                      src={mediaUpload.previewUrl} 
                      alt={mediaUpload.fileName}
                      className="w-full h-full object-cover transition-opacity group-hover:opacity-75"
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <ZoomIn className="h-5 w-5 text-white drop-shadow-lg" />
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <FileImage className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </div>
              
              {/* Status overlay - only show for uploading/processing */}
              {mediaUpload.status === 'UPLOADING' && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                  <Upload className="h-6 w-6 text-white animate-bounce" />
                </div>
              )}
              {mediaUpload.status === 'PROCESSING' && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                  <Loader2 className="h-6 w-6 text-white animate-spin" />
                </div>
              )}
              {mediaUpload.status === 'READY' && (
                <div className="absolute -top-1 -right-1 bg-green-600 rounded-full p-1">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              )}
            </div>

            {/* Media info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div>
                  <p className="font-medium text-gray-900 truncate">{mediaUpload.fileName}</p>
                  <p className="text-sm text-gray-500">{formatFileSize(mediaUpload.fileSize)}</p>
                  <div className="mt-1">
                    {getStatusDisplay()}
                  </div>
                </div>
                
                {/* Remove button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveMedia}
                  disabled={mediaUpload.status === 'UPLOADING'}
                  className="p-1 h-8 w-8 text-gray-400 hover:text-red-500 hover:bg-red-50"
                  title="Remove image"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Progress bar for uploading only */}
              {mediaUpload.status === 'UPLOADING' && mediaUpload.progress !== undefined && (
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${mediaUpload.progress}%` }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex items-end gap-3 px-4 py-3">
        {/* Attachment Input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept="image/*"
        />
        
        {/* Attachment Button */}
        <Button 
          variant="ghost" 
          size="sm"
          className={`h-10 w-10 p-0 hover:bg-gray-100 rounded-full flex-shrink-0 transition-all ${
            mediaUpload?.status === 'READY' 
              ? 'text-green-600 hover:text-green-700' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          disabled={sending || mediaUpload?.status === 'UPLOADING' || mediaUpload?.status === 'PROCESSING'}
          onClick={handleAttachClick}
          title={getAttachButtonTitle()}
        >
          {getAttachButtonIcon()}
        </Button>

        {/* Text Input Container */}
        <div className="flex-1 relative bg-gray-50 rounded-[18px] border border-gray-200 min-h-[40px] flex items-center">
          <textarea
            ref={textareaRef}
            value={textContent}
            onChange={handleTextChange}
            onKeyDown={handleKeyPress}
            placeholder={
              mediaUpload?.status === 'READY' 
                ? "Add a caption..." 
                : mediaUpload?.status === 'PROCESSING'
                ? "Processing image..."
                : mediaUpload?.status === 'UPLOADING'
                ? "Uploading image..."
                : "Type a message"
            }
            disabled={sending || mediaUpload?.status === 'UPLOADING'}
            className="w-full bg-transparent px-4 py-2.5 pr-10 text-[15px] resize-none focus:outline-none placeholder:text-gray-500 disabled:opacity-50 leading-5"
            style={{ 
              height: '40px',
              minHeight: '40px',
              maxHeight: '120px'
            }}
            rows={1}
          />
          
          {/* Emoji Button */}
          <Button 
            variant="ghost" 
            size="sm"
            className="absolute right-3 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-200 text-gray-600 hover:text-gray-700 rounded-full flex-shrink-0"
            disabled={sending || mediaUpload?.status === 'UPLOADING'}
            title="Insert emoji"
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        {/* Microphone or Send Button */}
        {showMicButton ? (
          <Button 
            onClick={handleVoiceRecord}
            size="sm"
            className={`h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all ${
              isRecording 
                ? 'bg-red-600 hover:bg-red-700 text-white' 
                : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
            }`}
            title="Record voice message"
          >
            <Mic className={`h-5 w-5 ${isRecording ? 'animate-pulse' : ''}`} />
          </Button>
        ) : (
          <Button 
            onClick={handleSend}
            disabled={!canSend}
            size="sm"
            className={`h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all ${
              canSend 
                ? 'bg-green-600 hover:bg-green-700 text-white' 
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
            title="Send message"
          >
            {sending ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        )}
      </div>

             {/* Image Preview Modal */}
       <div className="[&_.fixed]:!z-[9999]">
         <Dialog open={isImagePreviewOpen} onOpenChange={handleCloseImagePreview}>
           <DialogContent className="max-w-4xl max-h-[90vh] p-0">
           <DialogHeader className="p-6 pb-0">
             <DialogTitle className="text-lg font-semibold">
               {mediaUpload?.fileName || 'Image Preview'}
             </DialogTitle>
           </DialogHeader>
           <div className="p-6 flex items-center justify-center">
             <img 
               src={previewImageUrl || ''} 
               alt="Preview" 
               className="max-w-full max-h-[70vh] object-contain rounded-lg"
             />
           </div>
         </DialogContent>
       </Dialog>
       </div>
    </div>
  );
}; 