import React, { useState, useEffect } from 'react';
import { Search, MessageCircle, User, Phone } from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useCRM } from '@/hooks/useCRM';
import { Customer } from '@/lib/graphql/queries/crmQueries';

import { PhoneUtils } from '@/lib/utils';

interface StartConversationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onStartConversation: (customer: Customer, businessPhone: string) => void;
  businessPhone?: string; // Pre-selected business phone
}

export const StartConversationDialog: React.FC<StartConversationDialogProps> = ({
  isOpen,
  onClose,
  onStartConversation,
  businessPhone
}) => {
  const { customers, loading, searchCustomers, clearFilters } = useCRM();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Filter customers by business phone if provided
  const filteredCustomers = businessPhone 
    ? customers.filter(customer => 
        customer.display_phone_number === businessPhone
      )
    : customers;

  // Handle search input
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      searchCustomers(query);
    } else {
      clearFilters();
    }
  };

  // Handle customer selection
  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  // Start conversation with selected customer
  const handleStartConversation = () => {
    if (selectedCustomer) {
      onStartConversation(selectedCustomer, selectedCustomer.display_phone_number || '');
      onClose();
      setSelectedCustomer(null);
      setSearchQuery('');
    }
  };

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedCustomer(null);
      setSearchQuery('');
      clearFilters();
    }
  }, [isOpen, clearFilters]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Start New Conversation
          </DialogTitle>
          <DialogDescription>
            Select a customer from your CRM to start a new WhatsApp conversation
            {businessPhone && (
              <span className="block mt-1 text-sm">
                Business Phone: <Badge variant="outline">{businessPhone}</Badge>
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-4 flex-1 overflow-hidden">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search customers by name, phone, or tags..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Customer List */}
          <div className="flex-1 overflow-y-auto border rounded-lg">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-gray-500">Loading customers...</div>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                <User className="h-12 w-12 mb-2 opacity-50" />
                <div className="text-center">
                  <p className="font-medium">No customers found</p>
                  <p className="text-sm mt-1">
                    {searchQuery ? 'Try adjusting your search terms' : 'No customers in your CRM yet'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="p-2 space-y-1">
                {filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => handleCustomerSelect(customer)}
                    className={`p-3 rounded-lg cursor-pointer transition-colors border ${
                      selectedCustomer?.id === customer.id
                        ? 'bg-blue-50 border-blue-200 ring-1 ring-blue-200'
                        : 'hover:bg-gray-50 border-transparent'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                          {(customer.customer_name || 'U').charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            {customer.customer_name}
                          </h4>
                          <Badge 
                            variant={customer.conversation_status === 'active' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {customer.conversation_status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            <span>{PhoneUtils.formatForDisplay(customer.customer_phone)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageCircle className="h-3 w-3" />
                            <span>{customer.message_count} messages</span>
                          </div>
                        </div>
                        
                        {customer.tags && customer.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {customer.tags.slice(0, 3).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {customer.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{customer.tags.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            
            <Button 
              onClick={handleStartConversation}
              disabled={!selectedCustomer}
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              Start Conversation
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 