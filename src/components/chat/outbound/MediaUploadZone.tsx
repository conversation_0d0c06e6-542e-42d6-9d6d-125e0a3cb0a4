import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, FileText, Mic, Play, Image as ImageIcon, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

type MessageType = 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT';

interface MediaUploadZoneProps {
  acceptedTypes: MessageType;
  onFileSelected: (file: File) => void;
  onUploadComplete: (url: string) => void;
  onUploadStart: () => void;
  disabled?: boolean;
}

export const MediaUploadZone: React.FC<MediaUploadZoneProps> = ({
  acceptedTypes,
  onFileSelected,
  onUploadComplete,
  onUploadStart,
  disabled = false
}) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const getAcceptedFileTypes = (): Record<string, string[]> => {
    switch (acceptedTypes) {
      case 'IMAGE': 
        return { 'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp'] };
      case 'VIDEO': 
        return { 'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm'] };
      case 'AUDIO': 
        return { 'audio/*': ['.mp3', '.wav', '.ogg', '.m4a', '.aac'] };
      case 'DOCUMENT': 
        return { 
          'application/pdf': ['.pdf'],
          'application/msword': ['.doc'],
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
          'text/plain': ['.txt']
        };
      default: 
        return {};
    }
  };

  const getMaxFileSize = () => {
    switch (acceptedTypes) {
      case 'IMAGE': return 10 * 1024 * 1024; // 10MB
      case 'VIDEO': return 100 * 1024 * 1024; // 100MB
      case 'AUDIO': return 20 * 1024 * 1024; // 20MB
      case 'DOCUMENT': return 25 * 1024 * 1024; // 25MB
      default: return 10 * 1024 * 1024;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      let errorMessage = 'File upload failed';
      
      if (rejection.errors.some((e: any) => e.code === 'file-too-large')) {
        errorMessage = `File is too large. Maximum size is ${formatFileSize(getMaxFileSize())}`;
      } else if (rejection.errors.some((e: any) => e.code === 'file-invalid-type')) {
        errorMessage = `Invalid file type. Please select a ${acceptedTypes.toLowerCase()} file.`;
      }
      
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    const file = acceptedFiles[0];
    if (!file) return;

    setSelectedFile(file);
    onFileSelected(file);

    // Create preview for images and videos
    if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }

    // Start upload simulation
    onUploadStart();
    simulateUpload(file);
  }, [acceptedTypes, onFileSelected, onUploadStart, toast]);

  const simulateUpload = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 20) {
        setUploadProgress(progress);
        await new Promise(resolve => setTimeout(resolve, 150));
      }
      
      // Mock S3 URL - in real implementation, upload to S3 via Amplify Storage
      const mockS3Url = `s3://notefy-media/${Date.now()}-${file.name}`;
      onUploadComplete(mockS3Url);
      
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedFileTypes(),
    maxFiles: 1,
    maxSize: getMaxFileSize(),
    disabled: disabled || isUploading
  });

  const clearFile = () => {
    setSelectedFile(null);
    setPreviewUrl('');
    setUploadProgress(0);
    setIsUploading(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const getFileIcon = () => {
    switch (acceptedTypes) {
      case 'IMAGE': return ImageIcon;
      case 'VIDEO': return Play;
      case 'AUDIO': return Mic;
      case 'DOCUMENT': return FileText;
      default: return Upload;
    }
  };

  const FileIcon = getFileIcon();
  const isComplete = uploadProgress === 100 && !isUploading;

  // If file is uploaded, show compact preview
  if (selectedFile && isComplete) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-3">
        <div className="flex items-center gap-3">
          {/* Preview/Icon */}
          <div className="flex-shrink-0">
            {previewUrl && acceptedTypes === 'IMAGE' ? (
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="w-12 h-12 object-cover rounded-lg"
              />
            ) : (
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <FileIcon className="h-6 w-6 text-gray-500" />
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <p className="font-medium text-sm truncate" title={selectedFile.name}>
              {selectedFile.name}
            </p>
            <p className="text-xs text-gray-500">
              {formatFileSize(selectedFile.size)}
            </p>
          </div>

          {/* Status */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-xs font-medium">Ready</span>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearFile}
              className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If uploading, show progress
  if (selectedFile && isUploading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-3">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
            <FileIcon className="h-6 w-6 text-gray-500" />
          </div>
          
          <div className="flex-1">
            <p className="font-medium text-sm truncate">{selectedFile.name}</p>
            <div className="flex items-center gap-2 mt-1">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
              <span className="text-xs text-gray-500">{uploadProgress}%</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Upload zone
  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
        isDragActive 
          ? 'border-green-400 bg-green-50' 
          : 'border-gray-300 hover:border-gray-400'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <input {...getInputProps()} />
      
      <div className="flex flex-col items-center gap-2">
        <div className={`p-3 rounded-full ${
          isDragActive ? 'bg-green-100' : 'bg-gray-100'
        }`}>
          <FileIcon className={`h-6 w-6 ${
            isDragActive ? 'text-green-600' : 'text-gray-400'
          }`} />
        </div>
        
        <div>
          <p className="text-sm font-medium text-gray-700">
            {isDragActive 
              ? `Drop your ${acceptedTypes.toLowerCase()} here` 
              : `Choose ${acceptedTypes.toLowerCase()}`
            }
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Max {formatFileSize(getMaxFileSize())}
          </p>
        </div>
      </div>
    </div>
  );
}; 