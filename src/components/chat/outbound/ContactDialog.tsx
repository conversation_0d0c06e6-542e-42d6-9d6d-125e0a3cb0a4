import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Users, Phone, Mail, Building, Calendar, Globe, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export interface ContactData {
  name: string;
  phoneNumber?: string;
  email?: string;
  organization?: string;
  address?: string;
  birthday?: string;
  url?: string;
}

interface ContactDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContactSelect: (contact: ContactData) => void;
}

export const ContactDialog: React.FC<ContactDialogProps> = ({
  open,
  onOpenChange,
  onContactSelect
}) => {
  const [contact, setContact] = useState<ContactData>({
    name: '',
    phoneNumber: '',
    email: '',
    organization: '',
    address: '',
    birthday: '',
    url: ''
  });
  const { toast } = useToast();

  const handleSend = () => {
    if (!contact.name.trim()) {
      toast({
        title: "Contact name required",
        description: "Please enter a contact name",
        variant: "destructive",
      });
      return;
    }

    // Clean up empty fields
    const cleanContact: ContactData = {
      name: contact.name.trim(),
      ...(contact.phoneNumber?.trim() && { phoneNumber: contact.phoneNumber.trim() }),
      ...(contact.email?.trim() && { email: contact.email.trim() }),
      ...(contact.organization?.trim() && { organization: contact.organization.trim() }),
      ...(contact.address?.trim() && { address: contact.address.trim() }),
      ...(contact.birthday?.trim() && { birthday: contact.birthday.trim() }),
      ...(contact.url?.trim() && { url: contact.url.trim() })
    };

    onContactSelect(cleanContact);
    onOpenChange(false);
    
    // Reset form
    setContact({
      name: '',
      phoneNumber: '',
      email: '',
      organization: '',
      address: '',
      birthday: '',
      url: ''
    });
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form
    setContact({
      name: '',
      phoneNumber: '',
      email: '',
      organization: '',
      address: '',
      birthday: '',
      url: ''
    });
  };

  const isValid = contact.name.trim().length > 0;

  /*
  const formatPhoneNumber = (value: string) => {
    // Simple phone number formatting - remove non-digits and add basic formatting
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `${digits.slice(0, 3)}-${digits.slice(3)}`;
    if (digits.length <= 10) return `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6)}`;
    return `+${digits.slice(0, -10)} ${digits.slice(-10, -7)}-${digits.slice(-7, -4)}-${digits.slice(-4)}`;
  };
  */

  const handlePhoneChange = (value: string) => {
    setContact(prev => ({
      ...prev,
      phoneNumber: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-indigo-600" />
            Share Contact
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Required Fields */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="contact-name" className="text-sm font-medium flex items-center gap-1">
                <User className="h-4 w-4" />
                Name *
              </Label>
              <Input
                id="contact-name"
                placeholder="Full name"
                value={contact.name}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  name: e.target.value
                }))}
                className="mt-1"
                required
              />
            </div>
          </div>

          {/* Optional Fields */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="contact-phone" className="text-sm font-medium flex items-center gap-1">
                <Phone className="h-4 w-4" />
                Phone Number
              </Label>
              <Input
                id="contact-phone"
                type="tel"
                placeholder="******-567-8900"
                value={contact.phoneNumber}
                onChange={(e) => handlePhoneChange(e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="contact-email" className="text-sm font-medium flex items-center gap-1">
                <Mail className="h-4 w-4" />
                Email
              </Label>
              <Input
                id="contact-email"
                type="email"
                placeholder="<EMAIL>"
                value={contact.email}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  email: e.target.value
                }))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="contact-organization" className="text-sm font-medium flex items-center gap-1">
                <Building className="h-4 w-4" />
                Organization
              </Label>
              <Input
                id="contact-organization"
                placeholder="Company or organization"
                value={contact.organization}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  organization: e.target.value
                }))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="contact-address" className="text-sm font-medium">
                Address
              </Label>
              <Textarea
                id="contact-address"
                placeholder="Full address"
                value={contact.address}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  address: e.target.value
                }))}
                className="mt-1 resize-none"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="contact-birthday" className="text-sm font-medium flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Birthday
              </Label>
              <Input
                id="contact-birthday"
                type="date"
                value={contact.birthday}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  birthday: e.target.value
                }))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="contact-url" className="text-sm font-medium flex items-center gap-1">
                <Globe className="h-4 w-4" />
                Website
              </Label>
              <Input
                id="contact-url"
                type="url"
                placeholder="https://example.com"
                value={contact.url}
                onChange={(e) => setContact(prev => ({
                  ...prev,
                  url: e.target.value
                }))}
                className="mt-1"
              />
            </div>
          </div>

          {/* Contact Preview */}
          {isValid && (
            <div className="p-4 bg-indigo-50 rounded-lg border border-indigo-200">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-indigo-100 rounded-full">
                  <User className="h-5 w-5 text-indigo-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900">{contact.name}</p>
                  
                  {contact.phoneNumber && (
                    <div className="flex items-center gap-1 mt-1">
                      <Phone className="h-3 w-3 text-gray-400" />
                      <p className="text-xs text-gray-600">{contact.phoneNumber}</p>
                    </div>
                  )}
                  
                  {contact.email && (
                    <div className="flex items-center gap-1 mt-1">
                      <Mail className="h-3 w-3 text-gray-400" />
                      <p className="text-xs text-gray-600">{contact.email}</p>
                    </div>
                  )}
                  
                  {contact.organization && (
                    <div className="flex items-center gap-1 mt-1">
                      <Building className="h-3 w-3 text-gray-400" />
                      <p className="text-xs text-gray-600">{contact.organization}</p>
                    </div>
                  )}

                  {contact.url && (
                    <div className="flex items-center gap-1 mt-1">
                      <Globe className="h-3 w-3 text-gray-400" />
                      <p className="text-xs text-gray-600 truncate">{contact.url}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <p className="text-xs text-gray-500">
            * Required fields. Contact will be shared in vCard format compatible with most devices.
          </p>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleSend}
            disabled={!isValid}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            Send Contact
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 