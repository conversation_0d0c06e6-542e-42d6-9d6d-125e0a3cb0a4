import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { MapPin, Navigation, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface LocationData {
  latitude: number;
  longitude: number;
  name: string;
  address: string;
}

interface LocationPickerProps {
  value: LocationData | null;
  onChange: (location: LocationData | null) => void;
  disabled?: boolean;
}

export const LocationPicker: React.FC<LocationPickerProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  const [manualEntry, setManualEntry] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [manualData, setManualData] = useState({
    latitude: '',
    longitude: '',
    name: '',
    address: ''
  });
  const { toast } = useToast();

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast({
        title: "Geolocation not supported",
        description: "Your browser doesn't support geolocation. Please enter coordinates manually.",
        variant: "destructive",
      });
      setManualEntry(true);
      return;
    }

    setIsGettingLocation(true);
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        
        try {
          // Try to get address from coordinates using reverse geocoding
          // In a real app, you'd use a geocoding service like Google Maps API
          const location: LocationData = {
            latitude,
            longitude,
            name: 'Current Location',
            address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          };
          
          onChange(location);
          
          toast({
            title: "Location captured",
            description: "Your current location has been set",
          });
        } catch (error) {
          console.error('Error getting address:', error);
          
          // Fallback without address lookup
          const location: LocationData = {
            latitude,
            longitude,
            name: 'Current Location',
            address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          };
          
          onChange(location);
        } finally {
          setIsGettingLocation(false);
        }
      },
      (error) => {
        setIsGettingLocation(false);
        
        let errorMessage = 'Failed to get your location';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location permissions.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out.';
            break;
        }
        
        toast({
          title: "Location Error",
          description: errorMessage,
          variant: "destructive",
        });
        
        setManualEntry(true);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  const handleManualSave = () => {
    const lat = parseFloat(manualData.latitude);
    const lng = parseFloat(manualData.longitude);
    
    if (isNaN(lat) || isNaN(lng)) {
      toast({
        title: "Invalid coordinates",
        description: "Please enter valid latitude and longitude values",
        variant: "destructive",
      });
      return;
    }
    
    if (lat < -90 || lat > 90) {
      toast({
        title: "Invalid latitude",
        description: "Latitude must be between -90 and 90",
        variant: "destructive",
      });
      return;
    }
    
    if (lng < -180 || lng > 180) {
      toast({
        title: "Invalid longitude",
        description: "Longitude must be between -180 and 180",
        variant: "destructive",
      });
      return;
    }
    
    const location: LocationData = {
      latitude: lat,
      longitude: lng,
      name: manualData.name || 'Custom Location',
      address: manualData.address || `${lat.toFixed(6)}, ${lng.toFixed(6)}`
    };
    
    onChange(location);
    setManualEntry(false);
    setManualData({ latitude: '', longitude: '', name: '', address: '' });
    
    toast({
      title: "Location set",
      description: "Custom location has been set successfully",
    });
  };

  const clearLocation = () => {
    onChange(null);
    setManualEntry(false);
    setManualData({ latitude: '', longitude: '', name: '', address: '' });
  };

  // If location is already set, show the location card
  if (value) {
    return (
      <Card className="p-4">
        <div className="flex items-start gap-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <MapPin className="h-5 w-5 text-red-600" />
          </div>
          
          <div className="flex-1">
            <p className="font-medium">{value.name}</p>
            <p className="text-sm text-gray-600 mt-1">{value.address}</p>
            <p className="text-xs text-gray-400 mt-1">
              {value.latitude.toFixed(6)}, {value.longitude.toFixed(6)}
            </p>
            
            {/* Google Maps link */}
            <a
              href={`https://www.google.com/maps?q=${value.latitude},${value.longitude}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 hover:text-blue-800 underline mt-2 inline-block"
            >
              View on Google Maps
            </a>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={clearLocation}
            disabled={disabled}
          >
            Remove
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-gray-700">
        Share Location
      </div>
      
      {/* Location options */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={getCurrentLocation}
          disabled={disabled || isGettingLocation}
          className="flex-1"
        >
          {isGettingLocation ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Getting location...
            </>
          ) : (
            <>
              <Navigation className="h-4 w-4 mr-2" />
              Use Current Location
            </>
          )}
        </Button>
        
        <Button
          variant="outline"
          onClick={() => setManualEntry(true)}
          disabled={disabled}
        >
          Enter Manually
        </Button>
      </div>

      {/* Manual entry form */}
      {manualEntry && (
        <Card className="p-4 space-y-4">
          <div className="text-sm font-medium">Enter Location Details</div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-xs text-gray-600 mb-1 block">
                Latitude *
              </label>
              <Input
                placeholder="25.2048"
                type="number"
                step="any"
                value={manualData.latitude}
                onChange={(e) => setManualData(prev => ({ ...prev, latitude: e.target.value }))}
                disabled={disabled}
              />
            </div>
            <div>
              <label className="text-xs text-gray-600 mb-1 block">
                Longitude *
              </label>
              <Input
                placeholder="55.2708"
                type="number"
                step="any"
                value={manualData.longitude}
                onChange={(e) => setManualData(prev => ({ ...prev, longitude: e.target.value }))}
                disabled={disabled}
              />
            </div>
          </div>
          
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Location Name
            </label>
            <Input
              placeholder="Dubai Mall"
              value={manualData.name}
              onChange={(e) => setManualData(prev => ({ ...prev, name: e.target.value }))}
              disabled={disabled}
            />
          </div>
          
          <div>
            <label className="text-xs text-gray-600 mb-1 block">
              Address
            </label>
            <Input
              placeholder="Downtown Dubai, UAE"
              value={manualData.address}
              onChange={(e) => setManualData(prev => ({ ...prev, address: e.target.value }))}
              disabled={disabled}
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={handleManualSave}
              disabled={!manualData.latitude || !manualData.longitude || disabled}
              className="flex-1"
            >
              Set Location
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                setManualEntry(false);
                setManualData({ latitude: '', longitude: '', name: '', address: '' });
              }}
              disabled={disabled}
            >
              Cancel
            </Button>
          </div>
          
          <div className="text-xs text-gray-500">
            <p>Tip: You can get coordinates from Google Maps by right-clicking on a location.</p>
          </div>
        </Card>
      )}
    </div>
  );
}; 