import { Button } from '@/components/ui/button';
import { 
  MessageSquare, 
  Image, 
  Video, 
  Mic, 
  FileText, 
  MapPin, 
  Users 
} from 'lucide-react';

type MessageType = 'TEXT' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT' | 'LOCATION' | 'CONTACT';

interface MessageTypeSelectorProps {
  value: MessageType;
  onChange: (type: MessageType) => void;
  disabled?: boolean;
}

export const MessageTypeSelector: React.FC<MessageTypeSelectorProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  const messageTypes = [
    { 
      type: 'TEXT' as MessageType, 
      icon: MessageSquare, 
      label: 'Text', 
      color: 'text-blue-600',
      description: 'Send a text message'
    },
    { 
      type: 'IMAGE' as MessageType, 
      icon: Image, 
      label: 'Image', 
      color: 'text-green-600',
      description: 'Send an image with optional caption'
    },
    { 
      type: 'VIDEO' as MessageType, 
      icon: Video, 
      label: 'Video', 
      color: 'text-purple-600',
      description: 'Send a video with optional caption'
    },
    { 
      type: 'AUDIO' as MessageType, 
      icon: Mic, 
      label: 'Audio', 
      color: 'text-orange-600',
      description: 'Send an audio message or voice note'
    },
    { 
      type: 'DOCUMENT' as MessageType, 
      icon: FileText, 
      label: 'Document', 
      color: 'text-gray-600',
      description: 'Send a document or file'
    },
    { 
      type: 'LOCATION' as MessageType, 
      icon: MapPin, 
      label: 'Location', 
      color: 'text-red-600',
      description: 'Share your location or an address'
    },
    { 
      type: 'CONTACT' as MessageType, 
      icon: Users, 
      label: 'Contact', 
      color: 'text-indigo-600',
      description: 'Share contact information'
    },
  ];

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700">
        Message Type
      </label>
      
      {/* Desktop/Tablet View - Horizontal layout */}
      <div className="hidden sm:flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg border">
        {messageTypes.map(({ type, icon: Icon, label, color, description }) => (
          <Button
            key={type}
            variant={value === type ? "default" : "ghost"}
            size="sm"
            onClick={() => onChange(type)}
            disabled={disabled}
            className={`flex items-center gap-2 transition-all ${
              value === type 
                ? 'shadow-sm' 
                : `${color} hover:bg-white`
            }`}
            title={description}
          >
            <Icon size={16} />
            <span className="font-medium">{label}</span>
          </Button>
        ))}
      </div>

      {/* Mobile View - Vertical compact layout */}
      <div className="sm:hidden grid grid-cols-2 gap-2">
        {messageTypes.map(({ type, icon: Icon, label, color, description }) => (
          <Button
            key={type}
            variant={value === type ? "default" : "outline"}
            size="sm"
            onClick={() => onChange(type)}
            disabled={disabled}
            className={`flex items-center justify-start gap-2 h-12 ${
              value === type 
                ? '' 
                : `${color}`
            }`}
            title={description}
          >
            <Icon size={18} />
            <span className="font-medium text-sm">{label}</span>
          </Button>
        ))}
      </div>

      {/* Selected type description */}
      {value && (
        <div className="text-xs text-gray-500 italic">
          {messageTypes.find(t => t.type === value)?.description}
        </div>
      )}
    </div>
  );
}; 