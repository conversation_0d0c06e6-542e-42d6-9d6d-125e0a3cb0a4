import React from 'react';
import { ParsedContactContent, ContactInfo, getContentIcon } from '../../utils/messageContentParser';
import { cn } from '../../lib/utils';

interface ContactMessageProps {
  parsedContent: ParsedContactContent;
  className?: string;
}

export const ContactMessage: React.FC<ContactMessageProps> = ({ 
  parsedContent, 
  className 
}) => {
  const { contacts = [] } = parsedContent;
  const hasContactData = contacts.length > 0;

  if (!hasContactData) {
    // Show generic contact message when no specific contact data is available
    return (
      <div className={cn('flex items-center p-3 bg-gray-50 rounded-lg', className)}>
        <div className="text-2xl mr-3">
          {getContentIcon(parsedContent)}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-gray-900">
            Contact Shared
          </div>
          <div className="text-xs text-gray-500">
            Contact information was shared in this conversation
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {contacts.map((contact, index) => (
        <ContactCard key={index} contact={contact} />
      ))}
    </div>
  );
};

interface ContactCardProps {
  contact: ContactInfo;
}

const ContactCard: React.FC<ContactCardProps> = ({ contact }) => {
  const { name, phone, email, organization } = contact;

  const handleCall = (phoneNumber: string) => {
    window.open(`tel:${phoneNumber}`, '_self');
  };

  const handleMessage = (phoneNumber: string) => {
    // Remove any non-digit characters and format for WhatsApp
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    const whatsappUrl = `https://wa.me/${cleanPhone}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleCopyPhone = (phoneNumber: string) => {
    navigator.clipboard?.writeText(phoneNumber);
    // Could add a toast notification here
  };

  return (
    <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
      {/* Main contact info section */}
      <div className="flex items-start p-3">
        <div className="text-2xl mr-3 mt-1">
          👤
        </div>
        
        <div className="flex-1 min-w-0">
          {name && (
            <div className="text-sm font-medium text-gray-900 mb-1">
              {name}
            </div>
          )}
          
          {organization && (
            <div className="text-xs text-gray-600 mb-2">
              {organization}
            </div>
          )}
          
          <div className="space-y-1">
            {phone && (
              <div className="flex items-center text-xs text-gray-700">
                <PhoneIcon className="w-3 h-3 mr-1" />
                <span className="flex-1">{phone}</span>
              </div>
            )}
            
            {email && (
              <div className="flex items-center text-xs text-gray-700">
                <EmailIcon className="w-3 h-3 mr-1" />
                <a 
                  href={`mailto:${email}`}
                  className="hover:text-blue-600 transition-colors"
                >
                  {email}
                </a>
              </div>
            )}
          </div>
        </div>
        
        {/* Copy button in top right */}
        <div className="ml-2">
          {phone && (
            <button
              onClick={() => handleCopyPhone(phone)}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Copy phone number"
            >
              <CopyIcon className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>
      
      {/* Action buttons section */}
      {phone && (
        <div className="border-t border-gray-200 px-3 py-2 bg-gray-25">
          <div className="flex gap-2">
            <button
              onClick={() => handleCall(phone)}
              className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
              title="Call contact"
            >
              <CallIcon className="w-4 h-4" />
              Call
            </button>
            
            <button
              onClick={() => handleMessage(phone)}
              className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
              title="Send WhatsApp message"
            >
              <MessageIcon className="w-4 h-4" />
              Message
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Simple SVG icons
const PhoneIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
  </svg>
);

const EmailIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
  </svg>
);

const CopyIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
    <path d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L14.586 13H19v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2V5h-2v6z" />
  </svg>
);

const CallIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
  </svg>
);

const MessageIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
  </svg>
);

export default ContactMessage; 