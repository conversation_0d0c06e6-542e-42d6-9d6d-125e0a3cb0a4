import React, { useState, useCallback } from "react"
import { cn } from "../../../lib/utils"
import { ConversationPanel } from "./ConversationPanel"
import { MessagePanel } from "./MessagePanel"
import { useOrganizationNotifications } from "../../../hooks/useOrganizationNotifications"
// import DesktopWhatsAppBg from "../../../assets/Desktop-whatsapp.png"

export interface ChatLayoutProps {
  className?: string
}

export const ChatLayout: React.FC<ChatLayoutProps> = ({ className }) => {
  const [selectedConversationId, setSelectedConversationId] = useState<
    string | null
  >(null)
  const [, /*isMobileConversationOpen*/ setIsMobileConversationOpen] =
    useState(false)

  // Organization-wide notifications for messages in other conversations
  const { unreadNotifications, clearNotification } =
    useOrganizationNotifications({
      currentConversationId: selectedConversationId || undefined,
      enabled: true
    })

  const handleConversationSelect = useCallback((conversationId: string) => {
    // If clicking the same conversation, close it (WhatsApp behavior)
    if (selectedConversationId === conversationId) {
      setSelectedConversationId(null)
      setIsMobileConversationOpen(true)
      return
    }

    setSelectedConversationId(conversationId)
    // Clear notifications for this conversation
    unreadNotifications
      .filter((n) => n.conversationId === conversationId)
      .forEach((n) => clearNotification(n.id))
    // On mobile, hide conversation list when a conversation is selected
    setIsMobileConversationOpen(false)
  }, [selectedConversationId, unreadNotifications, clearNotification])

  const handleBackToConversations = useCallback(() => {
    setSelectedConversationId(null)
    setIsMobileConversationOpen(true)
  }, [])

  return (
    <div className={cn("h-full flex bg-[#0b141a] overflow-hidden", className)}>
      {/* Conversation Panel - WhatsApp Dark Style */}
      <div
        className={cn(
          "flex-shrink-0",
          "bg-[#111b21]",
          // Desktop: Always visible, fixed width
          "md:flex md:w-80 lg:w-96",
          // Mobile: Show when no chat selected, hide when chat is open
          !selectedConversationId
            ? "flex w-full md:w-80 lg:w-96"
            : "hidden md:flex"
        )}
      >
        <ConversationPanel
          selectedConversationId={selectedConversationId}
          onConversationSelect={handleConversationSelect}
          unreadNotifications={unreadNotifications}
          className="w-full"
        />
      </div>

      {/* Message Panel - WhatsApp Style */}
      <div
        className={cn(
          "flex-1 flex flex-col min-w-0",
          "bg-[#0b141a]",
          // Mobile: Show when chat is selected, hidden when no chat
          selectedConversationId ? "flex" : "hidden md:flex"
        )}
      >
        {selectedConversationId ? (
          <MessagePanel
            conversationId={selectedConversationId}
            onBackToConversations={handleBackToConversations}
            showBackButton={true} // Always show on mobile
          />
        ) : (
          <div
            className="hidden md:flex flex-1 items-center justify-center relative"
            style={{
              // backgroundImage: `url(${DesktopWhatsAppBg})`,
              backgroundSize: "contain",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              backgroundColor: "#0b141a"
            }}
          >
            {/* Overlay for better text readability */}
            <div className="absolute inset-0 bg-[#0b141a]/60"></div>

            <div className="text-center p-8 max-w-md relative z-10">
              <div className="w-32 h-32 mx-auto mb-8 bg-[#2a3942]/80 backdrop-blur-sm rounded-full flex items-center justify-center">
                <svg
                  className="w-16 h-16 text-[#8696a0]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-light text-[#e9edef] mb-4 drop-shadow-lg">
                WhatsApp Web
              </h3>
              <p className="text-[#8696a0] text-sm leading-relaxed mb-8 drop-shadow-md">
                Send and receive messages without keeping your phone online.
                <br />
                Use WhatsApp on up to 4 linked devices and 1 phone at the same
                time.
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-[#8696a0] border-t border-[#2a3942]/50 pt-6 backdrop-blur-sm">
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="drop-shadow-md">
                  Your personal messages are end-to-end encrypted
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ChatLayout
