import React from 'react';
import { cn } from '../../../lib/utils';
import { ChatView } from '../ChatView';

export interface MessagePanelProps {
  conversationId: string;
  onBackToConversations?: () => void;
  showBackButton?: boolean;
  className?: string;
}

export const MessagePanel: React.FC<MessagePanelProps> = ({
  conversationId,
  onBackToConversations,
  className,
}) => {
  return (
    <div className={cn('flex flex-col h-full bg-[#0b141a]', className)}>
      <ChatView
        conversationId={conversationId}
        onBack={onBackToConversations}
      />
    </div>
  );
};

export default MessagePanel; 