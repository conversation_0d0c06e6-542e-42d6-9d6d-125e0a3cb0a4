/**
 * MessageBubble Component Tests
 * Tests content parsing and display for various message types
 * Based on Task 1-9: Fix Chat Message Content Display Issues
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { MessageBubble } from '../MessageBubble';
import { ConversationMessage } from '../../../hooks/useConversationMessages';

// Mock the content parser module
jest.mock('../../../utils/messageContentParser', () => ({
  parseMessageContent: jest.fn(),
  getMediaDescription: jest.fn(),
  getContentIcon: jest.fn(),
  CONTENT_TYPE_TEXT: 'text',
  CONTENT_TYPE_LOCATION: 'location',
  CONTENT_TYPE_CONTACTS: 'contacts',
  CONTENT_TYPE_IMAGE: 'image',
  CONTENT_TYPE_VIDEO: 'video',
  CONTENT_TYPE_DOCUMENT: 'document',
  CONTENT_TYPE_AUDIO: 'audio',
}));

// Mock the ContactMessage component
jest.mock('../ContactMessage', () => ({
  ContactMessage: ({ parsedContent }: any) => (
    <div data-testid="contact-message">Contact: {parsedContent.type}</div>
  ),
}));

const mockParseMessageContent = jest.requireMock('../../../utils/messageContentParser').parseMessageContent;
const mockGetMediaDescription = jest.requireMock('../../../utils/messageContentParser').getMediaDescription;
const mockGetContentIcon = jest.requireMock('../../../utils/messageContentParser').getContentIcon;

// Test data based on real user examples
const createTestMessage = (overrides: Partial<ConversationMessage> = {}): ConversationMessage => ({
  id: 'test-message-1',
  conversationId: 'test-conversation',
  messageId: 'test-message-1',
  sortKey: '2025-01-17T08:30:00+00:00#test-message-1',
  organizationId: 'test-org',
  businessPhone: '1234567890',
  customerPhone: '0987654321',
  phoneNumberId: '1234567890',
  wabaId: 'test-waba',
  fromNumber: '0987654321',
  contactName: 'Test User',
  messageType: 'TEXT',
  direction: 'inbound' as const,
  status: 'DELIVERED',
  content: 'Test message',
  timestamp: '2025-01-17T08:30:00+00:00',
  createdAt: '2025-01-17T08:30:05+00:00',
  updatedAt: '2025-01-17T08:30:05+00:00',
  ...overrides,
});

describe('MessageBubble', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Location Messages', () => {
    it('should parse and display location content correctly', () => {
      // Real data from user example
      const locationContent = '"{\"latitude\":25.395776748657,\"type\":\"location\",\"longitude\":55.519287109375}"';
      
      const message = createTestMessage({
        content: locationContent,
        messageType: 'LOCATION',
      });

      // Mock the parser to return parsed location data
      mockParseMessageContent.mockReturnValue({
        type: 'location',
        latitude: 25.395776748657,
        longitude: 55.519287109375,
      });

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should render location coordinates
      expect(screen.getByText(/25.395777, 55.519287/)).toBeInTheDocument();
      expect(screen.getByText('View in Maps')).toBeInTheDocument();
    });

    it('should handle malformed location JSON gracefully', () => {
      const message = createTestMessage({
        content: '"{\"latitude\":invalid,\"type\":\"location\"}"',
        messageType: 'LOCATION',
      });

      // Mock parser returning null for invalid JSON
      mockParseMessageContent.mockReturnValue(null);

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should fallback to displaying the raw content
      expect(screen.getByText(/latitude.*invalid/)).toBeInTheDocument();
    });
  });

  describe('Contact Messages', () => {
    it('should display contact message correctly', () => {
      // Real data from user example
      const contactContent = '"{\"type\":\"contacts\"}"';
      
      const message = createTestMessage({
        content: contactContent,
        messageType: 'CONTACT',
      });

      // Mock the parser to return parsed contact data
      mockParseMessageContent.mockReturnValue({
        type: 'contacts',
        contacts: [],
      });

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should render ContactMessage component
      expect(screen.getByTestId('contact-message')).toBeInTheDocument();
      expect(screen.getByText('Contact: contacts')).toBeInTheDocument();
    });
  });

  describe('Media Messages', () => {
    it('should display image with meaningful description', () => {
      // Real data from user example  
      const imageContent = '"{\"mime_type\":\"image/jpeg\",\"s3_url\":\"https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/image.jpg\",\"image_id\":\"1451649865963275\",\"type\":\"image\",\"file_size\":11666}"';
      
      const message = createTestMessage({
        content: imageContent,
        messageType: 'IMAGE',
        mediaUrl: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/image.jpg',
        mediaType: 'image/jpeg',
      });

      // Mock the parser and helper functions
      mockParseMessageContent.mockReturnValue({
        type: 'image',
        s3_url: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/image.jpg',
        mime_type: 'image/jpeg',
        file_size: 11666,
        image_id: '1451649865963275',
      });
      
      mockGetMediaDescription.mockReturnValue('Image (JPEG) - 11.4 KB');
      mockGetContentIcon.mockReturnValue('🖼️');

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should render image with meaningful alt text
      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', 'Image (JPEG) - 11.4 KB');
      expect(image).toHaveAttribute('src', 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/image.jpg');
    });

    it('should display video with proper controls', () => {
      const videoContent = '"{\"mime_type\":\"video/mp4\",\"s3_url\":\"https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/video.mp4\",\"video_id\":\"2446818075693999\",\"type\":\"video\",\"file_size\":5665453}"';
      
      const message = createTestMessage({
        content: videoContent,
        messageType: 'VIDEO',
        mediaUrl: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/video.mp4',
        mediaType: 'video/mp4',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'video',
        s3_url: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/video.mp4',
        mime_type: 'video/mp4',
        file_size: 5665453,
        video_id: '2446818075693999',
      });

      mockGetMediaDescription.mockReturnValue('Video (MP4) - 5.4 MB');
      mockGetContentIcon.mockReturnValue('🎬');

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should render video element with controls
      const video = screen.getByRole('application'); // video elements have application role
      expect(video).toHaveAttribute('controls');
      expect(video).toBeInTheDocument();
    });

    it('should display document with download link', () => {
      const documentContent = '"{\"mime_type\":\"application/pdf\",\"s3_url\":\"https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/document.pdf\",\"document_id\":\"1054418386907424\",\"type\":\"document\",\"file_size\":513494}"';
      
      const message = createTestMessage({
        content: documentContent,
        messageType: 'DOCUMENT',
        mediaUrl: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/document.pdf',
        mediaType: 'application/pdf',
        fileName: 'test-document.pdf',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'document',
        s3_url: 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/document.pdf',
        mime_type: 'application/pdf',
        file_size: 513494,
        document_id: '1054418386907424',
      });

      mockGetMediaDescription.mockReturnValue('Document (PDF) - 501.5 KB');
      mockGetContentIcon.mockReturnValue('📄');

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should render document with download link
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
      expect(screen.getByText('Document (PDF) - 501.5 KB')).toBeInTheDocument();
      
      const downloadLink = screen.getByTitle('Download test-document.pdf');
      expect(downloadLink).toHaveAttribute('href', 'https://notefy-whatsapp-media-dev.s3.me-central-1.amazonaws.com/test/document.pdf');
      expect(downloadLink).toHaveAttribute('target', '_blank');
    });
  });

  describe('Text Messages', () => {
    it('should display plain text correctly', () => {
      const message = createTestMessage({
        content: '"{\"text\":\"Hello world!\",\"type\":\"text\"}"',
        textContent: 'Hello world!',
        messageType: 'TEXT',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'text',
        text: 'Hello world!',
      });

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      expect(screen.getByText('Hello world!')).toBeInTheDocument();
    });

    it('should handle URLs in text messages', () => {
      const message = createTestMessage({
        content: 'Check this out: https://example.com',
        messageType: 'TEXT',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'text',
        text: 'Check this out: https://example.com',
      });

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://example.com');
      expect(link).toHaveAttribute('target', '_blank');
    });
  });

  describe('Fallback Behavior', () => {
    it('should fallback to original display when parsing fails', () => {
      const message = createTestMessage({
        content: 'Invalid JSON content',
        messageType: 'UNKNOWN',
      });

      // Mock parser returning null for unparseable content
      mockParseMessageContent.mockReturnValue(null);

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should display the original content
      expect(screen.getByText('Invalid JSON content')).toBeInTheDocument();
    });

    it('should handle missing media URLs gracefully', () => {
      const message = createTestMessage({
        content: '"{\"type\":\"image\"}"',
        messageType: 'IMAGE',
        mediaUrl: undefined,
      });

      mockParseMessageContent.mockReturnValue({
        type: 'image',
        s3_url: '',
        mime_type: 'image/jpeg',
      });

      mockGetMediaDescription.mockReturnValue('Image (JPEG)');
      mockGetContentIcon.mockReturnValue('🖼️');

      render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should show unavailable message
      expect(screen.getByText(/unavailable/)).toBeInTheDocument();
    });
  });

  describe('Message Styling', () => {
    it('should apply correct styling for outbound messages', () => {
      const message = createTestMessage({
        content: 'Outbound message',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'text',
        text: 'Outbound message',
      });

      const { container } = render(<MessageBubble message={message} isOutbound={true} timeLabel="8:30 AM" />);

      // Should have green background for outbound messages
      const bubble = container.querySelector('.bg-green-500');
      expect(bubble).toBeInTheDocument();
    });

    it('should apply correct styling for inbound messages', () => {
      const message = createTestMessage({
        content: 'Inbound message',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'text',
        text: 'Inbound message',
      });

      const { container } = render(<MessageBubble message={message} isOutbound={false} timeLabel="8:30 AM" />);

      // Should have white background for inbound messages
      const bubble = container.querySelector('.bg-white');
      expect(bubble).toBeInTheDocument();
    });

    it('should display timestamp correctly', () => {
      const message = createTestMessage({
        content: 'Test message',
      });

      mockParseMessageContent.mockReturnValue({
        type: 'text',
        text: 'Test message',
      });

      render(<MessageBubble message={message} isOutbound={false} timeLabel="2:30 PM" />);

      expect(screen.getByText('2:30 PM')).toBeInTheDocument();
    });
  });
}); 