import { useEffect, useRef, useState, useCallback } from "react"
import { cn } from "../../lib/utils"
import DesktopWhatsAppBg from "../../assets/Desktop-whatsapp.png"
import { useAuth } from "../../hooks/useAuth"
import { useRealTimeConversationMessages } from "../../hooks/useRealTimeConversationMessages"
import { MessageItem } from "./MessageItem"
import { MessageComposer } from "./outbound/MessageComposer"
import { Card, CardContent } from "../ui/card"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import {
  Phone,
  MoreVertical,
  ArrowLeft,
  CheckCircle2,
  XCircle,
  UserCheck,
  MessageSquareX
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "../ui/dropdown-menu"
import { TooltipProvider } from "../ui/tooltip"

export const ChatView = ({
  conversationId,
  onBack
}: {
  conversationId: string
  onBack?: () => void
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [, setIsAtTop] = useState(false)
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true)
  const [showCustomerInfo, setShowCustomerInfo] = useState(false)

  // Get organization context for organization-wide subscriptions
  const { user } = useAuth()

  // ✅ NEW: Using real-time subscriptions instead of auto-refresh polling
  const {
    messages,
    loading,
    error,
    hasMore,
    loadingMore,
    loadMore,
    isConnected,
    subscriptionErrors,
    reconnectSubscriptions
  } = useRealTimeConversationMessages({
    conversationId,
    connectionId: undefined, // Will be extracted from first message
    organizationId: user?.organizationId, // Organization-wide subscriptions
    limit: 50,
    enabled: true
  })

  // Get customer info from first message and extract connectionId
  const customerInfo = messages?.[0]
  const contactName = customerInfo?.contactName || "Unknown Contact"
  const customerPhone = customerInfo?.customerPhone || ""
  const businessPhone = customerInfo?.businessPhone || ""
  const extractedConnectionId =
    customerInfo?.phoneNumberId || customerInfo?.wabaId



  // Mock customer data - compact version
  const customerData = {
    totalOrders: 47,
    totalSpent: 2847.5,
    currency: "AED",
    loyaltyTier: "Gold",
    location: "Dubai, UAE"
  }

  const storeInfo = {
    name: "Boutique Fashion Store",
    platform: "Shopify Plus",
    status: "Connected"
  }

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current
    const isNearTop = scrollTop < 100
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100

    setIsAtTop(isNearTop)
    setShouldAutoScroll(isNearBottom)

    // Load more messages when scrolling to top
    if (isNearTop && hasMore && !loadingMore && !loading) {
      const oldScrollHeight = scrollHeight
      ;(async () => {
        try {
          await loadMore()
          setTimeout(() => {
            if (scrollContainerRef.current) {
              const newScrollHeight = scrollContainerRef.current.scrollHeight
              scrollContainerRef.current.scrollTop =
                newScrollHeight - oldScrollHeight
            }
          }, 100)
        } catch (error) {
          console.error("Failed to load more messages:", error)
        }
      })()
    }
  }, [hasMore, loadingMore, loading, loadMore])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll && scrollContainerRef.current) {
      const scrollContainer = scrollContainerRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [messages, shouldAutoScroll])

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return ""
    // Handle phone numbers stored without + prefix
    let cleaned = phone.replace(/\D/g, "")

    // Add + prefix if not present and number looks international
    if (cleaned.length >= 10 && !phone.startsWith("+")) {
      // Check common patterns for UAE numbers (971)
      if (cleaned.startsWith("971") && cleaned.length === 12) {
        return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 5)} ${cleaned.slice(
          5,
          8
        )} ${cleaned.slice(8)}`
      }
      // Default international format
      if (cleaned.length >= 10) {
        const countryCode = cleaned.slice(0, -10)
        const number = cleaned.slice(-10)
        return `+${countryCode} ${number.slice(0, 3)} ${number.slice(
          3,
          6
        )} ${number.slice(6)}`
      }
    }

    // Handle already formatted numbers
    if (phone.startsWith("+")) {
      return phone
    }

    return `+${cleaned}`
  }

  const getCustomerInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const handleCallCustomer = () => {
    if (customerPhone) {
      window.open(`tel:${customerPhone}`, "_self")
    }
  }

  // if (loading && !messages.length) {
  //   return (
  //     <div className="flex items-center justify-center h-full">
  //       <div className="text-center space-y-2">
  //         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
  //         <p className="text-sm text-muted-foreground">
  //           Loading conversation...
  //         </p>
  //         <p className="text-xs text-muted-foreground">
  //           Conversation ID: {conversationId}
  //         </p>
  //       </div>
  //     </div>
  //   )
  // }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card className="p-6 max-w-md mx-4">
          <CardContent className="text-center space-y-4 p-0">
            <XCircle className="h-8 w-8 text-destructive mx-auto" />
            <h3 className="font-semibold">Failed to Load Conversation</h3>
            <p className="text-sm text-muted-foreground">{error}</p>
            <p className="text-xs text-muted-foreground">
              Conversation ID: {conversationId}
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              size="sm"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show a message if no messages are found
  // if (!loading && (!messages || messages.length === 0)) {
  //   return (
  //     <div className="flex items-center justify-center h-full">
  //       <Card className="p-6 max-w-md mx-4">
  //         <CardContent className="text-center space-y-4 p-0">
  //           <MessageSquareX className="h-8 w-8 text-muted-foreground mx-auto" />
  //           <h3 className="font-semibold">No Messages Found</h3>
  //           <p className="text-sm text-muted-foreground">
  //             This conversation doesn't have any messages yet.
  //           </p>
  //           <p className="text-xs text-muted-foreground">
  //             Conversation ID: {conversationId}
  //           </p>
  //           <Button
  //             onClick={() => window.location.reload()}
  //             variant="outline"
  //             size="sm"
  //           >
  //             Refresh
  //           </Button>
  //         </CardContent>
  //       </Card>
  //     </div>
  //   )
  // }

  return (
    <TooltipProvider>
      <div className="flex flex-col h-full bg-[#0b141a]">
        {/* WhatsApp Style Header */}
        <div className="bg-[#202c33] border-b p-[0.86rem] border-[#2a3942]">
          <div className="flex items-center px-4 ">
            {/* Back Button (Mobile) */}
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="md:hidden w-10 h-10 p-0 mr-2 text-[#8696a0] hover:bg-[#2a3942] rounded-full"
                aria-label="Back to conversations"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
            )}

            {/* Contact Info */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="relative">
                <Avatar className="w-10 h-10">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/initials/svg?seed=${contactName}`}
                  />
                  <AvatarFallback className="bg-[#6b7c85] text-white font-medium">
                    {getCustomerInitials(contactName)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-[#00a884] border-2 border-[#202c33] rounded-full"></div>
              </div>

              <div className="flex-1 min-w-0">
                <h2 className="font-medium text-[#e9edef] truncate">
                  {contactName}
                </h2>
                <p className="text-sm text-[#8696a0] truncate">
                  {formatPhoneNumber(customerPhone)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Real-time connection status indicator */}
              <div className="flex items-center space-x-1">
                <div
                  className={cn(
                    "w-2 h-2 rounded-full",
                    isConnected ? "bg-green-500" : "bg-red-500"
                  )}
                />
                <span className="text-xs text-muted-foreground">
                  {isConnected ? "Live" : "Offline"}
                </span>
                {subscriptionErrors.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={reconnectSubscriptions}
                    className="h-6 px-2 text-xs"
                  >
                    Retry
                  </Button>
                )}
              </div>

              {/* Video Call */}
              <Button
                variant="ghost"
                size="sm"
                className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
                title="Video call"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                </svg>
              </Button>

              {/* Voice Call */}
              <Button
                variant="ghost"
                size="sm"
                className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
                title="Voice call"
              >
                <Phone className="w-5 h-5" />
              </Button>

              {/* Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
                    title="Menu"
                  >
                    <MoreVertical className="w-5 h-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem>
                    <UserCheck className="h-4 w-4 mr-2" />
                    Assign to Agent
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Change Status
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    Close as Resolved
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-destructive">
                    <MessageSquareX className="h-4 w-4 mr-2" />
                    Close Chat
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Collapsible Customer Info - Compact */}
          {showCustomerInfo && (
            <div className="border-t bg-muted/30 px-4">
              <div className="grid grid-cols-4 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-lg font-bold text-blue-600">
                    {customerData.totalOrders}
                  </div>
                  <div className="text-xs text-muted-foreground">Orders</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-green-600">
                    {customerData.currency}{" "}
                    {customerData.totalSpent.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Spent</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold text-purple-600">
                    {customerData.currency}{" "}
                    {(
                      customerData.totalSpent / customerData.totalOrders
                    ).toFixed(0)}
                  </div>
                  <div className="text-xs text-muted-foreground">AOV</div>
                </div>
                <div className="space-y-1">
                  <Badge className="bg-green-100 text-green-800 text-xs">
                    <CheckCircle2 className="w-3 h-3 mr-1" />
                    {storeInfo.status}
                  </Badge>
                  <div className="text-xs text-muted-foreground">
                    {storeInfo.platform}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Messages Container - MAIN FOCUS - THE ACTUAL CHAT CONTENT */}
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="flex-1 overflow-y-auto px-4 py-4 space-y-4 relative"
          style={{
            // backgroundImage: `url(${DesktopWhatsAppBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundColor: "#0b141a",
            backgroundAttachment: "fixed"
          }}
        >
          {/* Overlay for better message readability */}
          <div className="absolute inset-0 bg-[#0b141a]/30 pointer-events-none"></div>
          {loadingMore && (
            <div className="text-center py-2 relative z-10">
              <div className="inline-flex items-center space-x-2 text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm">Loading more...</span>
              </div>
            </div>
          )}

          {/* THIS IS WHERE THE GRAPHQL/APPSYNC CHAT MESSAGES ARE RENDERED */}
          {messages && messages.length > 0 ? (
            <div className="space-y-0 relative z-10">
              {messages.map((message, index) => (
                <MessageItem
                  key={`${message.messageId}-${index}`}
                  message={message}
                  previousMessage={index > 0 ? messages[index - 1] : undefined}
                  nextMessage={
                    index < messages.length - 1
                      ? messages[index + 1]
                      : undefined
                  }
                  showSenderName={true}
                  isGrouped={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground relative z-10">
              <MessageSquareX className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">No messages to display</p>
            </div>
          )}

          {/* Only show typing indicator if someone is actually typing - removed for demo */}
          {/* <TypingIndicator /> */}
        </div>

        {/* Enhanced Message Composer */}
        <div className="border-t border-[#2a3942] bg-[#202c33]">
          <MessageComposer
            conversationId={conversationId}
            onMessageSent={(message) => {
              // Handle successful message send
              console.log("Message sent:", message)
              // The real-time hook will automatically update the messages list
            }}
            className="border-0 shadow-none"
          />
        </div>
      </div>
    </TooltipProvider>
  )
}
