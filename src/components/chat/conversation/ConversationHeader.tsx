import React from 'react';
import { MoreVertical, Settings, Users, Search, MessageCircle, Archive, Moon, Sun, Monitor } from 'lucide-react';
import { Button } from '../../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../ui/dropdown-menu';
import { useTheme } from '../../ui/theme-provider';
import { cn } from '../../../lib/utils';

export interface ConversationHeaderProps {
  className?: string;
}

export const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  className,
}) => {
  const { theme, setTheme } = useTheme();

  return (
    <div className={cn(
      'flex items-center justify-between p-4',
      'bg-chat-header dark:bg-chat-header-dark',
      'border-b border-chat-border dark:border-chat-border-dark',
      className
    )}>
      {/* User Avatar and Info */}
      <div className="flex items-center gap-3">
        <Avatar className="w-10 h-10">
          <AvatarImage src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" />
          <AvatarFallback className="bg-chat-primary text-chat-text-on-primary text-sm font-medium">
            ME
          </AvatarFallback>
        </Avatar>
        <div className="hidden sm:block">
          <h2 className="text-chat-text-primary dark:text-chat-text-primary-dark font-medium text-base">
            You
          </h2>
          <p className="text-chat-text-secondary dark:text-chat-text-secondary-dark text-xs">
            Online
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-1">
        {/* New Chat */}
        <Button
          variant="ghost"
          size="sm"
          className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
          title="New chat"
        >
          <MessageCircle className="w-5 h-5" />
        </Button>

        {/* Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-10 h-10 p-0 text-chat-text-secondary dark:text-chat-text-secondary-dark hover:bg-chat-hover dark:hover:bg-chat-hover-dark rounded-full"
              title="Menu"
            >
              <MoreVertical className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem>
              <Users className="w-4 h-4 mr-2" />
              New group
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Archive className="w-4 h-4 mr-2" />
              Archived
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setTheme('light')}>
              <Sun className="w-4 h-4 mr-2" />
              Light theme
              {theme === 'light' && <span className="ml-auto">✓</span>}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme('dark')}>
              <Moon className="w-4 h-4 mr-2" />
              Dark theme
              {theme === 'dark' && <span className="ml-auto">✓</span>}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme('system')}>
              <Monitor className="w-4 h-4 mr-2" />
              System theme
              {theme === 'system' && <span className="ml-auto">✓</span>}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default ConversationHeader; 