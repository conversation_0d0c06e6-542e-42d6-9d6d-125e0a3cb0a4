import React, { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Input } from '../../ui/input';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../ui/popover';
import { Label } from '../../ui/label';
import { Checkbox } from '../../ui/checkbox';
import { cn } from '../../../lib/utils';

export interface ConversationSearchProps {
  className?: string;
  onSearchChange?: (query: string) => void;
  onFiltersChange?: (filters: SearchFilters) => void;
}

export interface SearchFilters {
  status?: string[];
  stores?: string[];
  messageTypes?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export const ConversationSearch: React.FC<ConversationSearchProps> = ({
  className,
  onSearchChange,
  onFiltersChange,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<SearchFilters>({});

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    onSearchChange?.(value);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    onSearchChange?.('');
  };

  const handleFilterChange = (filterType: keyof SearchFilters, value: any) => {
    const newFilters = {
      ...activeFilters,
      [filterType]: value,
    };
    setActiveFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const getActiveFilterCount = () => {
    return Object.values(activeFilters).filter(Boolean).length;
  };

  const clearAllFilters = () => {
    setActiveFilters({});
    onFiltersChange?.({});
  };

  return (
    <div className={cn(
      'relative flex items-center',
      'bg-[#202c33]',
      'border border-[#2a3942]',
      'rounded-lg px-3 py-2',
      'transition-colors duration-200',
      className
    )}>
      {/* Search Icon */}
      <Search className="w-4 h-4 text-[#8696a0] mr-3 flex-shrink-0" />

      {/* Search Input */}
      <input
        type="text"
        value={searchQuery}
        onChange={(e) => handleSearchChange(e.target.value)}
        placeholder="Search or start new chat"
        className={cn(
          'flex-1 bg-transparent text-sm',
          'text-[#e9edef]',
          'placeholder:text-[#8696a0]',
          'focus:outline-none'
        )}
        aria-label="Search conversations"
      />

      {/* Clear Button */}
      {searchQuery && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearSearch}
          className="w-6 h-6 p-0 ml-2 text-chat-text-muted dark:text-chat-text-muted-dark hover:text-chat-text-secondary dark:hover:text-chat-text-secondary-dark"
          aria-label="Clear search"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  );
};

export default ConversationSearch; 