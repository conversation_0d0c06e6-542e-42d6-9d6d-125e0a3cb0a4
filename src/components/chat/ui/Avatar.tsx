import React from 'react';
import { cn } from '../../../lib/utils';
import { StatusBadge, OnlineStatus } from './StatusBadge';

export interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showStatus?: boolean;
  status?: OnlineStatus;
  lastSeen?: Date | string | undefined;
  fallbackColor?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name = 'User',
  size = 'md',
  className,
  showStatus = false,
  status = 'unknown',
  lastSeen,
  fallbackColor,
}) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'w-6 h-6 text-xs';
      case 'sm':
        return 'w-8 h-8 text-sm';
      case 'lg':
        return 'w-12 h-12 text-lg';
      case 'xl':
        return 'w-16 h-16 text-xl';
      default:
        return 'w-10 h-10 text-base';
    }
  };

  const getStatusBadgeSize = () => {
    switch (size) {
      case 'xs':
      case 'sm':
        return 'sm';
      case 'lg':
      case 'xl':
        return 'lg';
      default:
        return 'md';
    }
  };

  const getStatusPosition = () => {
    switch (size) {
      case 'xs':
        return '-bottom-0.5 -right-0.5';
      case 'sm':
        return '-bottom-0.5 -right-0.5';
      case 'lg':
        return '-bottom-1 -right-1';
      case 'xl':
        return '-bottom-1.5 -right-1.5';
      default:
        return '-bottom-1 -right-1';
    }
  };

  const getFallbackColor = () => {
    if (fallbackColor) return fallbackColor;
    
    // Generate a consistent color based on the name
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-red-500',
      'bg-yellow-500',
      'bg-teal-500',
    ];
    
    const nameHash = name.split('').reduce((hash, char) => {
      return char.charCodeAt(0) + ((hash << 5) - hash);
    }, 0);
    
    return colors[Math.abs(nameHash) % colors.length];
  };

  return (
    <div className={cn('relative inline-block', className)}>
      <div
        className={cn(
          'rounded-full overflow-hidden border-2 border-white shadow-sm',
          'flex items-center justify-center font-medium text-white',
          getSizeClasses(),
          !src && getFallbackColor()
        )}
      >
        {src ? (
          <img
            src={src}
            alt={alt || name}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Hide the image if it fails to load
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        ) : (
          <span className="select-none">
            {getInitials(name)}
          </span>
        )}
      </div>
      
      {/* Status indicator */}
      {showStatus && (
        <div className={cn('absolute', getStatusPosition())}>
          <StatusBadge
            status={status}
            lastSeen={lastSeen}
            size={getStatusBadgeSize()}
          />
        </div>
      )}
    </div>
  );
};

export default Avatar; 