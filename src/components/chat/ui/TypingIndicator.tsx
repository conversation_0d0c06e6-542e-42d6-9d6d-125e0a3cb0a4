import React from 'react';
import { cn } from '../../../lib/utils';

export interface TypingIndicatorProps {
  contactName?: string;
  className?: string;
  showAvatar?: boolean;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  contactName = 'Contact',
  className,
  showAvatar = true,
}) => {
  return (
    <div className={cn('flex items-start gap-2 px-4 py-2', className)}>
      {/* Avatar placeholder */}
      {showAvatar && (
        <div className="w-8 h-8 rounded-full bg-chat-gray-300 flex-shrink-0 flex items-center justify-center">
          <span className="text-chat-gray-600 text-xs font-medium">
            {contactName.charAt(0).toUpperCase()}
          </span>
        </div>
      )}
      
      {/* Typing indicator bubble */}
      <div className="bg-chat-inbound rounded-[18px] rounded-bl-[4px] px-4 py-3 shadow-[0_1px_0.5px_rgba(0,0,0,0.13)] max-w-[200px]">
        <div className="flex items-center gap-1">
          {/* Animated typing dots */}
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-chat-gray-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-chat-gray-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-chat-gray-500 rounded-full animate-bounce"></div>
          </div>
          
          {/* Optional typing text */}
          <span className="text-chat-gray-600 text-sm ml-2 select-none">
            typing...
          </span>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator; 