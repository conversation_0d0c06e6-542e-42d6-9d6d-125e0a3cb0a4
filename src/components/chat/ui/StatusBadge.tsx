import React from 'react';
import { cn } from '../../../lib/utils';

export type OnlineStatus = 'online' | 'offline' | 'away' | 'busy' | 'unknown';

export interface StatusBadgeProps {
  status: OnlineStatus;
  lastSeen?: Date | string | undefined;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  lastSeen,
  className,
  showText = false,
  size = 'md',
}) => {
  const formatLastSeen = (timestamp: Date | string | undefined) => {
    if (!timestamp) return 'unknown';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getStatusColor = () => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      case 'offline':
        return 'bg-chat-gray-400';
      default:
        return 'bg-chat-gray-300';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'online':
        return 'online';
      case 'away':
        return 'away';
      case 'busy':
        return 'busy';
      case 'offline':
        return lastSeen ? `last seen ${formatLastSeen(lastSeen)}` : 'offline';
      default:
        return 'unknown';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  if (showText) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div
          className={cn(
            'rounded-full border-2 border-white shadow-sm',
            getSizeClasses(),
            getStatusColor()
          )}
        />
        <span className="text-xs text-chat-gray-600 select-none">
          {getStatusText()}
        </span>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'rounded-full border-2 border-white shadow-sm',
        getSizeClasses(),
        getStatusColor(),
        className
      )}
      title={getStatusText()}
    />
  );
};

export default StatusBadge; 