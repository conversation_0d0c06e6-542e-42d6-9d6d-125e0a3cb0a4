import React, { useMemo } from 'react';
import { cn } from '../../lib/utils';
import { ConversationMessage } from '../../hooks/useConversationMessages';
import { useMediaImage, useMedia } from '@/hooks/useAuthenticatedMedia';
import { 
  parseMessageContent, 
  getMediaDescription, 
  getContentIcon,
  CONTENT_TYPE_TEXT,
  CONTENT_TYPE_LOCATION,
  CONTENT_TYPE_CONTACTS,
  CONTENT_TYPE_IMAGE,
  CONTENT_TYPE_VIDEO,
  CONTENT_TYPE_DOCUMENT,
  CONTENT_TYPE_AUDIO,
  CONTENT_TYPE_STICKER,
  ParsedContent,
  ParsedLocationContent,
  ParsedMediaContent
} from '../../utils/messageContentParser';
import ContactMessage from './ContactMessage';

export interface MessageBubbleProps {
  message: ConversationMessage;
  isOutbound: boolean;
  timeLabel: string;
  className?: string;
  isGrouped?: boolean;
  showTimestamp?: boolean;
  showStatus?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOutbound,
  timeLabel,
  className,
  isGrouped = false,
  showTimestamp = true,
  showStatus = true,
}) => {
  const bubbleClasses = cn(
    'relative max-w-full break-words',
    'px-3 py-1.5', // More compact padding like WhatsApp
    // WhatsApp-style rounded corners - more subtle than before
    className || 'rounded-[8px]', // Use provided className or default
    isOutbound
      ? [
          'bg-chat-outbound text-chat-outbound-text',
          !className && 'rounded-br-[3px]', // Sharp corner at bottom right for outbound (only if no custom className)
          'shadow-[0_1px_0.5px_rgba(0,0,0,0.13)]'
        ]
      : [
          'bg-chat-inbound text-chat-inbound-text',
          !className && 'rounded-bl-[3px]', // Sharp corner at bottom left for inbound (only if no custom className)
          'shadow-[0_1px_0.5px_rgba(0,0,0,0.13)]'
        ]
  );

  // Parse content once and memoize the result
  const parsedContent = useMemo(() => {
    return parseMessageContent(message.content);
  }, [message.content]);

  const renderMessageContent = () => {
    // If content parsing succeeded, use parsed content
    if (parsedContent) {
      switch (parsedContent.type) {
        case CONTENT_TYPE_TEXT:
          return <TextMessage content={parsedContent.text} />;
        case CONTENT_TYPE_LOCATION:
          return <LocationMessage parsedContent={parsedContent} message={message} />;
        case CONTENT_TYPE_CONTACTS:
          return <ContactMessage parsedContent={parsedContent} />;
        case CONTENT_TYPE_IMAGE:
        case CONTENT_TYPE_VIDEO:
        case CONTENT_TYPE_DOCUMENT:
        case CONTENT_TYPE_AUDIO:
        case CONTENT_TYPE_STICKER:
          return <MediaMessage parsedContent={parsedContent} message={message} />;
        default:
          return <TextMessage content={message.textContent || message.content} />;
      }
    }

    // Fallback to message type-based rendering for unparsed content
    switch (message.messageType?.toLowerCase()) {
      case 'text':
        return <TextMessage content={message.textContent || message.content} />;
      case 'image':
        return <ImageMessage message={message} />;
      case 'document':
        return <DocumentMessage message={message} />;
      case 'audio':
        return <AudioMessage message={message} />;
      case 'video':
        return <VideoMessage message={message} />;
      case 'location':
        return <LocationMessageFallback message={message} />;
      case 'contact':
        return <ContactMessageFallback />;
      case 'sticker':
        return <StickerMessage message={message} />;
      case 'template':
        return <TemplateMessage message={message} />;
      default:
        return <TextMessage content={message.textContent || message.content} />;
    }
  };

  return (
    <div className={bubbleClasses}>
      {/* Message content */}
      <div className={cn(
        showTimestamp || showStatus ? "mb-1" : "",
      )}>
        {renderMessageContent()}
      </div>
      
      {/* Timestamp and status - WhatsApp style at bottom right */}
      {(showTimestamp || showStatus) && (
        <div
          className={cn(
            'flex items-center justify-end gap-1 mt-1',
            'text-xs leading-none',
            isOutbound 
              ? 'text-chat-outbound-meta' 
              : 'text-chat-inbound-meta'
          )}
        >
          {showTimestamp && (
            <span className="select-none">{timeLabel}</span>
          )}
          
          {/* WhatsApp-style delivery status for outbound messages */}
          {isOutbound && showStatus && (
            <div className="flex items-center ml-1">
              {/* Enhanced status indicators */}
              <MessageStatusIndicator message={message} />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Enhanced message status indicator component
const MessageStatusIndicator: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  const getStatusIcon = () => {
    const status = message.status?.toLowerCase() || 'sent';
    
    switch (status) {
      case 'sending':
        return (
          <svg className="w-4 h-4 text-chat-gray-400 animate-spin" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        );
      case 'sent':
        return (
          <svg className="w-4 h-4 text-chat-gray-500" viewBox="0 0 16 15" fill="currentColor">
            <path d="M10.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      case 'delivered':
        return (
          <svg className="w-4 h-4 text-chat-gray-500" viewBox="0 0 16 15" fill="currentColor">
            <path d="M10.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
            <path d="M13.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L7.566 9.879a.32.32 0 0 1-.484.033L4.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      case 'read':
        return (
          <svg className="w-4 h-4 text-chat-delivered" viewBox="0 0 16 15" fill="currentColor">
            <path d="M10.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
            <path d="M13.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L7.566 9.879a.32.32 0 0 1-.484.033L4.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-4 h-4 text-chat-error" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-chat-gray-400" viewBox="0 0 16 15" fill="currentColor">
            <path d="M10.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
    }
  };

  return getStatusIcon();
};

// Enhanced text message component
const TextMessage: React.FC<{ content: string }> = ({ content }) => {
  if (!content) return null;

  // Enhanced URL detection and link rendering with WhatsApp-style formatting
  const renderTextWithLinks = (text: string) => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const parts = text.split(urlRegex);
    
    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        return (
          <a
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className={cn(
              "text-blue-600 hover:text-blue-800",
              "underline hover:no-underline",
              "break-all" // Ensure long URLs wrap properly
            )}
          >
            {part}
          </a>
        );
      }
      return part;
    });
  };

  return (
    <div className={cn(
      "whitespace-pre-wrap",
      "text-[14px] leading-[1.3]", // Exact WhatsApp text sizing
      "word-wrap break-words",
      "max-w-none"
    )}>
      {renderTextWithLinks(content)}
    </div>
  );
};

// Enhanced location message component using parsed content
const LocationMessage: React.FC<{ 
  parsedContent: ParsedLocationContent; 
  message: ConversationMessage;
}> = ({ parsedContent, message }) => {
  const { latitude, longitude, name, address } = parsedContent;
  
  // Use parsed coordinates first, then fall back to message fields
  const lat = latitude || message.latitude;
  const lng = longitude || message.longitude;
  const locationName = name || message.locationName;
  const locationAddress = address || message.locationAddress;

  if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
    return <TextMessage content={`Location: ${lat}, ${lng}`} />;
  }

  const mapUrl = `https://maps.google.com/maps?q=${lat},${lng}&z=15&output=embed`;
  const directionsUrl = `https://maps.google.com/maps?q=${lat},${lng}`;

  return (
    <div>
      <div className="bg-gray-50 rounded-lg overflow-hidden mb-2">
        <iframe
          src={mapUrl}
          width="100%"
          height="200"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title={`Location: ${lat}, ${lng}`}
        />
      </div>
      <div>
        {locationName && (
          <div className="font-medium text-sm mb-1">
            {locationName}
          </div>
        )}
        {locationAddress && (
          <div className="text-sm text-gray-600 mb-2">
            {locationAddress}
          </div>
        )}
        <div className="text-xs text-gray-500 mb-2">
          📍 {lat.toFixed(6)}, {lng.toFixed(6)}
        </div>
        <a
          href={directionsUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
        >
          <LocationIcon className="w-4 h-4 mr-1" />
          View in Maps
        </a>
      </div>
    </div>
  );
};

// Unified media message component using parsed content
const MediaMessage: React.FC<{ 
  parsedContent: ParsedMediaContent; 
  message: ConversationMessage;
}> = ({ parsedContent, message }) => {
  const { type, s3_url, mime_type, caption } = parsedContent;
  const mediaUrl = s3_url || message.mediaUrl;
  const description = getMediaDescription(parsedContent);
  const icon = getContentIcon(parsedContent);

  if (!mediaUrl) {
    return (
      <div className="flex items-center p-3 bg-gray-50 rounded-lg">
        <div className="text-2xl mr-3">{icon}</div>
        <div className="text-sm text-gray-600">
          {description} (unavailable)
        </div>
      </div>
    );
  }

  switch (type) {
    case CONTENT_TYPE_IMAGE:
      return (
        <div className="overflow-hidden rounded-lg max-w-full">
          <div className="relative group">
            <img
              src={mediaUrl}
              alt={description}
              className="rounded-lg max-w-full h-auto max-h-80 object-cover cursor-pointer transition-transform duration-200 group-hover:scale-[1.02]"
              loading="lazy"
              onClick={() => {
                // TODO: Open in lightbox/modal
                window.open(mediaUrl, '_blank');
              }}
            />
            {/* Image overlay for preview */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg pointer-events-none" />
            
            {/* Download button overlay */}
            <button
              className="absolute top-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-black/70"
              onClick={(e) => {
                e.stopPropagation();
                window.open(mediaUrl, '_blank');
              }}
              title="Download image"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-5-8h6a2 2 0 012 2v6" />
              </svg>
            </button>
          </div>
          {caption && (
            <div className="text-sm mt-2 text-chat-gray-700">
              {caption}
            </div>
          )}
        </div>
      );

    case CONTENT_TYPE_STICKER:
      return (
        <div className="overflow-hidden rounded-lg max-w-fit">
          <div className="relative group">
            <img
              src={mediaUrl}
              alt="Sticker"
              className="rounded-lg max-w-full h-auto max-h-48 max-w-48 object-contain cursor-pointer transition-transform duration-200 group-hover:scale-[1.05]"
              loading="lazy"
              onClick={() => {
                window.open(mediaUrl, '_blank');
              }}
            />
            {/* Subtle overlay for stickers */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-colors duration-200 rounded-lg pointer-events-none" />
          </div>
          {/* Stickers typically don't have captions in WhatsApp */}
        </div>
      );

    case CONTENT_TYPE_VIDEO:
      return (
        <div className="overflow-hidden rounded-lg max-w-full">
          <div className="relative group">
            <video
              controls
              className="rounded-lg max-w-full h-auto max-h-80 cursor-pointer"
              preload="metadata"
              poster={mediaUrl + '#t=0.5'} // Generate thumbnail at 0.5 seconds
            >
              <source src={mediaUrl} type={mime_type || 'video/mp4'} />
              <source src={mediaUrl} type="video/webm" />
              Your browser does not support the video element.
            </video>
            
            {/* Video play overlay */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="bg-black/50 rounded-full p-3 group-hover:bg-black/70 transition-colors duration-200">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>
          {caption && (
            <div className="text-sm mt-2 text-chat-gray-700">
              {caption}
            </div>
          )}
        </div>
      );

    case CONTENT_TYPE_AUDIO:
      return (
        <div className="bg-chat-gray-50 rounded-lg p-3 min-w-[200px] max-w-[300px]">
          <div className="flex items-center gap-3">
            {/* Audio icon */}
            <div className="w-10 h-10 bg-chat-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-5 h-5 text-chat-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M9 9a3 3 0 000 6h3v5a1 1 0 102 0v-7.5A1.5 1.5 0 0012.5 12H9z" />
              </svg>
            </div>
            
            {/* Audio controls */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-medium text-chat-gray-900 truncate">
                  Audio message
                </span>
                <span className="text-xs text-chat-gray-500 flex-shrink-0">
                  0:30 {/* TODO: Get actual duration */}
                </span>
              </div>
              
              {/* Waveform placeholder */}
              <div className="flex items-center gap-0.5 mb-2">
                {Array.from({ length: 20 }).map((_, i) => (
                  <div 
                    key={i}
                    className="w-1 bg-chat-gray-300 rounded-full"
                    style={{ 
                      height: `${Math.random() * 16 + 4}px`,
                      opacity: i < 6 ? 1 : 0.5 // Show progress
                    }}
                  />
                ))}
              </div>
              
              {/* Actual audio element (hidden) */}
              <audio controls className="w-full h-6 opacity-75">
                <source src={mediaUrl} type={mime_type || 'audio/mpeg'} />
                <source src={mediaUrl} type="audio/ogg" />
                Your browser does not support the audio element.
              </audio>
            </div>
          </div>
          {caption && (
            <div className="text-sm mt-2 text-chat-gray-700 border-t border-chat-gray-200 pt-2">
              {caption}
            </div>
          )}
        </div>
      );

    case CONTENT_TYPE_DOCUMENT:
      const fileName = message.fileName || getFileNameFromUrl(mediaUrl) || description;
      const fileExtension = fileName.split('.').pop()?.toUpperCase() || 'FILE';
      const fileSize = (message as any).fileSize ? formatFileSize((message as any).fileSize) : '';
      
      return (
        <div className="bg-chat-gray-50 rounded-lg p-3 min-w-[200px] max-w-[300px]">
          <div className="flex items-center gap-3">
            {/* Enhanced file icon based on type */}
            <div className="w-12 h-12 bg-chat-primary/10 rounded-lg flex flex-col items-center justify-center flex-shrink-0">
              <div className="text-xs font-bold text-chat-primary mb-0.5">
                {fileExtension}
              </div>
              <svg className="w-4 h-4 text-chat-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            
            {/* File details */}
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-chat-gray-900 truncate mb-1">
                {fileName}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-chat-gray-500">
                  {fileSize || 'Document'}
                </span>
                <a
                  href={mediaUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-chat-primary hover:text-chat-primary/80 transition-colors duration-200"
                  title={`Download ${fileName}`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-5-8h6a2 2 0 012 2v6" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
          {caption && (
            <div className="text-sm mt-2 text-chat-gray-700 border-t border-chat-gray-200 pt-2">
              {caption}
            </div>
          )}
        </div>
      );

    default:
      return <TextMessage content={description} />;
  }
};

// Fallback components for unparsed content
const ImageMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  if (!message.mediaUrl) {
    return <TextMessage content={message.content} />;
  }

  // Extract media info for authentication
  const mediaInfo = extractMediaInfo(message);
  const { imageUrl, isLoading, error } = useMediaImage({
    mediaUrl: message.mediaUrl,
    ...mediaInfo
  });

  // Try to extract meaningful description from content
  let description = 'Image';
  try {
    const parsed = JSON.parse(message.content);
    if (parsed.mime_type) {
      const extension = parsed.mime_type.split('/')[1];
      description = `Image (${extension?.toUpperCase()})`;
    }
  } catch {
    // Use default description
  }

  if (error) {
    return (
      <div className="p-3 bg-red-50 rounded-lg">
        <div className="text-red-600 text-sm">Failed to load image</div>
        <div className="text-xs text-red-500 mt-1">{error}</div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-2 relative">
        {isLoading ? (
          <div className="w-full h-40 bg-gray-200 rounded-lg flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-chat-primary"></div>
          </div>
        ) : (
          <img
            src={imageUrl || message.mediaUrl}
            alt={description}
            className="rounded-lg max-w-full h-auto max-h-80 object-cover cursor-pointer hover:opacity-90 transition-opacity"
            loading="lazy"
            onClick={() => {
              if (imageUrl) {
                window.open(imageUrl, '_blank');
              }
            }}
          />
        )}
      </div>
      {message.caption && (
        <div className="text-sm">
          {message.caption}
        </div>
      )}
    </div>
  );
};

// Document message component (fallback)
const DocumentMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  const fileName = message.fileName || 'Document';
  const fileIcon = getFileIcon(fileName);
  
  // Extract media info for authentication
  const mediaInfo = extractMediaInfo(message);
  const { download, isLoading } = useMedia({
    mediaUrl: message.mediaUrl,
    ...mediaInfo
  });

  const handleDownload = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (!message.mediaUrl) return;

    try {
      await download(fileName);
    } catch (error) {
      console.error('❌ Failed to download document:', error);
      // Fallback to direct link
      window.open(message.mediaUrl, '_blank');
    }
  };

  return (
    <div>
      <div className="flex items-center p-3 bg-gray-50 rounded-lg mb-2">
        <div className="text-2xl mr-3">{fileIcon}</div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {fileName}
          </div>
          <div className="text-xs text-gray-500">
            Document
          </div>
        </div>
        {message.mediaUrl && (
          <button
            onClick={handleDownload}
            disabled={isLoading}
            className="ml-2 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Download document"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            ) : (
              <DownloadIcon className="w-5 h-5" />
            )}
          </button>
        )}
      </div>
      {message.caption && (
        <div className="text-sm">
          {message.caption}
        </div>
      )}
    </div>
  );
};

// Audio message component (fallback)
const AudioMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  if (!message.mediaUrl) {
    return <TextMessage content={message.content} />;
  }

  return (
    <div>
      <div className="flex items-center p-3 bg-gray-50 rounded-lg mb-2">
        <AudioIcon className="w-6 h-6 text-gray-600 mr-3" />
        <div className="flex-1">
          <audio controls className="w-full">
            <source src={message.mediaUrl} type="audio/mpeg" />
            <source src={message.mediaUrl} type="audio/ogg" />
            Your browser does not support the audio element.
          </audio>
        </div>
      </div>
      {message.caption && (
        <div className="text-sm">
          {message.caption}
        </div>
      )}
    </div>
  );
};

// Video message component (fallback)
const VideoMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  if (!message.mediaUrl) {
    return <TextMessage content={message.content} />;
  }

  return (
    <div>
      <div className="mb-2">
        <video
          controls
          className="rounded-lg max-w-full h-auto max-h-80"
          preload="metadata"
        >
          <source src={message.mediaUrl} type="video/mp4" />
          <source src={message.mediaUrl} type="video/webm" />
          Your browser does not support the video element.
        </video>
      </div>
      {message.caption && (
        <div className="text-sm">
          {message.caption}
        </div>
      )}
    </div>
  );
};

// Sticker message component (fallback)
const StickerMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  if (!message.mediaUrl) {
    return <TextMessage content={message.content} />;
  }

  // Extract media info for authentication
  const mediaInfo = extractMediaInfo(message);
  const { imageUrl, isLoading, error } = useMediaImage({
    mediaUrl: message.mediaUrl,
    ...mediaInfo
  });

  if (error) {
    return (
      <div className="p-3 bg-red-50 rounded-lg">
        <div className="text-red-600 text-sm">Failed to load sticker</div>
        <div className="text-xs text-red-500 mt-1">{error}</div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg max-w-fit">
      <div className="relative group">
        {isLoading ? (
          <div className="w-32 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-chat-primary"></div>
          </div>
        ) : (
          <img
            src={imageUrl || message.mediaUrl}
            alt="Sticker"
            className="rounded-lg max-w-full h-auto max-h-48 max-w-48 object-contain cursor-pointer transition-transform duration-200 group-hover:scale-[1.05]"
            loading="lazy"
            onClick={() => {
              if (imageUrl) {
                window.open(imageUrl, '_blank');
              }
            }}
          />
        )}
        {/* Subtle overlay for stickers */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-colors duration-200 rounded-lg pointer-events-none" />
      </div>
      {/* Stickers typically don't have captions in WhatsApp */}
    </div>
  );
};

// Location message component (fallback)
const LocationMessageFallback: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  const { latitude, longitude, locationName, locationAddress } = message;

  // Try to parse coordinates from content if not in message fields
  let lat = latitude;
  let lng = longitude;
  
  if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
    try {
      const parsed = JSON.parse(message.content);
      lat = parsed.latitude;
      lng = parsed.longitude;
    } catch {
      return <TextMessage content={message.content} />;
    }
  }

  if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
    return <TextMessage content={message.content} />;
  }

  const mapUrl = `https://maps.google.com/maps?q=${lat},${lng}&z=15&output=embed`;
  const directionsUrl = `https://maps.google.com/maps?q=${lat},${lng}`;

  return (
    <div>
      <div className="bg-gray-50 rounded-lg overflow-hidden mb-2">
        <iframe
          src={mapUrl}
          width="100%"
          height="200"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title={`Location: ${lat}, ${lng}`}
        />
      </div>
      <div>
        {locationName && (
          <div className="font-medium text-sm mb-1">
            {locationName}
          </div>
        )}
        {locationAddress && (
          <div className="text-sm text-gray-600 mb-2">
            {locationAddress}
          </div>
        )}
        <div className="text-xs text-gray-500 mb-2">
          📍 {lat.toFixed(6)}, {lng.toFixed(6)}
        </div>
        <a
          href={directionsUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
        >
          <LocationIcon className="w-4 h-4 mr-1" />
          View in Maps
        </a>
      </div>
    </div>
  );
};

// Contact message fallback for unparsed content
const ContactMessageFallback: React.FC = () => {
  return (
    <div className="flex items-center p-3 bg-gray-50 rounded-lg">
      <div className="text-2xl mr-3">👤</div>
      <div className="flex-1">
        <div className="text-sm font-medium text-gray-900">
          Contact Shared
        </div>
        <div className="text-xs text-gray-500">
          Contact information was shared in this conversation
        </div>
      </div>
    </div>
  );
};

// Template message component
const TemplateMessage: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  return (
    <div className="border-l-4 border-blue-500 pl-3">
      <div className="text-xs text-gray-500 mb-1">
        Template: {message.templateName}
      </div>
      <TextMessage content={message.textContent || message.content} />
    </div>
  );
};

// Utility functions
const getFileNameFromUrl = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const segments = pathname.split('/');
    return segments[segments.length - 1] || null;
  } catch {
    return null;
  }
};

/**
 * Extract media info from message object for easier use with hooks
 */
function extractMediaInfo(message: any): {
  organizationId?: string;
  phoneId?: string;
  mediaId?: string;
} {
  // Try to extract from message properties
  return {
    organizationId: message?.organizationId || message?.storeId || message?.store_id,
    phoneId: message?.phoneNumberId || message?.phone_number_id || message?.businessPhone || message?.business_phone,
    mediaId: message?.mediaId || message?.media_id || extractMediaIdFromUrl(message?.mediaUrl)
  };
}

/**
 * Extract media ID from URL patterns
 */
function extractMediaIdFromUrl(url?: string): string | undefined {
  if (!url) return undefined;

  // Try to extract from S3 URL pattern
  if (url.includes('.s3.') && url.includes('.amazonaws.com/')) {
    const pathParts = url.split('/');
    if (pathParts.length >= 6) {
      return pathParts[pathParts.length - 2]; // media_id is second to last part
    }
  }

  // Try to extract from proxy URL pattern
  if (url.includes('/apis/v1/media/')) {
    const match = url.match(/\/apis\/v1\/media\/[^\/]+\/[^\/]+\/([^\/\?]+)/);
    return match?.[1];
  }

  return undefined;
}

// Utility function to get file icon based on file extension (fallback)
const getFileIcon = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'ppt':
    case 'pptx':
      return '📺';
    case 'zip':
    case 'rar':
      return '🗜️';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return '🖼️';
    case 'mp3':
    case 'wav':
      return '🎵';
    case 'mp4':
    case 'avi':
      return '🎬';
    default:
      return '📁';
  }
};

// Utility function to format file size
const formatFileSize = (bytes: number, decimalPoint = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimalPoint < 0 ? 0 : decimalPoint;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// Simple icons (you can replace with your preferred icon library)
const DownloadIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
  </svg>
);

const AudioIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clipRule="evenodd" />
  </svg>
);

const LocationIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
  </svg>
); 