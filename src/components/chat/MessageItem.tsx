import React from 'react';
import { cn } from '../../lib/utils';
import { ConversationMessage } from '../../hooks/useConversationMessages';
import { MessageBubble } from './MessageBubble';
import { format, isToday, isYesterday, isSameDay } from 'date-fns';

export interface MessageItemProps {
  message: ConversationMessage;
  className?: string;
  previousMessage?: ConversationMessage;
  nextMessage?: ConversationMessage;
  showSenderName?: boolean;
  isGrouped?: boolean;
}

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  className,
  previousMessage,
  nextMessage,
  showSenderName = true,
  isGrouped = false,
}) => {
  const messageDate = new Date(message.timestamp);
  const isOutbound = message.direction === 'OUTBOUND';
  const isInbound = !isOutbound;

  // Enhanced time formatting for WhatsApp-style display
  const formatTime = (date: Date) => {
    return format(date, 'HH:mm');
  };

  // Enhanced date label logic for separators
  const shouldShowDateSeparator = () => {
    if (!previousMessage) return true;
    
    const prevDate = new Date(previousMessage.timestamp);
    return !isSameDay(messageDate, prevDate);
  };

  // Smart message grouping logic
  const isFirstInGroup = () => {
    if (!previousMessage) return true;
    
    const prevDate = new Date(previousMessage.timestamp);
    const timeDiff = messageDate.getTime() - prevDate.getTime();
    const isFromSameSender = message.direction === previousMessage.direction;
    
    // Group messages if they're from the same sender and within 5 minutes
    return !isFromSameSender || timeDiff > 5 * 60 * 1000; // 5 minutes
  };

  const isLastInGroup = () => {
    if (!nextMessage) return true;
    
    const nextDate = new Date(nextMessage.timestamp);
    const timeDiff = nextDate.getTime() - messageDate.getTime();
    const isToSameSender = message.direction === nextMessage.direction;
    
    return !isToSameSender || timeDiff > 5 * 60 * 1000; // 5 minutes
  };

  const formatDateSeparator = (date: Date) => {
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  const timeLabel = formatTime(messageDate);
  const dateLabel = shouldShowDateSeparator() ? formatDateSeparator(messageDate) : null;
  const firstInGroup = isFirstInGroup();
  const lastInGroup = isLastInGroup();

  // Enhanced spacing logic for message clustering
  const getMessageSpacing = () => {
    if (firstInGroup && lastInGroup) return 'mb-3'; // Single message
    if (firstInGroup) return 'mb-1'; // First in group
    if (lastInGroup) return 'mb-3'; // Last in group
    return 'mb-0.5'; // Middle of group - very tight spacing
  };

  // Bubble corner radius adjustments for grouped messages
  const getBubbleClassName = () => {
    let baseClasses = '';
    
    if (isGrouped) {
      if (isOutbound) {
        if (firstInGroup && lastInGroup) {
          baseClasses = 'rounded-[18px] rounded-br-[4px]'; // Single message
        } else if (firstInGroup) {
          baseClasses = 'rounded-[18px] rounded-br-[4px] rounded-tr-[18px]'; // First in group
        } else if (lastInGroup) {
          baseClasses = 'rounded-[18px] rounded-br-[4px] rounded-tr-[4px]'; // Last in group
        } else {
          baseClasses = 'rounded-[4px] rounded-l-[18px]'; // Middle of group
        }
      } else {
        if (firstInGroup && lastInGroup) {
          baseClasses = 'rounded-[18px] rounded-bl-[4px]'; // Single message
        } else if (firstInGroup) {
          baseClasses = 'rounded-[18px] rounded-bl-[4px] rounded-tl-[18px]'; // First in group
        } else if (lastInGroup) {
          baseClasses = 'rounded-[18px] rounded-bl-[4px] rounded-tl-[4px]'; // Last in group
        } else {
          baseClasses = 'rounded-[4px] rounded-r-[18px]'; // Middle of group
        }
      }
    }
    
    return baseClasses;
  };

  return (
    <div className={cn('w-full animate-messageSlideIn', className)}>
      {/* Enhanced date separator */}
      {dateLabel && (
        <div className="flex justify-center my-4">
          <div className={cn(
            "bg-chat-gray-100 text-chat-gray-700 text-xs px-3 py-1.5 rounded-full",
            "shadow-sm border border-chat-gray-200 font-medium",
            "select-none animate-fadeIn" // Prevent text selection on date separators
          )}>
            {dateLabel}
          </div>
        </div>
      )}

      {/* Message container with enhanced spacing */}
      <div
        className={cn(
          'flex w-full',
          getMessageSpacing(),
          isOutbound ? 'justify-end pr-2' : 'justify-start pl-2'
        )}
      >
        <div
          className={cn(
            // WhatsApp exact widths and constraints
            'max-w-[85%] sm:max-w-[75%] md:max-w-[65%]', // Responsive max widths
            'min-w-[48px]', // Minimum width for short messages
          )}
        >
          {/* Enhanced sender name for both inbound and outbound messages */}
          {showSenderName && firstInGroup && message.contactName && (
            <div className={cn(
              "text-xs font-semibold mb-1 px-3 truncate cursor-pointer transition-colors duration-150",
              isInbound ? [
                "text-chat-primary hover:text-chat-primary-light"
              ] : [
                "text-chat-gray-700 text-right"
              ]
            )}>
              {isOutbound ? `You` : message.contactName}
            </div>
          )}

          {/* Enhanced message bubble with grouping support */}
          <MessageBubble
            message={message}
            isOutbound={isOutbound}
            timeLabel={timeLabel}
            className={getBubbleClassName()}
            isGrouped={isGrouped}
            showTimestamp={lastInGroup} // Only show timestamp on last message in group
            showStatus={isOutbound && lastInGroup} // Only show status on last outbound message
          />
        </div>
      </div>
    </div>
  );
};

/*
// Message status component for outbound messages
interface MessageStatusProps {
  status: string;
  deliveredAt?: string;
  readAt?: string;
}
*/

// Unused status component - commented out to fix build
/*
const MessageStatus: React.FC<MessageStatusProps> = ({ 
  status, 
  deliveredAt, 
  readAt 
}) => {
  const getStatusIcon = () => {
    if (readAt) {
      return (
        <div className="flex">
          <CheckIcon className="w-3 h-3 text-blue-500" />
          <CheckIcon className="w-3 h-3 text-blue-500 -ml-1" />
        </div>
      );
    } else if (deliveredAt) {
      return (
        <div className="flex">
          <CheckIcon className="w-3 h-3 text-gray-400" />
          <CheckIcon className="w-3 h-3 text-gray-400 -ml-1" />
        </div>
      );
    } else if (status === 'sent') {
      return <CheckIcon className="w-3 h-3 text-gray-400" />;
    } else if (status === 'failed') {
      return <ExclamationIcon className="w-3 h-3 text-red-500" />;
    } else {
      return <ClockIcon className="w-3 h-3 text-gray-300" />;
    }
  };

  return (
    <div className="flex items-center">
      {getStatusIcon()}
    </div>
  );
};
*/

/*
// Simple icons (you can replace with your preferred icon library)
const CheckIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
  </svg>
);

const ClockIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
  </svg>
);

const ExclamationIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
  </svg>
);
*/ 