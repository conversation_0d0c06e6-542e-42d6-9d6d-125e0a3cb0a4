import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { 
  User, 
  Phone, 
  Building2, 
  Tag, 
  ChevronDown,
  ChevronUp,
  Loader2,
  CheckCircle2,
  X
} from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useWhatsAppConnections } from '@/hooks/useGraphQL';
import { CreateCustomerInput } from '@/lib/graphql/mutations/crmMutations';

// Validation schema
const createCustomerSchema = z.object({
  customerName: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .regex(/^[a-zA-Z\s'-]+$/, "Name can only contain letters, spaces, hyphens, and apostrophes"),
  
  customerPhone: z
    .string()
    .regex(/^\+?[1-9]\d{1,14}$/, "Please enter a valid phone number with country code")
    .min(10, "Phone number must be at least 10 digits"),
  
  businessPhoneId: z
    .string()
    .min(1, "Please select a business phone number"),
  
  tags: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true;
      const tags = val.split(',').map(tag => tag.trim());
      return tags.every(tag => tag.length >= 2 && tag.length <= 20);
    }, "Each tag must be 2-20 characters"),
});

type CreateCustomerFormData = z.infer<typeof createCustomerSchema>;

interface CreateCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateCustomer: (input: CreateCustomerInput) => Promise<void>;
}

export const CreateCustomerModal: React.FC<CreateCustomerModalProps> = ({
  isOpen,
  onClose,
  onCreateCustomer
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Fetch WhatsApp connections for the organization
  const { data: whatsAppConnections, loading: connectionsLoading } = useWhatsAppConnections(
    user?.organizationId, 
    'ACTIVE'
  );

  const form = useForm<CreateCustomerFormData>({
    resolver: zodResolver(createCustomerSchema),
    defaultValues: {
      customerName: '',
      customerPhone: '',
      businessPhoneId: '',
      tags: '',
    },
    mode: 'onChange',
  });

  const { formState: { errors, isValid } } = form;

  // Auto-format phone number
  const handlePhoneChange = (value: string, field: any) => {
    // Remove all non-digits except plus sign
    const cleaned = value.replace(/[^\d+]/g, '');
    
    // Add + if not present and starts with a digit
    const formatted = cleaned.startsWith('+') ? cleaned : cleaned ? `+${cleaned}` : '';
    
    field.onChange(formatted);
  };

  // Parse and validate tags
  const parsedTags = form.watch('tags')
    ?.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0) || [];

  const onSubmit = async (data: CreateCustomerFormData) => {
    if (!user?.organizationId) {
      toast({
        title: "Error",
        description: "No organization found. Please try logging in again.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const input: CreateCustomerInput = {
        phoneId: data.businessPhoneId,
        organizationId: user.organizationId,
        customerPhone: data.customerPhone,
        customerName: data.customerName,
        tags: parsedTags.length > 0 ? parsedTags : undefined,
      };

      await onCreateCustomer(input);
      
      // Show success state
      setSubmitSuccess(true);
      
      toast({
        title: "Customer created successfully!",
        description: `${data.customerName} has been added to your CRM.`,
      });

      // Reset form and close after a short delay
      setTimeout(() => {
        form.reset();
        setSubmitSuccess(false);
        setShowAdvanced(false);
        onClose();
      }, 1500);
      
    } catch (error) {
      console.error('Error creating customer:', error);
      toast({
        title: "Failed to create customer",
        description: "Please check your information and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      form.reset();
      setShowAdvanced(false);
      setSubmitSuccess(false);
    }
  }, [isOpen, form]);

  // Get available phone connections for dropdown
  const availableConnections = whatsAppConnections?.filter(conn => 
    conn.status === 'ACTIVE' && conn.isVerified
  ) || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className="w-full max-w-md sm:max-w-lg mx-auto max-h-[85vh] overflow-hidden flex flex-col p-0 
                   bg-white shadow-2xl rounded-lg border-0 z-[9999] 
                   fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        {/* Header */}
        <DialogHeader className="space-y-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl sm:text-2xl font-semibold flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              Create New Customer
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-sm text-gray-600 leading-relaxed">
            Add a new customer to your CRM system with their contact information and preferences.
          </DialogDescription>
        </DialogHeader>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="px-6 py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                
                {/* Essential Information */}
                <div className="space-y-5">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                    <h3 className="font-medium text-base text-gray-900">Essential Information</h3>
                  </div>
                  
                  <div className="space-y-5 pl-4">
                    {/* Customer Name */}
                    <FormField
                      control={form.control}
                      name="customerName"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Customer Name *
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                placeholder="Enter full name"
                                className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                autoComplete="name"
                              />
                            </div>
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            The customer's full name as it should appear in your CRM
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Customer Phone */}
                    <FormField
                      control={form.control}
                      name="customerPhone"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Phone Number *
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                type="tel"
                                placeholder="****** 123 4567"
                                className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                autoComplete="tel"
                                onChange={(e) => handlePhoneChange(e.target.value, field)}
                              />
                            </div>
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Include country code (e.g., +1 for US, +44 for UK)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Business Phone Selection */}
                    <FormField
                      control={form.control}
                      name="businessPhoneId"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Business Phone Number *
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
                              <Select onValueChange={field.onChange} value={field.value}>
                                <SelectTrigger className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                                  <SelectValue placeholder="Select connected business phone" />
                                </SelectTrigger>
                                <SelectContent>
                                  {connectionsLoading ? (
                                    <SelectItem value="loading" disabled>
                                      <div className="flex items-center gap-2">
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        Loading phone numbers...
                                      </div>
                                    </SelectItem>
                                  ) : availableConnections.length === 0 ? (
                                    <SelectItem value="empty" disabled>
                                      No active phone numbers found
                                    </SelectItem>
                                  ) : (
                                    availableConnections.map((connection) => (
                                      <SelectItem key={connection.id} value={connection.phoneNumberId}>
                                        <div className="flex flex-col">
                                          <span className="font-medium">{connection.displayName}</span>
                                          <span className="text-xs text-gray-500">{connection.phoneNumber}</span>
                                          {connection.storeConnections && connection.storeConnections.length > 0 && (
                                            <span className="text-xs text-green-600">
                                              Connected to {connection.storeConnections.length} store(s)
                                            </span>
                                          )}
                                        </div>
                                      </SelectItem>
                                    ))
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Select which business phone number will be used to communicate with this customer
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <Separator />

                {/* Advanced Options */}
                <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                  <CollapsibleTrigger asChild>
                    <div className="w-full cursor-pointer py-3 px-4 rounded-lg border border-dashed border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                          <span className="text-sm font-medium text-gray-700">Advanced Options</span>
                          <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">Optional</Badge>
                        </div>
                        {showAdvanced ? (
                          <ChevronUp className="h-4 w-4 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-500" />
                        )}
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="space-y-5 pl-4 pt-4">
                    {/* Customer Tags */}
                    <FormField
                      control={form.control}
                      name="tags"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Customer Tags
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                              <Input
                                {...field}
                                placeholder="vip, loyal, premium"
                                className="pl-10 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                              />
                            </div>
                          </FormControl>
                          <FormDescription className="text-xs text-gray-500">
                            Separate multiple tags with commas
                          </FormDescription>
                          {parsedTags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {parsedTags.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CollapsibleContent>
                </Collapsible>
              </form>
            </Form>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex-shrink-0 p-6 pt-4 border-t border-gray-100 bg-gray-50">
          {/* Form validation summary */}
          {Object.keys(errors).length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-xs text-red-600 font-medium mb-1">
                Please fix the following errors:
              </p>
              <ul className="text-xs text-red-600 space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>• {error.message}</li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="flex flex-col-reverse sm:flex-row gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="h-12 px-6 bg-white hover:bg-gray-50 border-gray-300"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting || !isValid || submitSuccess || availableConnections.length === 0}
              className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              {submitSuccess ? (
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4" />
                  Customer Created!
                </div>
              ) : isSubmitting ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating Customer...
                </div>
              ) : availableConnections.length === 0 ? (
                'No Active Phone Numbers'
              ) : (
                'Create Customer'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
