// @ts-nocheck
import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Label and Alert components - using simple alternatives
import { Loader2, Store, ExternalLink, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { executeMutation } from '@/lib/graphql-client';
import { INITIATE_SHOPIFY_OAUTH } from '@/lib/graphql/mutations/shopify';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface ShopifyConnectionFormProps {
  onSuccess?: (storeData: any) => void;
  className?: string;
}

interface ShopifyOAuthResponse {
  authorizationUrl: string;
  state: string;
  shop: string;
}

export const ShopifyConnectionForm: React.FC<ShopifyConnectionFormProps> = ({
  onSuccess,
  className
}) => {
  const [shopName, setShopName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();
  const { toast } = useToast();
  
  const initiateOAuth = async (input: { shop: string; user_id?: string; organizationId?: string }) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await executeMutation<{ initiateShopifyOAuth: ShopifyOAuthResponse }>({
        query: INITIATE_SHOPIFY_OAUTH,
        variables: { input },
      });
      
      if (!response.data?.initiateShopifyOAuth) {
        throw new Error('Failed to get OAuth URL from server');
      }
      
      const { authorizationUrl } = response.data.initiateShopifyOAuth;
      
      // Store connection attempt in localStorage for post-OAuth tracking
      localStorage.setItem('shopify_connection_attempt', JSON.stringify({
        shop: shopName,
        timestamp: Date.now(),
        userId: user?.userId || 'authenticated'
      }));
      
      // Redirect to Shopify OAuth
      window.location.href = authorizationUrl;
      
    } catch (error: any) {
      console.error('OAuth initiation failed:', error);
      setError(error.message || 'Failed to initiate Shopify connection');
      setIsLoading(false);
      
      toast({
        title: 'Connection Failed',
        description: 'Unable to connect to Shopify. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const validateShopName = (shop: string): boolean => {
    const cleanShop = shop.trim().toLowerCase();
    
    // Check if it's a valid shop name format
    const shopRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$/;
    
    if (!cleanShop) {
      setError('Please enter your shop name');
      return false;
    }
    
    if (cleanShop.length < 3) {
      setError('Shop name must be at least 3 characters');
      return false;
    }
    
    if (cleanShop.length > 60) {
      setError('Shop name is too long');
      return false;
    }
    
    // Remove .myshopify.com if user included it
    const shopNameOnly = cleanShop.replace(/\.myshopify\.com$/, '');
    
    if (!shopRegex.test(shopNameOnly)) {
      setError('Invalid shop name format. Use only letters, numbers, and hyphens.');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!validateShopName(shopName)) {
      return;
    }
    
    if (!user) {
      setError('You must be logged in to connect a Shopify store');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const cleanShopName = shopName.trim().toLowerCase().replace(/\.myshopify\.com$/, '');
      
      const input: any = {
        shop: cleanShopName,
        user_id: user?.userId,  // ✅ Backend Lambda expects user_id
      };
      
      // Only include organizationId if it exists
      if (user.organizationId) {
        input.organizationId = user.organizationId;
      }
      
      await initiateOAuth(input);
    } catch (err) {
      console.error('Connection error:', err);
      setIsLoading(false);
    }
  };

  const handleShopNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setShopName(value);
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
          <Store className="h-6 w-6 text-green-600" />
        </div>
        <CardTitle className="text-xl font-semibold">Connect Your Shopify Store</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Link your Shopify store to automatically send order notifications via WhatsApp
        </CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <div className="flex items-center space-x-2 p-3 text-sm text-red-800 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
          
          <div className="space-y-2">
            <label htmlFor="shopName" className="block text-sm font-medium text-gray-700">
              Shop Name
            </label>
            <div className="relative">
              <Input
                id="shopName"
                type="text"
                placeholder="your-store-name"
                value={shopName}
                onChange={handleShopNameChange}
                disabled={isLoading}
                className="pr-32"
                autoComplete="off"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-sm text-muted-foreground">.myshopify.com</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Enter just your shop name without ".myshopify.com"
            </p>
          </div>
          
          <div className="rounded-lg border bg-muted/50 p-4">
            <h4 className="mb-2 text-sm font-medium">What you'll get:</h4>
            <ul className="space-y-1 text-xs text-muted-foreground">
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-green-500" />
                Automatic WhatsApp notifications for new orders
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-green-500" />
                Real-time order status updates
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-green-500" />
                Customer support integration
              </li>
              <li className="flex items-center gap-2">
                <div className="h-1.5 w-1.5 rounded-full bg-green-500" />
                Analytics and insights
              </li>
            </ul>
          </div>
          
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/50">
            <div className="flex items-start gap-3">
              <ExternalLink className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Secure OAuth Connection
                </h4>
                <p className="mt-1 text-xs text-blue-700 dark:text-blue-300">
                  You'll be redirected to Shopify to authorize this connection. 
                  We never store your Shopify password.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        
        <CardFooter>
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isLoading || !shopName.trim()}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting to Shopify...
              </>
            ) : (
              <>
                <Store className="mr-2 h-4 w-4" />
                Connect Store
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

// Additional component for success/error states after OAuth callback
export const ShopifyConnectionStatus: React.FC<{
  status: 'success' | 'error';
  storeName?: string;
  storeId?: string;
  error?: string;
  onRetry?: () => void;
  onContinue?: () => void;
}> = ({ status, storeName, storeId, error, onRetry, onContinue }) => {
  const { toast } = useToast();
  
  React.useEffect(() => {
    if (status === 'success' && storeName) {
      toast({
        title: 'Store Connected Successfully!',
        description: `${storeName} is now connected to Notefy.`,
        variant: 'default'
      });
    }
  }, [status, storeName, toast]);
  
  if (status === 'success') {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
            <Store className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-green-900">
            Store Connected!
          </CardTitle>
          <CardDescription>
            {storeName} is now connected to Notefy
          </CardDescription>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            {storeName}
          </Badge>
          
          <div className="text-sm text-muted-foreground">
            <p>Your store is now set up to send WhatsApp notifications for:</p>
            <ul className="mt-2 space-y-1">
              <li>• New orders</li>
              <li>• Order updates</li>
              <li>• Payment confirmations</li>
              <li>• Fulfillment updates</li>
            </ul>
          </div>
        </CardContent>
        
        <CardFooter>
          <Button onClick={onContinue} className="w-full">
            Continue to Dashboard
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  if (status === 'error') {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-red-900">
            Connection Failed
          </CardTitle>
          <CardDescription>
            We couldn't connect your Shopify store
          </CardDescription>
        </CardHeader>
        
        <CardContent className="text-center">
          <div className="flex items-center space-x-2 p-3 text-sm text-red-800 bg-red-50 border border-red-200 rounded-md">
            <AlertCircle className="h-4 w-4" />
            <span>{error || 'An unknown error occurred during the connection process.'}</span>
          </div>
        </CardContent>
        
        <CardFooter className="flex gap-2">
          <Button variant="outline" onClick={onRetry} className="flex-1">
            Try Again
          </Button>
          <Button onClick={onContinue} className="flex-1">
            Continue
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  return null;
}; 