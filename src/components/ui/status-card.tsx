// @ts-nocheck
// StatusCard Component
// Enterprise-grade status display with real-time indicators

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { LucideIcon, Wifi, WifiOff, Clock, CheckCircle, AlertCircle, XCircle, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const statusCardVariants = cva(
  "relative overflow-hidden transition-all duration-300 group",
  {
    variants: {
      variant: {
        default: "bg-card border-border hover:border-primary/20",
        primary: "bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200",
        success: "bg-gradient-to-br from-green-50 to-emerald-50 border-green-200",
        warning: "bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200",
        danger: "bg-gradient-to-br from-red-50 to-rose-50 border-red-200",
        glass: "bg-white/10 backdrop-blur-md border-white/20",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

const statusIndicatorVariants = cva(
  "inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium transition-all duration-300",
  {
    variants: {
      status: {
        online: "bg-green-100 text-green-700 border border-green-200",
        offline: "bg-red-100 text-red-700 border border-red-200",
        pending: "bg-amber-100 text-amber-700 border border-amber-200",
        connecting: "bg-blue-100 text-blue-700 border border-blue-200",
        error: "bg-red-100 text-red-700 border border-red-200",
      },
      animated: {
        true: "animate-pulse",
        false: "",
      },
    },
    defaultVariants: {
      status: "offline",
      animated: false,
    },
  }
);

// ============================================================================
// Component Types
// ============================================================================

export interface StatusItem {
  id: string;
  title: string;
  subtitle?: string;
  status: 'online' | 'offline' | 'pending' | 'connecting' | 'error';
  lastUpdated?: string;
  metadata?: Record<string, any>;
}

export interface StatusCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusCardVariants> {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  items: StatusItem[];
  loading?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
  emptyMessage?: string;
  maxItems?: number;
  showTimestamp?: boolean;
}

// ============================================================================
// Helper Components
// ============================================================================

const StatusIndicator: React.FC<{
  status: StatusItem['status'];
  animated?: boolean;
  showText?: boolean;
}> = ({ status, animated = false, showText = true }) => {
  const icons = {
    online: CheckCircle,
    offline: XCircle,
    pending: Clock,
    connecting: Wifi,
    error: AlertCircle,
  };

  const Icon = icons[status];

  return (
    <div className={cn(statusIndicatorVariants({ status, animated }))}>
      <Icon className="h-3 w-3" />
      {showText && (
        <span className="capitalize">
          {status === 'connecting' ? 'Connecting...' : status}
        </span>
      )}
    </div>
  );
};

const StatusListItem: React.FC<{
  item: StatusItem;
  showTimestamp?: boolean;
}> = ({ item, showTimestamp = true }) => {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="flex items-center justify-between py-3 border-b border-border/50 last:border-b-0 group/item hover:bg-muted/30 -mx-2 px-2 rounded-lg transition-colors duration-200">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-3">
          <StatusIndicator 
            status={item.status} 
            animated={item.status === 'connecting'} 
            showText={false}
          />
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-foreground truncate group-hover/item:text-primary transition-colors duration-200">
              {item.title}
            </h4>
            {item.subtitle && (
              <p className="text-xs text-muted-foreground truncate mt-0.5">
                {item.subtitle}
              </p>
            )}
          </div>
        </div>
      </div>
      
      {showTimestamp && item.lastUpdated && (
        <div className="text-xs text-muted-foreground ml-2">
          {formatTimestamp(item.lastUpdated)}
        </div>
      )}
    </div>
  );
};

const LoadingSkeleton: React.FC = () => (
  <div className="space-y-3">
    {Array.from({ length: 3 }).map((_, i) => (
      <div key={i} className="flex items-center justify-between py-3 animate-pulse">
        <div className="flex items-center gap-3">
          <div className="h-6 w-16 bg-muted rounded-full"></div>
          <div className="space-y-1">
            <div className="h-4 bg-muted rounded w-32"></div>
            <div className="h-3 bg-muted rounded w-24"></div>
          </div>
        </div>
        <div className="h-3 bg-muted rounded w-12"></div>
      </div>
    ))}
  </div>
);

// ============================================================================
// Main Component
// ============================================================================

export const StatusCard: React.FC<StatusCardProps> = ({
  className,
  variant,
  size,
  title,
  subtitle,
  icon: Icon,
  items,
  loading = false,
  onRefresh,
  refreshing = false,
  emptyMessage = "No items to display",
  maxItems,
  showTimestamp = true,
  ...props
}) => {
  const displayItems = maxItems ? items.slice(0, maxItems) : items;
  const hasMoreItems = maxItems && items.length > maxItems;

  // Calculate status summary
  const statusSummary = React.useMemo(() => {
    const counts = items.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: items.length,
      online: counts.online || 0,
      offline: counts.offline || 0,
      pending: counts.pending || 0,
      connecting: counts.connecting || 0,
      error: counts.error || 0,
    };
  }, [items]);

  return (
    <Card
      className={cn(
        statusCardVariants({ variant, size }),
        "hover:shadow-lg transition-shadow duration-300",
        className
      )}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {Icon && (
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              <Icon className="h-5 w-5" />
            </div>
          )}
          <div>
            <h3 className="font-semibold text-foreground">{title}</h3>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Status Summary */}
          {statusSummary.total > 0 && (
            <div className="flex items-center gap-1">
              {statusSummary.online > 0 && (
                <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                  {statusSummary.online} online
                </Badge>
              )}
              {statusSummary.offline > 0 && (
                <Badge variant="secondary" className="bg-red-100 text-red-700 text-xs">
                  {statusSummary.offline} offline
                </Badge>
              )}
            </div>
          )}
          
          {/* Refresh Button */}
          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={refreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn(
                "h-4 w-4",
                refreshing && "animate-spin"
              )} />
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="space-y-1">
        {loading ? (
          <LoadingSkeleton />
        ) : displayItems.length > 0 ? (
          <>
            {displayItems.map((item) => (
              <StatusListItem
                key={item.id}
                item={item}
                showTimestamp={showTimestamp}
              />
            ))}
            
            {hasMoreItems && (
              <div className="pt-3 text-center">
                <Button variant="ghost" size="sm" className="text-xs">
                  View {items.length - maxItems!} more
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <div className="text-muted-foreground text-sm">{emptyMessage}</div>
          </div>
        )}
      </div>

      {/* Pulse Animation for Active States */}
      {statusSummary.connecting > 0 && (
        <div className="absolute top-2 right-2 h-2 w-2 bg-blue-500 rounded-full animate-ping" />
      )}
    </Card>
  );
};

// ============================================================================
// Connection Status Card (Specialized)
// ============================================================================

export interface ConnectionStatusCardProps extends Omit<StatusCardProps, 'items'> {
  whatsappConnections?: Array<{
    id: string;
    phoneNumber: string;
    status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'ERROR';
    lastSeen?: string;
    businessName?: string;
  }>;
  shopifyStores?: Array<{
    id: string;
    name: string;
    domain: string;
    status: 'CONNECTED' | 'DISCONNECTED' | 'PENDING' | 'ERROR';
    lastSync?: string;
  }>;
}

export const ConnectionStatusCard: React.FC<ConnectionStatusCardProps> = ({
  whatsappConnections = [],
  shopifyStores = [],
  ...props
}) => {
  const statusMap = {
    ACTIVE: 'online' as const,
    CONNECTED: 'online' as const,
    INACTIVE: 'offline' as const,
    DISCONNECTED: 'offline' as const,
    PENDING: 'pending' as const,
    ERROR: 'error' as const,
  };

  const items: StatusItem[] = [
    ...whatsappConnections.map(conn => ({
      id: `whatsapp-${conn.id}`,
      title: conn.businessName || conn.phoneNumber,
      subtitle: `WhatsApp Business • ${conn.phoneNumber}`,
      status: statusMap[conn.status],
      lastUpdated: conn.lastSeen,
    })),
    ...shopifyStores.map(store => ({
      id: `shopify-${store.id}`,
      title: store.name,
      subtitle: `Shopify Store • ${store.domain}`,
      status: statusMap[store.status],
      lastUpdated: store.lastSync,
    })),
  ];

  return (
    <StatusCard
      title="Connection Status"
      subtitle="WhatsApp & Shopify integrations"
      icon={Wifi}
      items={items}
      emptyMessage="No connections configured"
      {...props}
    />
  );
};

export default StatusCard; 