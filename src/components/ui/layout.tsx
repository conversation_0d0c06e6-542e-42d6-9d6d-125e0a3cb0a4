import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

// Container Component
const containerVariants = cva("mx-auto px-4 sm:px-6 lg:px-8", {
  variants: {
    size: {
      sm: "max-w-screen-sm",
      md: "max-w-screen-md",
      lg: "max-w-screen-lg",
      xl: "max-w-screen-xl",
      "2xl": "max-w-screen-2xl",
      full: "max-w-full",
      none: "max-w-none",
    },
  },
  defaultVariants: {
    size: "xl",
  },
})

export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(containerVariants({ size }), className)}
        {...props}
      />
    )
  }
)
Container.displayName = "Container"

// Grid Component
const gridVariants = cva("grid", {
  variants: {
    cols: {
      1: "grid-cols-1",
      2: "grid-cols-1 md:grid-cols-2",
      3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
      5: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",
      6: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",
      12: "grid-cols-12",
    },
    gap: {
      none: "gap-0",
      sm: "gap-2",
      default: "gap-4",
      md: "gap-6",
      lg: "gap-8",
      xl: "gap-12",
    },
  },
  defaultVariants: {
    cols: 1,
    gap: "default",
  },
})

export interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols, gap, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(gridVariants({ cols, gap }), className)}
        {...props}
      />
    )
  }
)
Grid.displayName = "Grid"

// Stack Component (Vertical)
const stackVariants = cva("flex flex-col", {
  variants: {
    spacing: {
      none: "space-y-0",
      xs: "space-y-1",
      sm: "space-y-2",
      default: "space-y-4",
      md: "space-y-6",
      lg: "space-y-8",
      xl: "space-y-12",
    },
    align: {
      start: "items-start",
      center: "items-center",
      end: "items-end",
      stretch: "items-stretch",
    },
  },
  defaultVariants: {
    spacing: "default",
    align: "stretch",
  },
})

export interface StackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stackVariants> {}

const Stack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, spacing, align, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(stackVariants({ spacing, align }), className)}
        {...props}
      />
    )
  }
)
Stack.displayName = "Stack"

// Flex Component (Horizontal)
const flexVariants = cva("flex", {
  variants: {
    direction: {
      row: "flex-row",
      column: "flex-col",
      "row-reverse": "flex-row-reverse",
      "column-reverse": "flex-col-reverse",
    },
    align: {
      start: "items-start",
      center: "items-center",
      end: "items-end",
      stretch: "items-stretch",
      baseline: "items-baseline",
    },
    justify: {
      start: "justify-start",
      center: "justify-center",
      end: "justify-end",
      between: "justify-between",
      around: "justify-around",
      evenly: "justify-evenly",
    },
    gap: {
      none: "gap-0",
      xs: "gap-1",
      sm: "gap-2",
      default: "gap-4",
      md: "gap-6",
      lg: "gap-8",
      xl: "gap-12",
    },
    wrap: {
      nowrap: "flex-nowrap",
      wrap: "flex-wrap",
      "wrap-reverse": "flex-wrap-reverse",
    },
  },
  defaultVariants: {
    direction: "row",
    align: "center",
    justify: "start",
    gap: "default",
    wrap: "nowrap",
  },
})

export interface FlexProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof flexVariants> {}

const Flex = React.forwardRef<HTMLDivElement, FlexProps>(
  ({ className, direction, align, justify, gap, wrap, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          flexVariants({ direction, align, justify, gap, wrap }),
          className
        )}
        {...props}
      />
    )
  }
)
Flex.displayName = "Flex"

// Section Component
const sectionVariants = cva("", {
  variants: {
    spacing: {
      none: "py-0",
      sm: "py-8",
      default: "py-12",
      md: "py-16",
      lg: "py-20",
      xl: "py-24",
    },
    background: {
      default: "bg-background",
      muted: "bg-muted/30",
      card: "bg-card",
      gradient: "bg-gradient-primary",
      transparent: "bg-transparent",
    },
  },
  defaultVariants: {
    spacing: "default",
    background: "default",
  },
})

export interface SectionProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof sectionVariants> {}

const Section = React.forwardRef<HTMLElement, SectionProps>(
  ({ className, spacing, background, ...props }, ref) => {
    return (
      <section
        ref={ref}
        className={cn(sectionVariants({ spacing, background }), className)}
        {...props}
      />
    )
  }
)
Section.displayName = "Section"

// Divider Component
export interface DividerProps extends React.HTMLAttributes<HTMLHRElement> {
  orientation?: "horizontal" | "vertical"
  variant?: "default" | "dashed" | "dotted"
}

const Divider = React.forwardRef<HTMLHRElement, DividerProps>(
  ({ className, orientation = "horizontal", variant = "default", ...props }, ref) => {
    return (
      <hr
        ref={ref}
        className={cn(
          "border-border",
          orientation === "horizontal" 
            ? "w-full h-px" 
            : "h-full w-px",
          variant === "dashed" && "border-dashed",
          variant === "dotted" && "border-dotted",
          className
        )}
        {...props}
      />
    )
  }
)
Divider.displayName = "Divider"

// Spacer Component
export interface SpacerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "xs" | "sm" | "default" | "md" | "lg" | "xl" | "2xl"
}

const Spacer = React.forwardRef<HTMLDivElement, SpacerProps>(
  ({ className, size = "default", ...props }, ref) => {
    const sizeClasses = {
      xs: "h-1",
      sm: "h-2",
      default: "h-4",
      md: "h-6",
      lg: "h-8",
      xl: "h-12",
      "2xl": "h-16",
    }

    return (
      <div
        ref={ref}
        className={cn(sizeClasses[size], className)}
        {...props}
      />
    )
  }
)
Spacer.displayName = "Spacer"

export {
  Container,
  Grid,
  Stack,
  Flex,
  Section,
  Divider,
  Spacer,
  containerVariants,
  gridVariants,
  stackVariants,
  flexVariants,
  sectionVariants,
} 