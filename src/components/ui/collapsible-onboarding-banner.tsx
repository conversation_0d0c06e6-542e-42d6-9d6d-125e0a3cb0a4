// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, CheckCircle, Circle, X, Settings, ShoppingBag, MessageSquare, Wrench, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { OnboardingStep } from '@/components/ui/onboarding-progress-badge';
import { hapticFeedback } from '@/components/ui/mobile-interactions';
import { 
  ResponsiveGrid, 
  ResponsiveFlex, 
  ResponsiveStack, 
  ResponsiveShow, 
  useResponsiveBreakpoint 
} from '@/components/ui/responsive-grid';

// ============================================================================
// Types
// ============================================================================

export interface CollapsibleOnboardingBannerProps {
  steps: OnboardingStep[];
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  onContinue?: () => void;
  onSkip?: () => void;
  onDismiss?: () => void;
  onComplete?: () => void;
  isVisible?: boolean;
  className?: string;
  /** Mobile-optimized variant with better touch targets */
  mobileOptimized?: boolean;
  /** Enable haptic feedback on mobile devices */
  hapticFeedback?: boolean;
  /** Show expanded by default */
  defaultExpanded?: boolean;
}

// ============================================================================
// Step Icon Helper
// ============================================================================

const getStepIcon = (stepId: string) => {
  switch (stepId) {
    case 'welcome':
      return CheckCircle;
    case 'shopify':
      return ShoppingBag;
    case 'whatsapp':
      return MessageSquare;
    case 'configuration':
      return Wrench;
    default:
      return Circle;
  }
};

// ============================================================================
// Compact Banner Content
// ============================================================================

const CompactBannerContent: React.FC<{
  steps: OnboardingStep[];
  completedSteps: number;
  totalSteps: number;
  progressPercentage: number;
  isComplete: boolean;
  onExpand: () => void;
  onDismiss?: () => void;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}> = ({ 
  steps, 
  completedSteps, 
  totalSteps, 
  progressPercentage, 
  isComplete,
  onExpand,
  onDismiss,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false 
}) => {
  const handleExpand = () => {
    if (enableHaptic) hapticFeedback.light();
    onExpand();
  };

  const handleDismiss = () => {
    if (enableHaptic) hapticFeedback.medium();
    onDismiss?.();
  };

  return (
    <div className="flex items-center justify-between gap-4 p-4">
      {/* Left: Progress Info */}
      <div className="flex items-center gap-3 flex-1">
        <div className="flex items-center gap-2">
          {isComplete ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <Settings className="h-5 w-5 text-blue-600" />
          )}
          
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className={cn(
                "font-medium",
                mobileOptimized ? "text-sm" : "text-sm"
              )}>
                {isComplete ? 'Setup Complete!' : 'Setup Progress'}
              </span>
              <Badge variant={isComplete ? 'default' : 'secondary'} className="text-xs">
                {completedSteps}/{totalSteps}
              </Badge>
            </div>
            
            <ResponsiveShow above="sm">
              <span className="text-xs text-muted-foreground">
                {isComplete ? 'You\'re all set!' : `${Math.round(progressPercentage)}% complete`}
              </span>
            </ResponsiveShow>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="flex-1 max-w-32 sm:max-w-48">
          <Progress 
            value={progressPercentage} 
            className="h-2"
          />
        </div>
      </div>

      {/* Right: Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleExpand}
          className={cn(
            "gap-2",
            mobileOptimized && "min-h-[36px] min-w-[36px]"
          )}
        >
          <ChevronDown className="h-4 w-4" />
          <ResponsiveShow above="sm">
            <span>Expand</span>
          </ResponsiveShow>
        </Button>
        
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className={cn(
              "p-2 h-auto w-auto",
              mobileOptimized && "min-h-[36px] min-w-[36px]"
            )}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Expanded Banner Content
// ============================================================================

const ExpandedBannerContent: React.FC<{
  steps: OnboardingStep[];
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  onContinue?: () => void;
  onSkip?: () => void;
  onCollapse: () => void;
  onComplete?: () => void;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}> = ({ 
  steps, 
  currentStep,
  onStepClick,
  onContinue,
  onSkip,
  onCollapse,
  onComplete,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false 
}) => {
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;
  const nextIncompleteStep = steps.find(step => !step.completed);

  const handleStepClick = (stepId: string) => {
    if (enableHaptic) hapticFeedback.light();
    onStepClick?.(stepId);
  };

  const handleContinue = () => {
    if (enableHaptic) hapticFeedback.medium();
    if (isComplete) {
      onComplete?.();
    } else {
      onContinue?.();
    }
  };

  const handleSkip = () => {
    if (enableHaptic) hapticFeedback.light();
    onSkip?.();
  };

  const handleCollapse = () => {
    if (enableHaptic) hapticFeedback.light();
    onCollapse();
  };

  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isComplete ? (
              <CheckCircle className="h-6 w-6 text-green-600" />
            ) : (
              <Settings className="h-6 w-6 text-blue-600" />
            )}
            
            <div>
              <h3 className={cn(
                "font-semibold",
                mobileOptimized ? "text-base" : "text-lg"
              )}>
                {isComplete ? 'Setup Complete!' : 'Complete Your Setup'}
              </h3>
              <p className="text-sm text-muted-foreground">
                {isComplete 
                  ? 'Your Notefy account is fully configured and ready to use.' 
                  : `${completedSteps} of ${totalSteps} steps completed`
                }
              </p>
            </div>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCollapse}
          className={cn(
            "gap-2",
            mobileOptimized && "min-h-[36px] min-w-[36px]"
          )}
        >
          <ChevronUp className="h-4 w-4" />
          <ResponsiveShow above="sm">
            <span>Collapse</span>
          </ResponsiveShow>
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progressPercentage} className="h-3" />
        <p className="text-xs text-muted-foreground text-center">
          {Math.round(progressPercentage)}% complete
        </p>
      </div>

      {/* Steps Grid */}
      <ResponsiveGrid 
        cols={mobileOptimized ? 1 : 2} 
        gap="sm"
        className="max-w-4xl"
      >
        {steps.map((step, index) => {
          const Icon = getStepIcon(step.id);
          const isActive = step.id === currentStep;
          const isClickable = step.completed || isActive;
          
          return (
            <Card 
              key={step.id}
              className={cn(
                "transition-all duration-200 cursor-pointer",
                step.completed && "border-green-200 bg-green-50/50",
                isActive && "border-blue-200 bg-blue-50/50 shadow-sm",
                !step.completed && !isActive && "border-gray-200 bg-gray-50/50",
                isClickable && "hover:shadow-md"
              )}
              onClick={() => isClickable && handleStepClick(step.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={cn(
                    "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center",
                    step.completed && "bg-green-100 text-green-600",
                    isActive && "bg-blue-100 text-blue-600",
                    !step.completed && !isActive && "bg-gray-100 text-gray-400"
                  )}>
                    {step.completed ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className={cn(
                        "font-medium",
                        mobileOptimized ? "text-sm" : "text-sm"
                      )}>
                        {step.name}
                      </h4>
                      
                      {step.optional && (
                        <Badge variant="outline" className="text-xs">
                          Optional
                        </Badge>
                      )}
                      
                      {step.completed && (
                        <Badge variant="default" className="text-xs">
                          Done
                        </Badge>
                      )}
                      
                      {isActive && !step.completed && (
                        <Badge variant="secondary" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-xs text-muted-foreground mb-2">
                      {step.description}
                    </p>
                    
                    {isActive && !step.completed && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleContinue();
                        }}
                        className="gap-2 text-xs"
                      >
                        Continue
                        <ArrowRight className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </ResponsiveGrid>

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center gap-2">
          {isComplete ? (
            <Button
              variant="default"
              onClick={handleContinue}
              className="gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Get Started
            </Button>
          ) : (
            <Button
              variant="default"
              onClick={handleContinue}
              className="gap-2"
            >
              {nextIncompleteStep ? `Continue to ${nextIncompleteStep.name}` : 'Continue'}
              <ArrowRight className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {onSkip && !isComplete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              className="text-muted-foreground"
            >
              Skip Setup
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCollapse}
            className="text-muted-foreground"
          >
            Collapse
          </Button>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

export const CollapsibleOnboardingBanner: React.FC<CollapsibleOnboardingBannerProps> = ({
  steps,
  currentStep,
  onStepClick,
  onContinue,
  onSkip,
  onDismiss,
  onComplete,
  isVisible = true,
  className,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
  defaultExpanded = false,
}) => {
  const { isMobile } = useResponsiveBreakpoint();
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Auto-detect mobile optimization
  const optimizedForMobile = mobileOptimized || isMobile;

  // Calculate progress
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;

  // Handle expand/collapse
  const handleExpand = () => {
    setIsExpanded(true);
  };

  const handleCollapse = () => {
    setIsExpanded(false);
  };

  // Handle dismiss (hide completely)
  const handleDismiss = () => {
    setIsCollapsed(true);
    onDismiss?.();
  };

  // Don't render if not visible or collapsed
  if (!isVisible || isCollapsed) return null;

  return (
    <div
      className={cn(
        "w-full bg-background/95 backdrop-blur-sm border-b border-border/50 shadow-sm transition-all duration-300",
        optimizedForMobile && "touch-manipulation",
        className
      )}
    >
      <div className="container mx-auto max-w-7xl">
        {isExpanded ? (
          <ExpandedBannerContent
            steps={steps}
            currentStep={currentStep}
            onStepClick={onStepClick}
            onContinue={onContinue}
            onSkip={onSkip}
            onCollapse={handleCollapse}
            onComplete={onComplete}
            mobileOptimized={optimizedForMobile}
            hapticFeedback={enableHaptic}
          />
        ) : (
          <CompactBannerContent
            steps={steps}
            completedSteps={completedSteps}
            totalSteps={totalSteps}
            progressPercentage={progressPercentage}
            isComplete={isComplete}
            onExpand={handleExpand}
            onDismiss={handleDismiss}
            mobileOptimized={optimizedForMobile}
            hapticFeedback={enableHaptic}
          />
        )}
      </div>
    </div>
  );
};

// ============================================================================
// Hook for Banner State Management
// ============================================================================

export const useCollapsibleOnboardingBanner = (steps: OnboardingStep[]) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;
  const hasIncompleteSteps = steps.some(step => !step.completed);

  // Auto-hide when complete (optional)
  const hideWhenComplete = () => {
    if (isComplete) {
      setIsVisible(false);
    }
  };

  // Show banner if there are incomplete steps
  const showBanner = () => {
    setIsVisible(true);
  };

  // Hide banner
  const hideBanner = () => {
    setIsVisible(false);
  };

  return {
    isExpanded,
    setIsExpanded,
    isVisible,
    setIsVisible,
    completedSteps,
    totalSteps,
    progressPercentage,
    isComplete,
    hasIncompleteSteps,
    hideWhenComplete,
    showBanner,
    hideBanner,
  };
};

export default CollapsibleOnboardingBanner; 