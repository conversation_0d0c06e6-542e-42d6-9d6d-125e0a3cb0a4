import React, { useState, useEffect, useCallback } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
}

interface NotificationContainerProps {
  notifications: Notification[];
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const getPositionClasses = (position: string = 'top-right') => {
  switch (position) {
    case 'top-left':
      return 'top-0 left-0';
    case 'bottom-right':
      return 'bottom-0 right-0';
    case 'bottom-left':
      return 'bottom-0 left-0';
    default:
      return 'top-0 right-0';
  }
};

export const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onDismiss,
  position = 'top-right',
}) => {
  const [visibleNotifications, setVisibleNotifications] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    // Initialize visibility for new notifications
    const newVisibility: { [key: string]: boolean } = {};
    let hasNewNotifications = false;

    notifications.forEach((notification) => {
      if (visibleNotifications[notification.id] === undefined) {
        newVisibility[notification.id] = true;
        hasNewNotifications = true;
      }
    });

    if (hasNewNotifications) {
      setVisibleNotifications((prev) => ({
        ...prev,
        ...newVisibility,
      }));
    }
  }, [notifications.map(n => n.id).join(',')]);

  const handleDismiss = (id: string) => {
    setVisibleNotifications((prev) => ({
      ...prev,
      [id]: false,
    }));
    
    // Delay actual removal to allow animation to complete
    setTimeout(() => {
      onDismiss(id);
    }, 300);
  };

  const getIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return (
          <div className="h-6 w-6 text-emerald-400 bg-emerald-100 rounded-full flex items-center justify-center">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="h-6 w-6 text-red-400 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="h-6 w-6 text-amber-400 bg-amber-100 rounded-full flex items-center justify-center">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="h-6 w-6 text-blue-400 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  return (
    <div
      className={cn(
        'fixed z-[9999] pointer-events-none p-6 space-y-4 max-w-md w-full',
        getPositionClasses(position)
      )}
    >
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={cn(
            'pointer-events-auto w-full overflow-hidden rounded-lg border shadow-lg transition-all duration-300 ease-in-out',
            notification.type === 'error' && 'bg-white border-red-200',
            notification.type === 'success' && 'bg-white border-emerald-200',
            notification.type === 'warning' && 'bg-white border-amber-200',
            notification.type === 'info' && 'bg-white border-blue-200',
            visibleNotifications[notification.id] ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
          )}
        >
          <div className="p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {getIcon(notification.type)}
              </div>
              <div className="ml-3 w-0 flex-1">
                <p className={cn(
                  'text-sm font-medium',
                  notification.type === 'error' && 'text-red-900',
                  notification.type === 'success' && 'text-emerald-900',
                  notification.type === 'warning' && 'text-amber-900',
                  notification.type === 'info' && 'text-blue-900'
                )}>
                  {notification.title}
                </p>
                {notification.message && (
                  <p className={cn(
                    'mt-1 text-sm',
                    notification.type === 'error' && 'text-red-700',
                    notification.type === 'success' && 'text-emerald-700',
                    notification.type === 'warning' && 'text-amber-700',
                    notification.type === 'info' && 'text-blue-700'
                  )}>
                    {notification.message}
                  </p>
                )}
              </div>
              <div className="ml-4 flex flex-shrink-0">
                <button
                  onClick={() => handleDismiss(notification.id)}
                  className={cn(
                    'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                    notification.type === 'error' && 'text-red-400 hover:text-red-500 focus:ring-red-500',
                    notification.type === 'success' && 'text-emerald-400 hover:text-emerald-500 focus:ring-emerald-500',
                    notification.type === 'warning' && 'text-amber-400 hover:text-amber-500 focus:ring-amber-500',
                    notification.type === 'info' && 'text-blue-400 hover:text-blue-500 focus:ring-blue-500'
                  )}
                >
                  <span className="sr-only">Close</span>
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Hook for managing notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setNotifications(prev => [...prev, { ...notification, id }]);
    
    // Auto-remove after 5 seconds for non-error notifications
    if (notification.type !== 'error') {
      setTimeout(() => {
        removeNotification(id);
      }, 5000);
    }
  }, [removeNotification]);

  const success = useCallback((title: string, message?: string) => {
    addNotification({ type: 'success', title, message });
  }, [addNotification]);

  const error = useCallback((title: string, message?: string) => {
    addNotification({ type: 'error', title, message });
  }, [addNotification]);

  const warning = useCallback((title: string, message?: string) => {
    addNotification({ type: 'warning', title, message });
  }, [addNotification]);

  const info = useCallback((title: string, message?: string) => {
    addNotification({ type: 'info', title, message });
  }, [addNotification]);

  return {
    notifications,
    addNotification,
    removeNotification,
    success,
    error,
    warning,
    info,
  };
}; 