import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const DropdownMenu = DropdownMenuPrimitive.Root

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuGroup = DropdownMenuPrimitive.Group

const DropdownMenuPortal = DropdownMenuPrimitive.Portal

const DropdownMenuSub = DropdownMenuPrimitive.Sub

const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean
    /** Mobile-optimized variant with larger touch targets */
    mobileOptimized?: boolean
  }
>(({ className, inset, children, mobileOptimized = false, ...props }, ref) => (
  <DropdownMenuPrimitive.SubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent touch-manipulation",
      mobileOptimized && "px-4 py-3 text-base min-h-[44px] active:bg-accent/80",
      inset && (mobileOptimized ? "pl-12" : "pl-8"),
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className={cn("ml-auto h-4 w-4", mobileOptimized && "h-5 w-5")} />
  </DropdownMenuPrimitive.SubTrigger>
))
DropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent> & {
    /** Mobile-optimized variant with better spacing */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      mobileOptimized && "min-w-[12rem] p-2 rounded-lg shadow-xl",
      className
    )}
    {...props}
  />
))
DropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content> & {
    /** Mobile-optimized variant with better spacing and positioning */
    mobileOptimized?: boolean
  }
>(({ className, sideOffset = 4, mobileOptimized = false, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={mobileOptimized ? 8 : sideOffset}
      className={cn(
        "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md",
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        mobileOptimized && "min-w-[12rem] p-2 rounded-lg shadow-xl max-h-[70vh] overflow-y-auto",
        className
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean
    /** Mobile-optimized variant with larger touch targets */
    mobileOptimized?: boolean
    /** Add haptic feedback on mobile devices */
    hapticFeedback?: boolean
  }
>(({ className, inset, mobileOptimized = false, hapticFeedback = false, onClick, ...props }, ref) => {
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(1)
    }
    onClick?.(e)
  }

  return (
    <DropdownMenuPrimitive.Item
      ref={ref}
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 touch-manipulation",
        mobileOptimized && "px-4 py-3 text-base min-h-[44px] rounded-md active:bg-accent/80",
        inset && (mobileOptimized ? "pl-12" : "pl-8"),
        className
      )}
      onClick={onClick ? handleClick : undefined}
      {...props}
    />
  )
})
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem> & {
    /** Mobile-optimized variant with larger touch targets */
    mobileOptimized?: boolean
    /** Add haptic feedback on mobile devices */
    hapticFeedback?: boolean
  }
>(({ className, children, checked, mobileOptimized = false, hapticFeedback = false, onClick, ...props }, ref) => {
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(1)
    }
    onClick?.(e)
  }

  return (
    <DropdownMenuPrimitive.CheckboxItem
      ref={ref}
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 touch-manipulation",
        mobileOptimized && "py-3 pl-12 pr-4 text-base min-h-[44px] rounded-md active:bg-accent/80",
        className
      )}
      checked={checked}
      onClick={onClick ? handleClick : undefined}
      {...props}
    >
      <span className={cn(
        "absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
        mobileOptimized && "left-3 h-5 w-5"
      )}>
        <DropdownMenuPrimitive.ItemIndicator>
          <Check className={cn("h-4 w-4", mobileOptimized && "h-5 w-5")} />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.CheckboxItem>
  )
})
DropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem> & {
    /** Mobile-optimized variant with larger touch targets */
    mobileOptimized?: boolean
    /** Add haptic feedback on mobile devices */
    hapticFeedback?: boolean
  }
>(({ className, children, mobileOptimized = false, hapticFeedback = false, onClick, ...props }, ref) => {
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(1)
    }
    onClick?.(e)
  }

  return (
    <DropdownMenuPrimitive.RadioItem
      ref={ref}
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 touch-manipulation",
        mobileOptimized && "py-3 pl-12 pr-4 text-base min-h-[44px] rounded-md active:bg-accent/80",
        className
      )}
      onClick={onClick ? handleClick : undefined}
      {...props}
    >
      <span className={cn(
        "absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
        mobileOptimized && "left-3 h-5 w-5"
      )}>
        <DropdownMenuPrimitive.ItemIndicator>
          <Circle className={cn("h-2 w-2 fill-current", mobileOptimized && "h-3 w-3")} />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.RadioItem>
  )
})
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean
    /** Mobile-optimized variant with better typography */
    mobileOptimized?: boolean
  }
>(({ className, inset, mobileOptimized = false, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold",
      mobileOptimized && "px-4 py-2 text-base font-medium",
      inset && (mobileOptimized ? "pl-12" : "pl-8"),
      className
    )}
    {...props}
  />
))
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator> & {
    /** Mobile-optimized variant with better spacing */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn(
      "-mx-1 my-1 h-px bg-muted",
      mobileOptimized && "-mx-2 my-2",
      className
    )}
    {...props}
  />
))
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({
  className,
  mobileOptimized = false,
  ...props
}: React.HTMLAttributes<HTMLSpanElement> & {
  /** Mobile-optimized variant with better typography */
  mobileOptimized?: boolean
}) => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest opacity-60",
        mobileOptimized && "text-sm hidden sm:inline-block",
        className
      )}
      {...props}
    />
  )
}
DropdownMenuShortcut.displayName = "DropdownMenuShortcut"

// Mobile-specific dropdown menu variants
const MobileDropdownMenu = DropdownMenuPrimitive.Root

const MobileDropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const MobileDropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ ...props }, ref) => (
  <DropdownMenuContent
    ref={ref}
    mobileOptimized
    {...props}
  />
))
MobileDropdownMenuContent.displayName = "MobileDropdownMenuContent"

const MobileDropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean
  }
>(({ ...props }, ref) => (
  <DropdownMenuItem
    ref={ref}
    mobileOptimized
    hapticFeedback
    {...props}
  />
))
MobileDropdownMenuItem.displayName = "MobileDropdownMenuItem"

const MobileDropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ ...props }, ref) => (
  <DropdownMenuCheckboxItem
    ref={ref}
    mobileOptimized
    hapticFeedback
    {...props}
  />
))
MobileDropdownMenuCheckboxItem.displayName = "MobileDropdownMenuCheckboxItem"

const MobileDropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ ...props }, ref) => (
  <DropdownMenuRadioItem
    ref={ref}
    mobileOptimized
    hapticFeedback
    {...props}
  />
))
MobileDropdownMenuRadioItem.displayName = "MobileDropdownMenuRadioItem"

const MobileDropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean
  }
>(({ ...props }, ref) => (
  <DropdownMenuLabel
    ref={ref}
    mobileOptimized
    {...props}
  />
))
MobileDropdownMenuLabel.displayName = "MobileDropdownMenuLabel"

const MobileDropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ ...props }, ref) => (
  <DropdownMenuSeparator
    ref={ref}
    mobileOptimized
    {...props}
  />
))
MobileDropdownMenuSeparator.displayName = "MobileDropdownMenuSeparator"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
  // Mobile variants
  MobileDropdownMenu,
  MobileDropdownMenuTrigger,
  MobileDropdownMenuContent,
  MobileDropdownMenuItem,
  MobileDropdownMenuCheckboxItem,
  MobileDropdownMenuRadioItem,
  MobileDropdownMenuLabel,
  MobileDropdownMenuSeparator,
} 