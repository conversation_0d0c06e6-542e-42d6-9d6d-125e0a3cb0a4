// @ts-nocheck
import React, { useState, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Input } from './input';
import { Button } from './button';
import { Check, ChevronDown, Phone } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './command';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { getCountries, getCountryCallingCode } from 'react-phone-number-input/input';
import en from 'react-phone-number-input/locale/en.json';
import type { Country } from 'react-phone-number-input';

interface PhoneInputProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: string;
  defaultCountry?: Country;
  international?: boolean;
}

const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ 
    value, 
    onChange, 
    placeholder = "Enter phone number", 
    disabled, 
    className, 
    error, 
    defaultCountry = "US",
    international = false,
    ...props 
  }, ref) => {
    const [selectedCountry, setSelectedCountry] = React.useState<Country>(defaultCountry);
    const [open, setOpen] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    
    const countries = getCountries();
    
    const getCountryDisplayName = (countryCode: Country): string => {
      return (en as Record<string, string>)[countryCode] || countryCode;
    };

    const handleCountryChange = (countryCode: Country) => {
      setSelectedCountry(countryCode);
      setOpen(false);
      // Reset phone number when country changes
      if (onChange) {
        onChange(undefined);
      }
    };

    const handlePhoneChange = (phoneValue: string) => {
      if (onChange) {
        onChange(phoneValue);
      }
    };

    const formatPhoneNumber = (phone: string, country: Country) => {
      if (!phone) return '';
      
      // Remove country code from display
      const callingCode = getCountryCallingCode(country);
      if (phone.startsWith(`+${callingCode}`)) {
        return phone.slice(`+${callingCode}`.length);
      }
      return phone;
    };

    const getCurrentPhoneValue = () => {
      if (!value) return '';
      const callingCode = getCountryCallingCode(selectedCountry);
      if (value.startsWith(`+${callingCode}`)) {
        return value.slice(`+${callingCode}`.length);
      }
      return value;
    };

    return (
      <div className={cn("flex w-full group", className)}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              aria-label="Select country"
              className={cn(
                "h-12 w-[150px] justify-between rounded-r-none border-r-0 px-4 bg-white/70 backdrop-blur-sm border-slate-200/60 hover:border-slate-300/80 shadow-sm hover:shadow-md transition-all duration-200",
                "focus-visible:ring-2 focus-visible:ring-emerald-500/30 focus-visible:border-emerald-400 focus-visible:shadow-lg",
                error && "border-red-300/80 bg-red-50/50 focus-visible:ring-red-500/30 focus-visible:border-red-400",
                disabled && "cursor-not-allowed opacity-50",
                open && "border-emerald-400 ring-2 ring-emerald-500/30 shadow-lg"
              )}
              disabled={disabled}
            >
              <div className="flex items-center gap-3">
                <span className="text-xl leading-none">
                  {String.fromCodePoint(
                    ...[...selectedCountry.toUpperCase()]
                      .map((char) => 127397 + char.charCodeAt(0))
                  )}
                </span>
                <span className="text-sm font-medium text-slate-700">
                  +{getCountryCallingCode(selectedCountry)}
                </span>
              </div>
              <ChevronDown className={cn(
                "h-4 w-4 shrink-0 transition-all duration-200",
                open ? "rotate-180 text-emerald-500" : "text-slate-400"
              )} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[320px] p-0 border-slate-200/60 shadow-xl backdrop-blur-sm bg-white/95">
            <Command className="bg-transparent">
              <CommandInput 
                placeholder="Search country..." 
                className="border-none bg-transparent placeholder:text-slate-400"
              />
              <CommandEmpty className="text-slate-500 text-sm py-6">No country found.</CommandEmpty>
              <CommandList className="max-h-[200px]">
                <CommandGroup>
                  {countries.map((country) => (
                    <CommandItem
                      key={country}
                      value={`${country} ${getCountryDisplayName(country)}`}
                      onSelect={() => handleCountryChange(country)}
                      className="cursor-pointer hover:bg-emerald-50/50 px-3 py-2.5 transition-colors duration-150"
                    >
                      <Check
                        className={cn(
                          "mr-3 h-4 w-4 text-emerald-500",
                          selectedCountry === country ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <span className="mr-3 text-xl leading-none">
                        {String.fromCodePoint(
                          ...[...country.toUpperCase()]
                            .map((char) => 127397 + char.charCodeAt(0))
                        )}
                      </span>
                      <span className="flex-1 font-medium text-slate-700">
                        {getCountryDisplayName(country)}
                      </span>
                      <span className="text-sm text-slate-500 font-medium">
                        +{getCountryCallingCode(country)}
                      </span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        
        <div className="relative flex-1">
          <Phone className={cn(
            "absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 transition-colors duration-200",
            isFocused ? "text-emerald-500" : "text-slate-400",
            error && "text-red-500"
          )} />
          <Input
            ref={ref}
            type="tel"
            placeholder={placeholder}
            value={getCurrentPhoneValue()}
            onChange={(e) => {
              const inputValue = e.target.value;
              const callingCode = getCountryCallingCode(selectedCountry);
              const fullPhoneNumber = inputValue ? `+${callingCode}${inputValue}` : '';
              handlePhoneChange(fullPhoneNumber);
            }}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            disabled={disabled}
            error={error}
            className={cn(
              "rounded-l-none border-l-0 pl-12 font-medium text-slate-700"
            )}
            {...props}
          />
        </div>
      </div>
    );
  }
);

PhoneInput.displayName = "PhoneInput";

export { PhoneInput }; 