import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

// Mobile-first spacing system based on 4px base unit
// Follows iOS and Android design guidelines for touch targets

export const mobileSpacing = {
  // Base spacing units (rem values)
  xs: "0.25rem",    // 4px
  sm: "0.5rem",     // 8px
  md: "0.75rem",    // 12px
  lg: "1rem",       // 16px
  xl: "1.5rem",     // 24px
  "2xl": "2rem",    // 32px
  "3xl": "3rem",    // 48px
  "4xl": "4rem",    // 64px
  "5xl": "5rem",    // 80px
  
  // Touch-friendly spacing (minimum 44px for iOS)
  touch: "2.75rem", // 44px
  "touch-sm": "2rem", // 32px
  "touch-lg": "3rem", // 48px
  
  // Mobile-specific spacing
  "mobile-xs": "0.5rem",   // 8px
  "mobile-sm": "1rem",     // 16px
  "mobile-md": "1.5rem",   // 24px
  "mobile-lg": "2rem",     // 32px
  "mobile-xl": "3rem",     // 48px
  
  // Layout spacing
  "section": "2rem",       // 32px
  "component": "1.5rem",   // 24px
  "element": "1rem",       // 16px
  "content": "0.75rem",    // 12px
} as const

export type MobileSpacingValue = keyof typeof mobileSpacing

// Spacing component variants
const spacingVariants = cva("", {
  variants: {
    space: {
      xs: "space-y-1",
      sm: "space-y-2", 
      md: "space-y-3",
      lg: "space-y-4",
      xl: "space-y-6",
      "2xl": "space-y-8",
      "3xl": "space-y-12",
      "4xl": "space-y-16",
      "5xl": "space-y-20",
      touch: "space-y-11",
      "touch-sm": "space-y-8",
      "touch-lg": "space-y-12",
      "mobile-xs": "space-y-2",
      "mobile-sm": "space-y-4",
      "mobile-md": "space-y-6",
      "mobile-lg": "space-y-8",
      "mobile-xl": "space-y-12",
      section: "space-y-8",
      component: "space-y-6",
      element: "space-y-4",
      content: "space-y-3",
    },
    gap: {
      xs: "gap-1",
      sm: "gap-2",
      md: "gap-3", 
      lg: "gap-4",
      xl: "gap-6",
      "2xl": "gap-8",
      "3xl": "gap-12",
      "4xl": "gap-16",
      "5xl": "gap-20",
      touch: "gap-11",
      "touch-sm": "gap-8",
      "touch-lg": "gap-12",
      "mobile-xs": "gap-2",
      "mobile-sm": "gap-4",
      "mobile-md": "gap-6",
      "mobile-lg": "gap-8",
      "mobile-xl": "gap-12",
      section: "gap-8",
      component: "gap-6",
      element: "gap-4",
      content: "gap-3",
    },
    padding: {
      xs: "p-1",
      sm: "p-2",
      md: "p-3",
      lg: "p-4",
      xl: "p-6",
      "2xl": "p-8",
      "3xl": "p-12",
      "4xl": "p-16",
      "5xl": "p-20",
      touch: "p-11",
      "touch-sm": "p-8",
      "touch-lg": "p-12",
      "mobile-xs": "p-2",
      "mobile-sm": "p-4",
      "mobile-md": "p-6",
      "mobile-lg": "p-8",
      "mobile-xl": "p-12",
      section: "p-8",
      component: "p-6",
      element: "p-4",
      content: "p-3",
    },
    margin: {
      xs: "m-1",
      sm: "m-2",
      md: "m-3",
      lg: "m-4",
      xl: "m-6",
      "2xl": "m-8",
      "3xl": "m-12",
      "4xl": "m-16",
      "5xl": "m-20",
      touch: "m-11",
      "touch-sm": "m-8",
      "touch-lg": "m-12",
      "mobile-xs": "m-2",
      "mobile-sm": "m-4",
      "mobile-md": "m-6",
      "mobile-lg": "m-8",
      "mobile-xl": "m-12",
      section: "m-8",
      component: "m-6",
      element: "m-4",
      content: "m-3",
    },
  },
  defaultVariants: {
    space: "md",
    gap: "md",
    padding: "md",
    margin: "md",
  },
})

export interface MobileSpacingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spacingVariants> {
  /** Apply mobile-first responsive spacing */
  responsive?: boolean
  /** Custom spacing value */
  customSpacing?: string
  /** Apply touch-friendly spacing */
  touchFriendly?: boolean
}

// Spacing Wrapper Component
const MobileSpacing = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ 
    className, 
    space, 
    gap, 
    padding, 
    margin,
    responsive = false,
    customSpacing,
    touchFriendly = false,
    children,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          spacingVariants({ 
            space: touchFriendly ? "touch" : space,
            gap: touchFriendly ? "touch" : gap,
            padding: touchFriendly ? "touch" : padding,
            margin: touchFriendly ? "touch" : margin,
          }),
          responsive && "space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-6",
          className
        )}
        style={customSpacing ? { gap: customSpacing } : undefined}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileSpacing.displayName = "MobileSpacing"

// Vertical Stack Component
const MobileVStack = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ space = "mobile-sm", className, ...props }, ref) => (
    <MobileSpacing
      ref={ref}
      space={space}
      className={cn("flex flex-col", className)}
      {...props}
    />
  )
)
MobileVStack.displayName = "MobileVStack"

// Horizontal Stack Component
const MobileHStack = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ gap = "mobile-sm", className, ...props }, ref) => (
    <MobileSpacing
      ref={ref}
      gap={gap}
      className={cn("flex flex-row items-center", className)}
      {...props}
    />
  )
)
MobileHStack.displayName = "MobileHStack"

// Section Container with proper spacing
const MobileSection = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ padding = "section", space = "section", className, ...props }, ref) => (
    <MobileSpacing
      ref={ref}
      padding={padding}
      space={space}
      className={cn("w-full", className)}
      {...props}
    />
  )
)
MobileSection.displayName = "MobileSection"

// Component Container with proper spacing
const MobileContainer = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ padding = "component", space = "component", className, ...props }, ref) => (
    <MobileSpacing
      ref={ref}
      padding={padding}
      space={space}
      className={cn("w-full", className)}
      {...props}
    />
  )
)
MobileContainer.displayName = "MobileContainer"

// Touch-friendly spacing component
const MobileTouchArea = React.forwardRef<HTMLDivElement, MobileSpacingProps>(
  ({ className, ...props }, ref) => (
    <MobileSpacing
      ref={ref}
      touchFriendly
      className={cn("min-h-[44px] min-w-[44px] flex items-center justify-center", className)}
      {...props}
    />
  )
)
MobileTouchArea.displayName = "MobileTouchArea"

// Responsive spacing utilities
export const mobileSpacingClasses = {
  // Responsive padding
  paddingResponsive: "p-3 sm:p-4 md:p-5 lg:p-6",
  paddingXResponsive: "px-3 sm:px-4 md:px-5 lg:px-6",
  paddingYResponsive: "py-3 sm:py-4 md:py-5 lg:py-6",
  
  // Responsive margin
  marginResponsive: "m-3 sm:m-4 md:m-5 lg:m-6",
  marginXResponsive: "mx-3 sm:mx-4 md:mx-5 lg:mx-6",
  marginYResponsive: "my-3 sm:my-4 md:my-5 lg:my-6",
  
  // Responsive gap
  gapResponsive: "gap-3 sm:gap-4 md:gap-5 lg:gap-6",
  gapXResponsive: "gap-x-3 sm:gap-x-4 md:gap-x-5 lg:gap-x-6",
  gapYResponsive: "gap-y-3 sm:gap-y-4 md:gap-y-5 lg:gap-y-6",
  
  // Responsive space
  spaceResponsive: "space-y-3 sm:space-y-4 md:space-y-5 lg:space-y-6",
  spaceXResponsive: "space-x-3 sm:space-x-4 md:space-x-5 lg:space-x-6",
  
  // Touch-friendly spacing
  touchPadding: "p-11", // 44px
  touchMargin: "m-11", // 44px
  touchGap: "gap-11", // 44px
  touchSpace: "space-y-11", // 44px
  
  // Mobile-specific layouts
  mobileSection: "px-4 py-6 sm:px-6 sm:py-8 md:px-8 md:py-10",
  mobileContainer: "px-3 py-4 sm:px-4 sm:py-6 md:px-6 md:py-8",
  mobileCard: "p-4 sm:p-5 md:p-6",
  mobileButton: "px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3",
  mobileInput: "px-3 py-2 sm:px-4 sm:py-2.5 md:px-5 md:py-3",
  
  // Mobile grid spacing
  mobileGrid: "grid gap-3 sm:gap-4 md:gap-5 lg:gap-6",
  mobileGridTight: "grid gap-2 sm:gap-3 md:gap-4",
  mobileGridLoose: "grid gap-4 sm:gap-5 md:gap-6 lg:gap-8",
  
  // Mobile list spacing
  mobileList: "space-y-2 sm:space-y-3 md:space-y-4",
  mobileListTight: "space-y-1 sm:space-y-2 md:space-y-3",
  mobileListLoose: "space-y-3 sm:space-y-4 md:space-y-5 lg:space-y-6",
} as const

// Utility function to get mobile spacing value
export const getMobileSpacing = (size: MobileSpacingValue): string => {
  return mobileSpacing[size]
}

// Utility function to create responsive spacing classes
export const createResponsiveSpacing = (
  baseSize: MobileSpacingValue,
  type: "padding" | "margin" | "gap" | "space" = "padding"
): string => {
  const sizeMap = {
    xs: { sm: "sm", md: "md", lg: "lg" },
    sm: { sm: "md", md: "lg", lg: "xl" },
    md: { sm: "lg", md: "xl", lg: "2xl" },
    lg: { sm: "xl", md: "2xl", lg: "3xl" },
    xl: { sm: "2xl", md: "3xl", lg: "4xl" },
  } as const

  const sizes = sizeMap[baseSize as keyof typeof sizeMap]
  if (!sizes) return ""

  const prefixes = {
    padding: "p",
    margin: "m",
    gap: "gap",
    space: "space-y",
  }

  const prefix = prefixes[type]
  const baseClass = `${prefix}-${baseSize}`
  const smClass = `sm:${prefix}-${sizes.sm}`
  const mdClass = `md:${prefix}-${sizes.md}`
  const lgClass = `lg:${prefix}-${sizes.lg}`

  return `${baseClass} ${smClass} ${mdClass} ${lgClass}`
}

// Hook for mobile spacing
export const useMobileSpacing = () => {
  const [isMobile, setIsMobile] = React.useState(false)

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return {
    isMobile,
    spacing: isMobile ? mobileSpacing : mobileSpacing,
    classes: mobileSpacingClasses,
    getSpacing: getMobileSpacing,
    createResponsive: createResponsiveSpacing,
  }
}

export {
  MobileSpacing,
  MobileVStack,
  MobileHStack,
  MobileSection,
  MobileContainer,
  MobileTouchArea,
  spacingVariants,
} 