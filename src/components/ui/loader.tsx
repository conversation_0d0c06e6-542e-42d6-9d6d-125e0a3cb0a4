import React from "react";
import { cn } from "@/lib/utils";

interface LoaderProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "dots" | "pulse";
}

export const Loader: React.FC<LoaderProps> = ({ 
  className, 
  size = "md", 
  variant = "default" 
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  if (variant === "dots") {
    return (
      <div className={cn("flex space-x-1", className)}>
        <div className={cn("bg-current rounded-full animate-bounce", 
          size === "sm" ? "w-2 h-2" : size === "md" ? "w-3 h-3" : "w-4 h-4"
        )} style={{ animationDelay: "0ms" }}></div>
        <div className={cn("bg-current rounded-full animate-bounce", 
          size === "sm" ? "w-2 h-2" : size === "md" ? "w-3 h-3" : "w-4 h-4"
        )} style={{ animationDelay: "150ms" }}></div>
        <div className={cn("bg-current rounded-full animate-bounce", 
          size === "sm" ? "w-2 h-2" : size === "md" ? "w-3 h-3" : "w-4 h-4"
        )} style={{ animationDelay: "300ms" }}></div>
      </div>
    );
  }

  if (variant === "pulse") {
    return (
      <div className={cn(
        "rounded-full bg-current animate-pulse opacity-75",
        sizeClasses[size],
        className
      )} />
    );
  }

  return (
    <div className={cn("animate-spin", sizeClasses[size], className)}>
      <svg
        className="w-full h-full"
        fill="none"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
}; 