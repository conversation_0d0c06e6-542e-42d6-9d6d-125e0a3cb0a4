// Mobile-First Responsive Grid System
// Enterprise-grade responsive components for mobile-first design

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// ============================================================================
// Responsive Grid System
// ============================================================================

const gridVariants = cva(
  "grid gap-4",
  {
    variants: {
      cols: {
        1: "grid-cols-1",
        2: "grid-cols-1 sm:grid-cols-2",
        3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
        4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
        5: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5",
        6: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6",
        12: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-12",
      },
      gap: {
        none: "gap-0",
        sm: "gap-2",
        default: "gap-4",
        md: "gap-6",
        lg: "gap-8",
        xl: "gap-12",
      },
      align: {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch",
      },
    },
    defaultVariants: {
      cols: 1,
      gap: "default",
      align: "stretch",
    },
  }
);

const flexVariants = cva(
  "flex",
  {
    variants: {
      direction: {
        row: "flex-row",
        column: "flex-col",
        responsive: "flex-col sm:flex-row",
      },
      wrap: {
        nowrap: "flex-nowrap",
        wrap: "flex-wrap",
        reverse: "flex-wrap-reverse",
      },
      justify: {
        start: "justify-start",
        center: "justify-center",
        end: "justify-end",
        between: "justify-between",
        around: "justify-around",
        evenly: "justify-evenly",
      },
      align: {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch",
        baseline: "items-baseline",
      },
      gap: {
        none: "gap-0",
        sm: "gap-2",
        default: "gap-4",
        md: "gap-6",
        lg: "gap-8",
        xl: "gap-12",
      },
    },
    defaultVariants: {
      direction: "row",
      wrap: "nowrap",
      justify: "start",
      align: "start",
      gap: "default",
    },
  }
);

// ============================================================================
// Responsive Grid Component
// ============================================================================

export interface ResponsiveGridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {
  children: React.ReactNode;
  auto?: boolean;
  minChildWidth?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols,
  gap,
  align,
  auto = false,
  minChildWidth = "280px",
  ...props
}) => {
  const gridStyle = auto ? {
    gridTemplateColumns: `repeat(auto-fit, minmax(${minChildWidth}, 1fr))`,
  } : undefined;

  return (
    <div
      className={cn(
        auto ? "grid" : gridVariants({ cols, gap, align }),
        className
      )}
      style={gridStyle}
      {...props}
    >
      {children}
    </div>
  );
};

// ============================================================================
// Responsive Flex Component
// ============================================================================

export interface ResponsiveFlexProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof flexVariants> {
  children: React.ReactNode;
}

export const ResponsiveFlex: React.FC<ResponsiveFlexProps> = ({
  children,
  className,
  direction,
  wrap,
  justify,
  align,
  gap,
  ...props
}) => {
  return (
    <div
      className={cn(
        flexVariants({ direction, wrap, justify, align, gap }),
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// ============================================================================
// Mobile-First Container Component
// ============================================================================

const containerVariants = cva(
  "w-full mx-auto px-4",
  {
    variants: {
      size: {
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-xl",
        "2xl": "max-w-2xl",
        "3xl": "max-w-3xl",
        "4xl": "max-w-4xl",
        "5xl": "max-w-5xl",
        "6xl": "max-w-6xl",
        "7xl": "max-w-7xl",
        full: "max-w-full",
        screen: "max-w-screen-2xl",
      },
      padding: {
        none: "px-0",
        sm: "px-2 sm:px-4",
        default: "px-4 sm:px-6",
        md: "px-4 sm:px-6 lg:px-8",
        lg: "px-4 sm:px-6 lg:px-8 xl:px-12",
      },
    },
    defaultVariants: {
      size: "7xl",
      padding: "default",
    },
  }
);

export interface ResponsiveContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {
  children: React.ReactNode;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  size,
  padding,
  ...props
}) => {
  return (
    <div
      className={cn(containerVariants({ size, padding }), className)}
      {...props}
    >
      {children}
    </div>
  );
};

// ============================================================================
// Mobile-First Stack Component
// ============================================================================

const stackVariants = cva(
  "flex flex-col",
  {
    variants: {
      spacing: {
        none: "space-y-0",
        xs: "space-y-1",
        sm: "space-y-2",
        default: "space-y-4",
        md: "space-y-6",
        lg: "space-y-8",
        xl: "space-y-12",
      },
      align: {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch",
      },
      responsive: {
        true: "sm:flex-row sm:space-y-0 sm:space-x-4",
        false: "",
      },
    },
    defaultVariants: {
      spacing: "default",
      align: "stretch",
      responsive: false,
    },
  }
);

export interface ResponsiveStackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stackVariants> {
  children: React.ReactNode;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  className,
  spacing,
  align,
  responsive,
  ...props
}) => {
  return (
    <div
      className={cn(stackVariants({ spacing, align, responsive }), className)}
      {...props}
    >
      {children}
    </div>
  );
};

// ============================================================================
// Mobile-First Section Component
// ============================================================================

const sectionVariants = cva(
  "w-full",
  {
    variants: {
      spacing: {
        none: "py-0",
        xs: "py-2",
        sm: "py-4",
        default: "py-6 sm:py-8",
        md: "py-8 sm:py-12",
        lg: "py-12 sm:py-16",
        xl: "py-16 sm:py-20",
      },
      background: {
        none: "",
        muted: "bg-muted/50",
        card: "bg-card",
        gradient: "bg-gradient-to-br from-background to-muted/20",
      },
    },
    defaultVariants: {
      spacing: "default",
      background: "none",
    },
  }
);

export interface ResponsiveSectionProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof sectionVariants> {
  children: React.ReactNode;
  containerSize?: ResponsiveContainerProps['size'];
}

export const ResponsiveSection: React.FC<ResponsiveSectionProps> = ({
  children,
  className,
  spacing,
  background,
  containerSize = "7xl",
  ...props
}) => {
  return (
    <section
      className={cn(sectionVariants({ spacing, background }), className)}
      {...props}
    >
      <ResponsiveContainer size={containerSize}>
        {children}
      </ResponsiveContainer>
    </section>
  );
};

// ============================================================================
// Responsive Visibility Utilities
// ============================================================================

export interface ResponsiveShowProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  above?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  below?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  only?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export const ResponsiveShow: React.FC<ResponsiveShowProps> = ({
  children,
  className,
  above,
  below,
  only,
  ...props
}) => {
  const visibilityClass = cn(
    // Base visibility
    "block",
    
    // Above breakpoint
    above === 'sm' && "hidden sm:block",
    above === 'md' && "hidden md:block",
    above === 'lg' && "hidden lg:block",
    above === 'xl' && "hidden xl:block",
    above === '2xl' && "hidden 2xl:block",
    
    // Below breakpoint
    below === 'sm' && "block sm:hidden",
    below === 'md' && "block md:hidden",
    below === 'lg' && "block lg:hidden",
    below === 'xl' && "block xl:hidden",
    below === '2xl' && "block 2xl:hidden",
    
    // Only at breakpoint
    only === 'sm' && "hidden sm:block md:hidden",
    only === 'md' && "hidden md:block lg:hidden",
    only === 'lg' && "hidden lg:block xl:hidden",
    only === 'xl' && "hidden xl:block 2xl:hidden",
    only === '2xl' && "hidden 2xl:block",
    
    className
  );

  return (
    <div className={visibilityClass} {...props}>
      {children}
    </div>
  );
};

// ============================================================================
// Responsive Breakpoint Hook
// ============================================================================

export const useResponsiveBreakpoint = () => {
  const [breakpoint, setBreakpoint] = React.useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('xs');

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width >= 1536) setBreakpoint('2xl');
      else if (width >= 1280) setBreakpoint('xl');
      else if (width >= 1024) setBreakpoint('lg');
      else if (width >= 768) setBreakpoint('md');
      else if (width >= 640) setBreakpoint('sm');
      else setBreakpoint('xs');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    isXs: breakpoint === 'xs',
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    is2Xl: breakpoint === '2xl',
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    isTablet: breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl',
  };
};

// ============================================================================
// Mobile-First Utility Classes
// ============================================================================

export const mobileFirst = {
  // Spacing utilities
  spacing: {
    xs: "p-2 sm:p-4",
    sm: "p-4 sm:p-6",
    md: "p-6 sm:p-8",
    lg: "p-8 sm:p-12",
    xl: "p-12 sm:p-16",
  },
  
  // Typography utilities
  typography: {
    h1: "text-2xl sm:text-3xl lg:text-4xl font-bold",
    h2: "text-xl sm:text-2xl lg:text-3xl font-semibold",
    h3: "text-lg sm:text-xl lg:text-2xl font-medium",
    h4: "text-base sm:text-lg lg:text-xl font-medium",
    body: "text-sm sm:text-base",
    small: "text-xs sm:text-sm",
  },
  
  // Layout utilities
  layout: {
    fullHeight: "min-h-screen",
    section: "py-6 sm:py-8 lg:py-12",
    card: "p-4 sm:p-6 lg:p-8",
    button: "px-4 py-2 sm:px-6 sm:py-3",
  },
}; 