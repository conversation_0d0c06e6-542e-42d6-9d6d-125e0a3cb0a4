// @ts-nocheck
// Mobile-First Page Layout Components
// Enterprise-grade responsive layout system with mobile-first approach

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { ResponsiveContainer, ResponsiveStack, ResponsiveFlex, mobileFirst } from './responsive-grid';

// ============================================================================
// Enhanced Page Layout Component
// ============================================================================

const pageLayoutVariants = cva(
  "w-full",
  {
    variants: {
      height: {
        auto: "min-h-0",
        screen: "min-h-screen",
        full: "h-full",
        viewport: "min-h-[100vh]",
      },
      padding: {
        none: "p-0",
        xs: mobileFirst.spacing.xs,
        sm: mobileFirst.spacing.sm,
        default: mobileFirst.spacing.md,
        lg: mobileFirst.spacing.lg,
        xl: mobileFirst.spacing.xl,
      },
      spacing: {
        none: "space-y-0",
        xs: "space-y-2",
        sm: "space-y-4",
        default: "space-y-6",
        md: "space-y-8",
        lg: "space-y-12",
      },
      background: {
        none: "",
        muted: "bg-muted/30",
        card: "bg-card",
        gradient: "bg-gradient-to-br from-background via-muted/10 to-background",
      },
    },
    defaultVariants: {
      height: "auto",
      padding: "default",
      spacing: "default",
      background: "none",
    },
  }
);

export interface PageLayoutProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof pageLayoutVariants> {
  children: React.ReactNode;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full' | 'screen';
  bottomNavbarSpace?: boolean;
  safeArea?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  className,
  height,
  padding,
  spacing,
  background,
  containerSize = "7xl",
  bottomNavbarSpace = false,
  safeArea = true,
  ...props
}) => {
  return (
    <div
      className={cn(
        pageLayoutVariants({ height, padding, spacing, background }),
        // Bottom navbar space for mobile
        bottomNavbarSpace && "pb-20 sm:pb-24",
        // Safe area for mobile devices
        safeArea && "pt-safe-top pb-safe-bottom",
        className
      )}
      {...props}
    >
      <ResponsiveContainer size={containerSize}>
        {children}
      </ResponsiveContainer>
    </div>
  );
};

// ============================================================================
// Mobile-First Dashboard Layout
// ============================================================================

export interface MobileDashboardLayoutProps extends Omit<PageLayoutProps, 'children'> {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  headerContent?: React.ReactNode;
  children: React.ReactNode;
  sticky?: boolean;
  collapsible?: boolean;
}

export const MobileDashboardLayout: React.FC<MobileDashboardLayoutProps> = ({
  title,
  subtitle,
  actions,
  headerContent,
  children,
  className,
  sticky = false,
  collapsible = false,
  ...props
}) => {
  const [headerCollapsed, setHeaderCollapsed] = React.useState(false);

  return (
    <PageLayout
      className={cn("space-y-4 sm:space-y-6", className)}
      bottomNavbarSpace={true}
      {...props}
    >
      {/* Mobile-First Header */}
      {(title || subtitle || actions || headerContent) && (
        <div className={cn(
          "space-y-3 sm:space-y-4",
          sticky && "sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-border/50 -mx-4 px-4 py-3 sm:-mx-6 sm:px-6"
        )}>
          {/* Title and Actions */}
          {(title || actions) && (
            <ResponsiveFlex
              direction="responsive"
              justify="between"
              align="center"
              gap="md"
              className="items-start sm:items-center"
            >
              {/* Title Section */}
              {title && (
                <div className="flex-1 min-w-0">
                  <h1 className={cn(
                    mobileFirst.typography.h1,
                    "text-foreground tracking-tight"
                  )}>
                    {title}
                  </h1>
                  {subtitle && (
                    <p className={cn(
                      mobileFirst.typography.body,
                      "text-muted-foreground mt-1 sm:mt-2"
                    )}>
                      {subtitle}
                    </p>
                  )}
                </div>
              )}
              
              {/* Actions Section */}
              {actions && (
                <div className="flex-shrink-0">
                  <ResponsiveFlex
                    direction="responsive"
                    gap="sm"
                    className="w-full sm:w-auto"
                  >
                    {actions}
                  </ResponsiveFlex>
                </div>
              )}
            </ResponsiveFlex>
          )}
          
          {/* Additional Header Content */}
          {headerContent && !headerCollapsed && (
            <div className="w-full">
              {headerContent}
            </div>
          )}
          
          {/* Collapsible Toggle */}
          {collapsible && headerContent && (
            <button
              onClick={() => setHeaderCollapsed(!headerCollapsed)}
              className="w-full text-center text-sm text-muted-foreground hover:text-foreground transition-colors py-2 border-t border-border/50"
            >
              {headerCollapsed ? 'Show Details' : 'Hide Details'}
            </button>
          )}
        </div>
      )}
      
      {/* Page Content */}
      <div className="space-y-4 sm:space-y-6">
        {children}
      </div>
    </PageLayout>
  );
};

// ============================================================================
// Mobile-First Content Layout
// ============================================================================

export interface MobileContentLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  sidebarPosition?: 'left' | 'right';
  sidebarCollapsible?: boolean;
  sidebarWidth?: 'sm' | 'md' | 'lg' | 'xl';
  stackOnMobile?: boolean;
}

export const MobileContentLayout: React.FC<MobileContentLayoutProps> = ({
  children,
  sidebar,
  sidebarPosition = 'left',
  sidebarCollapsible = true,
  sidebarWidth = 'md',
  stackOnMobile = true,
  className,
  ...props
}) => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const sidebarWidthClasses = {
    sm: "w-64",
    md: "w-72",
    lg: "w-80",
    xl: "w-96",
  };

  if (!sidebar) {
    return (
      <div className={cn("w-full", className)} {...props}>
        {children}
      </div>
    );
  }

  return (
    <div className={cn(
      "flex",
      stackOnMobile ? "flex-col lg:flex-row" : "flex-row",
      "gap-4 sm:gap-6 lg:gap-8",
      className
    )} {...props}>
      {/* Sidebar */}
      <aside className={cn(
        "flex-shrink-0",
        stackOnMobile ? "w-full lg:w-auto" : "w-auto",
        !stackOnMobile && sidebarWidthClasses[sidebarWidth],
        sidebarPosition === 'right' && "order-2"
      )}>
        {/* Mobile Sidebar Toggle */}
        {sidebarCollapsible && stackOnMobile && (
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="lg:hidden w-full mb-4 px-4 py-3 text-left text-sm font-medium text-muted-foreground hover:text-foreground bg-card border border-border rounded-lg transition-colors"
          >
            {sidebarOpen ? 'Hide Sidebar' : 'Show Sidebar'}
          </button>
        )}
        
        {/* Sidebar Content */}
        <div className={cn(
          stackOnMobile && sidebarCollapsible && "lg:block",
          stackOnMobile && sidebarCollapsible && !sidebarOpen && "hidden",
          !stackOnMobile && sidebarWidthClasses[sidebarWidth]
        )}>
          {sidebar}
        </div>
      </aside>
      
      {/* Main Content */}
      <main className={cn(
        "flex-1 min-w-0",
        sidebarPosition === 'right' && "order-1"
      )}>
        {children}
      </main>
    </div>
  );
};

// ============================================================================
// Mobile-First Card Layout
// ============================================================================

const mobileCardVariants = cva(
  "bg-card text-card-foreground border border-border/50 transition-all duration-200",
  {
    variants: {
      rounded: {
        none: "rounded-none",
        sm: "rounded-sm",
        default: "rounded-lg",
        lg: "rounded-xl",
        xl: "rounded-2xl",
      },
      padding: {
        none: "p-0",
        xs: "p-3 sm:p-4",
        sm: "p-4 sm:p-5",
        default: "p-4 sm:p-6",
        lg: "p-6 sm:p-8",
        xl: "p-8 sm:p-10",
      },
      shadow: {
        none: "shadow-none",
        sm: "shadow-sm",
        default: "shadow-md",
        lg: "shadow-lg hover:shadow-xl",
        xl: "shadow-xl hover:shadow-2xl",
      },
      interactive: {
        true: "hover:shadow-lg hover:-translate-y-0.5 cursor-pointer active:scale-[0.98]",
        false: "",
      },
    },
    defaultVariants: {
      rounded: "default",
      padding: "default",
      shadow: "default",
      interactive: false,
    },
  }
);

export interface MobileCardLayoutProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof mobileCardVariants> {
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  headerPadding?: boolean;
  footerPadding?: boolean;
}

export const MobileCardLayout: React.FC<MobileCardLayoutProps> = ({
  children,
  header,
  footer,
  headerPadding = true,
  footerPadding = true,
  className,
  rounded,
  padding,
  shadow,
  interactive,
  ...props
}) => {
  return (
    <div
      className={cn(
        mobileCardVariants({ rounded, padding: padding || "none", shadow, interactive }),
        className
      )}
      {...props}
    >
      {/* Header */}
      {header && (
        <div className={cn(
          "border-b border-border/50",
          headerPadding && mobileFirst.spacing.sm
        )}>
          {header}
        </div>
      )}
      
      {/* Content */}
      <div className={cn(
        padding && mobileFirst.spacing.sm
      )}>
        {children}
      </div>
      
      {/* Footer */}
      {footer && (
        <div className={cn(
          "border-t border-border/50",
          footerPadding && mobileFirst.spacing.sm
        )}>
          {footer}
        </div>
      )}
    </div>
  );
};

// ============================================================================
// Mobile-First Form Layout
// ============================================================================

export interface MobileFormLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  spacing?: 'sm' | 'default' | 'lg';
  stickyActions?: boolean;
}

export const MobileFormLayout: React.FC<MobileFormLayoutProps> = ({
  children,
  title,
  description,
  actions,
  spacing = 'default',
  stickyActions = false,
  className,
  ...props
}) => {
  const spacingClasses = {
    sm: "space-y-4",
    default: "space-y-6",
    lg: "space-y-8",
  };

  return (
    <div className={cn("w-full", className)} {...props}>
      {/* Form Header */}
      {(title || description) && (
        <div className="mb-6 sm:mb-8">
          {title && (
            <h2 className={cn(
              mobileFirst.typography.h2,
              "text-foreground mb-2"
            )}>
              {title}
            </h2>
          )}
          {description && (
            <p className={cn(
              mobileFirst.typography.body,
              "text-muted-foreground"
            )}>
              {description}
            </p>
          )}
        </div>
      )}
      
      {/* Form Content */}
      <div className={cn(spacingClasses[spacing], "mb-6 sm:mb-8")}>
        {children}
      </div>
      
      {/* Form Actions */}
      {actions && (
        <div className={cn(
          "border-t border-border/50 pt-6",
          stickyActions && "sticky bottom-0 bg-background/95 backdrop-blur-sm -mx-4 px-4 py-4 sm:-mx-6 sm:px-6"
        )}>
          <ResponsiveFlex
            direction="responsive"
            justify="end"
            gap="sm"
            className="w-full sm:w-auto"
          >
            {actions}
          </ResponsiveFlex>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// Legacy Export (for backward compatibility)
// ============================================================================

export const DashboardPageLayout = MobileDashboardLayout;
export default PageLayout; 