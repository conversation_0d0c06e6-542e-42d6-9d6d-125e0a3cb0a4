import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const progressVariants = cva(
  "relative w-full bg-gray-200 rounded-full overflow-hidden",
  {
    variants: {
      size: {
        sm: "h-1",
        default: "h-2",
        lg: "h-3",
        xl: "h-4",
        // Mobile-specific sizes
        "mobile-sm": "h-2",
        "mobile-md": "h-3",
        "mobile-lg": "h-4",
      },
      variant: {
        default: "bg-gray-200",
        primary: "bg-blue-100",
        success: "bg-green-100",
        warning: "bg-yellow-100",
        error: "bg-red-100",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  }
);

const progressBarVariants = cva(
  "h-full rounded-full transition-all duration-500 ease-out",
  {
    variants: {
      variant: {
        default: "bg-blue-600",
        primary: "bg-blue-600",
        success: "bg-green-600",
        warning: "bg-yellow-600",
        error: "bg-red-600",
      },
      animated: {
        true: "transition-all duration-500 ease-out",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      animated: true,
    },
  }
);

// ============================================================================
// Types
// ============================================================================

export interface ProgressProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof progressVariants> {
  /** Progress value (0-100) */
  value: number;
  /** Maximum value (default: 100) */
  max?: number;
  /** Show percentage text */
  showPercentage?: boolean;
  /** Animate progress changes */
  animated?: boolean;
  /** Progress bar variant */
  barVariant?: VariantProps<typeof progressBarVariants>['variant'];
  /** Mobile-optimized styling */
  mobileOptimized?: boolean;
}

// ============================================================================
// Progress Component
// ============================================================================

export const Progress: React.FC<ProgressProps> = ({
  className,
  value,
  max = 100,
  size,
  variant,
  showPercentage = false,
  animated = true,
  barVariant = "default",
  mobileOptimized = false,
  ...props
}) => {
  // Ensure value is within bounds
  const normalizedValue = Math.min(Math.max(value, 0), max);
  const percentage = (normalizedValue / max) * 100;

  // Auto-adjust size for mobile
  const displaySize = mobileOptimized ? "mobile-md" : size;

  return (
    <div className="w-full">
      <div
        className={cn(
          progressVariants({ size: displaySize, variant }),
          className
        )}
        role="progressbar"
        aria-valuenow={normalizedValue}
        aria-valuemin={0}
        aria-valuemax={max}
        {...props}
      >
        <div
          className={cn(
            progressBarVariants({ variant: barVariant, animated })
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {showPercentage && (
        <div className="flex justify-between items-center mt-1">
          <span className="text-xs text-muted-foreground">
            {Math.round(percentage)}%
          </span>
          <span className="text-xs text-muted-foreground">
            {normalizedValue}/{max}
          </span>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// Circular Progress Component
// ============================================================================

export interface CircularProgressProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Progress value (0-100) */
  value: number;
  /** Maximum value (default: 100) */
  max?: number;
  /** Size of the circular progress */
  size?: number;
  /** Stroke width */
  strokeWidth?: number;
  /** Show percentage text */
  showPercentage?: boolean;
  /** Color variant */
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error';
  /** Mobile-optimized styling */
  mobileOptimized?: boolean;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  className,
  value,
  max = 100,
  size = 40,
  strokeWidth = 4,
  showPercentage = false,
  variant = 'default',
  mobileOptimized = false,
  ...props
}) => {
  // Ensure value is within bounds
  const normalizedValue = Math.min(Math.max(value, 0), max);
  const percentage = (normalizedValue / max) * 100;
  
  // Auto-adjust size for mobile
  const displaySize = mobileOptimized ? Math.max(size, 44) : size;
  const displayStroke = mobileOptimized ? Math.max(strokeWidth, 3) : strokeWidth;
  
  // Calculate circle properties
  const radius = (displaySize - displayStroke) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  // Color variants
  const colorVariants = {
    default: 'stroke-blue-600',
    primary: 'stroke-blue-600',
    success: 'stroke-green-600',
    warning: 'stroke-yellow-600',
    error: 'stroke-red-600',
  };

  return (
    <div
      className={cn(
        "relative inline-flex items-center justify-center",
        className
      )}
      style={{ width: displaySize, height: displaySize }}
      {...props}
    >
      <svg
        className="transform -rotate-90"
        width={displaySize}
        height={displaySize}
      >
        {/* Background circle */}
        <circle
          cx={displaySize / 2}
          cy={displaySize / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={displayStroke}
          fill="transparent"
          className="text-gray-200"
        />
        
        {/* Progress circle */}
        <circle
          cx={displaySize / 2}
          cy={displaySize / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={displayStroke}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            "transition-all duration-500 ease-out",
            colorVariants[variant]
          )}
        />
      </svg>
      
      {showPercentage && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={cn(
            "font-medium",
            mobileOptimized ? "text-xs" : "text-sm"
          )}>
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// Step Progress Component
// ============================================================================

export interface StepProgressProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** Current step (0-based index) */
  currentStep: number;
  /** Total number of steps */
  totalSteps: number;
  /** Step labels */
  stepLabels?: string[];
  /** Show step numbers */
  showStepNumbers?: boolean;
  /** Mobile-optimized styling */
  mobileOptimized?: boolean;
}

export const StepProgress: React.FC<StepProgressProps> = ({
  className,
  currentStep,
  totalSteps,
  stepLabels,
  showStepNumbers = true,
  mobileOptimized = false,
  ...props
}) => {
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  return (
    <div
      className={cn(
        "w-full space-y-2",
        className
      )}
      {...props}
    >
      {/* Progress bar */}
      <Progress
        value={progressPercentage}
        size={mobileOptimized ? "mobile-md" : "default"}
        mobileOptimized={mobileOptimized}
      />
      
      {/* Step indicators */}
      <div className="flex justify-between items-center">
        {Array.from({ length: totalSteps }, (_, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const label = stepLabels?.[index] || `Step ${index + 1}`;
          
          return (
            <div
              key={index}
              className={cn(
                "flex flex-col items-center gap-1",
                mobileOptimized && "min-w-0"
              )}
            >
              <div
                className={cn(
                  "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium transition-colors",
                  isCompleted && "bg-green-600 text-white",
                  isCurrent && "bg-blue-600 text-white",
                  !isCompleted && !isCurrent && "bg-gray-200 text-gray-600"
                )}
              >
                {showStepNumbers && (index + 1)}
              </div>
              
              {stepLabels && (
                <span className={cn(
                  "text-xs text-center",
                  mobileOptimized && "hidden sm:block"
                )}>
                  {label}
                </span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// ============================================================================
// Default Export
// ============================================================================

export default Progress; 