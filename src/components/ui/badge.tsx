import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 touch-manipulation",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        success:
          "border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        warning:
          "border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
        info:
          "border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        gradient:
          "border-transparent bg-gradient-primary text-white shadow",
        // Mobile-specific variants
        "mobile-default":
          "border-transparent bg-primary text-primary-foreground shadow active:bg-primary/80 active:scale-95",
        "mobile-secondary":
          "border-transparent bg-secondary text-secondary-foreground active:bg-secondary/80 active:scale-95",
        "mobile-success":
          "border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 active:bg-green-200 active:scale-95",
        "mobile-warning":
          "border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 active:bg-yellow-200 active:scale-95",
        "mobile-info":
          "border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 active:bg-blue-200 active:scale-95",
        "mobile-destructive":
          "border-transparent bg-destructive text-destructive-foreground shadow active:bg-destructive/80 active:scale-95",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
        xl: "px-4 py-1.5 text-base",
        // Mobile-specific sizes
        "mobile-sm": "px-2.5 py-1 text-xs min-h-[24px]",
        "mobile-md": "px-3 py-1.5 text-sm min-h-[32px]",
        "mobile-lg": "px-4 py-2 text-base min-h-[40px]",
        "mobile-xl": "px-5 py-2.5 text-lg min-h-[44px]",
        "mobile-responsive": "px-2.5 py-1 text-xs min-h-[24px] sm:px-3 sm:py-1.5 sm:text-sm sm:min-h-[32px]",
      },
      interactive: {
        none: "",
        hover: "hover:shadow-sm cursor-pointer",
        clickable: "hover:shadow-sm cursor-pointer active:scale-95 transition-transform",
        "mobile-tap": "cursor-pointer active:scale-95 transition-transform duration-150",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      interactive: "none",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  /** Makes the badge clickable with proper accessibility */
  clickable?: boolean
  /** Mobile-optimized variant with better touch targets */
  mobileOptimized?: boolean
  /** Add haptic feedback on mobile devices */
  hapticFeedback?: boolean
  /** Show loading state */
  loading?: boolean
  /** Icon to display in the badge */
  icon?: React.ReactNode
  /** Show close button */
  closable?: boolean
  /** Close button callback */
  onClose?: () => void
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ 
    className, 
    variant, 
    size, 
    interactive,
    clickable = false,
    mobileOptimized = false,
    hapticFeedback = false,
    loading = false,
    icon,
    closable = false,
    onClose,
    onClick,
    children,
    ...props 
  }, ref) => {
    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (hapticFeedback && 'vibrate' in navigator) {
        navigator.vibrate(1)
      }
      onClick?.(e)
    }

    const handleClose = (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation()
      if (hapticFeedback && 'vibrate' in navigator) {
        navigator.vibrate(2)
      }
      onClose?.()
    }

    return (
      <div 
        ref={ref}
        className={cn(
          badgeVariants({ 
            variant: mobileOptimized ? "mobile-default" : variant, 
            size: mobileOptimized ? "mobile-responsive" : size,
            interactive: mobileOptimized ? "mobile-tap" : clickable ? "clickable" : interactive,
          }),
          loading && "opacity-70 animate-pulse",
          clickable && "focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1",
          className
        )}
        onClick={onClick ? handleClick : undefined}
        role={clickable ? "button" : undefined}
        tabIndex={clickable ? 0 : undefined}
        {...props}
      >
        {loading && (
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1" />
        )}
        {icon && <span className="mr-1">{icon}</span>}
        {children}
        {closable && (
          <button
            onClick={handleClose}
            className={cn(
              "ml-1 rounded-full focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-1",
              mobileOptimized ? "p-0.5 hover:bg-black/10 min-h-[20px] min-w-[20px]" : "p-0.5 hover:bg-black/10"
            )}
            aria-label="Close"
          >
            <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    )
  }
)
Badge.displayName = "Badge"

// Mobile-specific badge variants
const MobileBadge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ variant = "mobile-default", ...props }, ref) => (
    <Badge
      ref={ref}
      variant={variant}
      mobileOptimized
      hapticFeedback
      {...props}
    />
  )
)
MobileBadge.displayName = "MobileBadge"

const MobileClickableBadge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ variant = "mobile-default", ...props }, ref) => (
    <Badge
      ref={ref}
      variant={variant}
      mobileOptimized
      hapticFeedback
      clickable
      {...props}
    />
  )
)
MobileClickableBadge.displayName = "MobileClickableBadge"

const MobileStatusBadge = React.forwardRef<HTMLDivElement, BadgeProps & {
  status: "success" | "warning" | "info" | "destructive"
}>(
  ({ status, ...props }, ref) => {
    const statusVariants = {
      success: "mobile-success",
      warning: "mobile-warning", 
      info: "mobile-info",
      destructive: "mobile-destructive",
    } as const

    return (
      <Badge
        ref={ref}
        variant={statusVariants[status]}
        mobileOptimized
        {...props}
      />
    )
  }
)
MobileStatusBadge.displayName = "MobileStatusBadge"

const MobileInteractiveBadge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ variant = "mobile-default", ...props }, ref) => (
    <Badge
      ref={ref}
      variant={variant}
      mobileOptimized
      hapticFeedback
      clickable
      closable
      {...props}
    />
  )
)
MobileInteractiveBadge.displayName = "MobileInteractiveBadge"

export { 
  Badge, 
  badgeVariants,
  MobileBadge,
  MobileClickableBadge,
  MobileStatusBadge,
  MobileInteractiveBadge,
} 