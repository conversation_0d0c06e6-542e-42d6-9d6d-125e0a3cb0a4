// @ts-nocheck
import React, { useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { LucideIcon, ChevronRight, Home, RefreshCw, Calendar, Eye, EyeOff, ChevronUp, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const pageHeaderVariants = cva(
  "space-y-6 pb-8 border-b border-border/50",
  {
    variants: {
      variant: {
        default: "",
        gradient: "bg-gradient-to-r from-primary/5 to-secondary/5 -mx-6 px-6 py-8 rounded-lg mb-6",
        minimal: "border-none pb-6",
      },
      size: {
        sm: "space-y-4 pb-6",
        default: "space-y-6 pb-8",
        lg: "space-y-8 pb-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// ============================================================================
// Component Types
// ============================================================================

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: LucideIcon;
  current?: boolean;
}

export interface ActionItem {
  label: string;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  size?: 'default' | 'sm' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void | Promise<void>;
}

export interface PageHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof pageHeaderVariants> {
  title: string;
  subtitle?: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: ActionItem[];
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  icon?: LucideIcon;
  showBackButton?: boolean;
  onBack?: () => void;
}

// ============================================================================
// Helper Components
// ============================================================================

const Breadcrumbs: React.FC<{ items: BreadcrumbItem[] }> = ({ items }) => {
  if (!items.length) return null;

  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
      <Home className="h-4 w-4" />
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <ChevronRight className="h-4 w-4" />
          <div className="flex items-center space-x-1">
            {item.icon && <item.icon className="h-4 w-4" />}
            {item.href ? (
              <a 
                href={item.href} 
                className={cn(
                  "hover:text-foreground transition-colors",
                  item.current && "text-foreground font-medium"
                )}
              >
                {item.label}
              </a>
            ) : (
              <span className={cn(
                item.current && "text-foreground font-medium"
              )}>
                {item.label}
              </span>
            )}
          </div>
        </React.Fragment>
      ))}
    </nav>
  );
};

const ActionButtons: React.FC<{ actions: ActionItem[] }> = ({ actions }) => {
  if (!actions.length) return null;

  return (
    <div className="flex items-center gap-3">
      {actions.map((action, index) => (
        <Button
          key={index}
          variant={action.variant}
          size={action.size}
          disabled={action.disabled || action.loading}
          onClick={action.onClick}
          className="gap-2"
        >
          {action.loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-muted border-t-foreground" />
          ) : (
            action.icon && <action.icon className="h-4 w-4" />
          )}
          {action.label}
        </Button>
      ))}
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

export const PageHeader: React.FC<PageHeaderProps> = ({
  className,
  variant,
  size,
  title,
  subtitle,
  description,
  breadcrumbs = [],
  actions = [],
  badge,
  icon: Icon,
  showBackButton = false,
  onBack,
  ...props
}) => {
  return (
    <div
      className={cn(pageHeaderVariants({ variant, size }), className)}
      {...props}
    >
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <Breadcrumbs items={breadcrumbs} />
      )}

      {/* Main Header Content */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        {/* Left Side - Title and Description */}
        <div className="space-y-4 flex-1">
          {/* Title Row */}
          <div className="flex items-center gap-4">
            {/* Back Button */}
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="p-2"
              >
                <ChevronRight className="h-4 w-4 rotate-180" />
              </Button>
            )}

            {/* Icon */}
            {Icon && (
              <div className="p-3 rounded-lg bg-primary/10 text-primary">
                <Icon className="h-6 w-6" />
              </div>
            )}

            {/* Title and Badge */}
            <div className="flex items-center gap-3">
              <h1 className={cn(
                "font-bold text-foreground",
                size === 'sm' ? 'text-2xl' : size === 'lg' ? 'text-4xl' : 'text-3xl'
              )}>
                {title}
              </h1>
              
              {badge && (
                <Badge variant={badge.variant} className="text-xs">
                  {badge.text}
                </Badge>
              )}
            </div>
          </div>

          {/* Subtitle */}
          {subtitle && (
            <p className={cn(
              "text-muted-foreground",
              size === 'sm' ? 'text-base' : size === 'lg' ? 'text-xl' : 'text-lg'
            )}>
              {subtitle}
            </p>
          )}

          {/* Description */}
          {description && (
            <p className="text-muted-foreground max-w-3xl leading-relaxed">
              {description}
            </p>
          )}
        </div>

        {/* Right Side - Actions */}
        {actions.length > 0 && (
          <div className="flex-shrink-0">
            <ActionButtons actions={actions} />
          </div>
        )}
      </div>

      {/* Gradient Accent Line */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
    </div>
  );
};

// ============================================================================
// Specialized Page Headers
// ============================================================================

export interface DashboardHeaderProps extends Omit<PageHeaderProps, 'title' | 'icon'> {
  user?: {
    fullName?: string;
    email?: string;
  };
  timeRange?: string;
  onTimeRangeChange?: (range: string) => void;
  onRefresh?: () => void;
  refreshing?: boolean;
  /** Mobile-optimized variant with better touch targets */
  mobileOptimized?: boolean;
  /** Enable haptic feedback on mobile devices */
  hapticFeedback?: boolean;
  /** Show quick stats toggle on mobile */
  showQuickStats?: boolean;
  /** Quick stats visibility state */
  quickStatsVisible?: boolean;
  /** Toggle quick stats callback */
  onToggleQuickStats?: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  user,
  timeRange = '7d',
  onTimeRangeChange,
  onRefresh,
  refreshing = false,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
  showQuickStats = false,
  quickStatsVisible = false,
  onToggleQuickStats,
  ...props
}) => {
  const [isMobile, setIsMobile] = React.useState(false);
  
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const isOptimizedForMobile = mobileOptimized || isMobile;

  const handleRefresh = () => {
    if (enableHaptic) {
      // Dynamic import for haptic feedback
      import('@/components/ui/mobile-interactions').then(({ hapticFeedback }) => {
        hapticFeedback.light();
      });
    }
    onRefresh?.();
  };

  const handleTimeRangeChange = (range: string) => {
    if (enableHaptic) {
      import('@/components/ui/mobile-interactions').then(({ hapticFeedback }) => {
        hapticFeedback.light();
      });
    }
    onTimeRangeChange?.(range);
  };

  const handleToggleQuickStats = () => {
    if (enableHaptic) {
      import('@/components/ui/mobile-interactions').then(({ hapticFeedback }) => {
        hapticFeedback.light();
      });
    }
    onToggleQuickStats?.();
  };

  const timeRangeOptions = [
    { value: '1d', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
  ];

  const timeRangeActions: ActionItem[] = [
    ...(onRefresh ? [{
      label: 'Refresh',
      icon: RefreshCw,
      variant: 'outline' as const,
      loading: refreshing,
      onClick: handleRefresh,
    }] : []),
  ];

  return (
    <div className="space-y-4">
      {/* Mobile-First Header */}
      <div className={cn(
        "flex items-center justify-between",
        isOptimizedForMobile && "flex-col gap-3 items-start"
      )}>
        {/* Title Section */}
        <div className="flex-1 min-w-0">
          <h1 className={cn(
            "font-bold tracking-tight text-foreground",
            isOptimizedForMobile ? "text-2xl" : "text-3xl"
          )}>
            Analytics Dashboard
          </h1>
          <p className={cn(
            "text-muted-foreground mt-1",
            isOptimizedForMobile ? "text-sm" : "text-base"
          )}>
            Welcome back, <span className="font-medium text-foreground">{user?.businessName || user?.email || 'User'}</span>
          </p>
        </div>

        {/* Desktop Controls */}
        {!isOptimizedForMobile && (
          <div className="flex items-center gap-3">
            {/* Time Range Selector */}
            {onTimeRangeChange && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <select 
                  value={timeRange} 
                  onChange={(e) => handleTimeRangeChange(e.target.value)}
                  className="bg-background border border-border rounded-lg px-3 py-2 text-sm font-medium focus:ring-2 focus:ring-primary focus:border-primary transition-colors min-w-[140px]"
                >
                  {timeRangeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {/* Refresh Button */}
            {onRefresh && (
              <Button 
                variant="outline" 
                onClick={handleRefresh}
                disabled={refreshing}
                className="gap-2"
              >
                <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
                Refresh
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Mobile Controls */}
      {isOptimizedForMobile && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            {/* Time Range Selector */}
            {onTimeRangeChange && (
              <div className="flex items-center gap-2 flex-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <select 
                  value={timeRange} 
                  onChange={(e) => handleTimeRangeChange(e.target.value)}
                  className={cn(
                    "w-full bg-background border border-border rounded-lg px-3 py-2 text-sm font-medium focus:ring-2 focus:ring-primary focus:border-primary transition-colors",
                    "min-h-[44px] touch-manipulation"
                  )}
                >
                  {timeRangeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {/* Refresh Button */}
            {onRefresh && (
              <Button 
                variant="outline" 
                onClick={handleRefresh}
                disabled={refreshing}
                className={cn(
                  "gap-2 flex-shrink-0",
                  "min-h-[44px] min-w-[44px] touch-manipulation"
                )}
              >
                <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
                <span className="hidden sm:inline">Refresh</span>
              </Button>
            )}
          </div>

          {/* Quick Stats Toggle */}
          {showQuickStats && onToggleQuickStats && (
            <button
              onClick={handleToggleQuickStats}
              className={cn(
                "flex items-center justify-center gap-2 w-full py-2 text-sm text-muted-foreground hover:text-foreground transition-colors",
                "min-h-[44px] touch-manipulation",
                "border border-border rounded-lg bg-background/50 hover:bg-background"
              )}
            >
              {quickStatsVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {quickStatsVisible ? 'Hide' : 'Show'} Quick Stats
              {quickStatsVisible ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

// ============================================================================
// Mobile-First Analytics Header
// ============================================================================

export interface MobileAnalyticsHeaderProps {
  user?: {
    fullName?: string;
    email?: string;
  };
  timeRange?: string;
  onTimeRangeChange?: (range: string) => void;
  onRefresh?: () => void;
  refreshing?: boolean;
  showQuickStats?: boolean;
  quickStatsVisible?: boolean;
  onToggleQuickStats?: () => void;
  metrics?: {
    totalConnections: number;
    totalConversations: number;
    responseRate: number;
    totalStores: number;
  };
  className?: string;
}

export const MobileAnalyticsHeader: React.FC<MobileAnalyticsHeaderProps> = ({
  user,
  timeRange = '7d',
  onTimeRangeChange,
  onRefresh,
  refreshing = false,
  showQuickStats = true,
  quickStatsVisible = false,
  onToggleQuickStats,
  metrics,
  className,
}) => {
  const [isMobile, setIsMobile] = React.useState(false);
  
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const timeRangeOptions = [
    { value: '1d', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
  ];

  return (
    <div className={cn("space-y-4", className)}>
      {/* Primary Header */}
      <div className={cn(
        "flex items-center justify-between",
        isMobile && "flex-col gap-3 items-start"
      )}>
        <div className="flex-1 min-w-0">
          <h1 className={cn(
            "font-bold tracking-tight text-foreground",
            isMobile ? "text-2xl" : "text-3xl"
          )}>
            Analytics Dashboard
          </h1>
          <p className={cn(
            "text-muted-foreground mt-1",
            isMobile ? "text-sm" : "text-base"
          )}>
            Welcome back, <span className="font-medium text-foreground">{user?.businessName || user?.email || 'User'}</span>
          </p>
        </div>

        {/* Desktop Controls */}
        {!isMobile && (
          <div className="flex items-center gap-3">
            {onTimeRangeChange && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <select 
                  value={timeRange} 
                  onChange={(e) => onTimeRangeChange(e.target.value)}
                  className="bg-background border border-border rounded-lg px-3 py-2 text-sm font-medium focus:ring-2 focus:ring-primary focus:border-primary transition-colors min-w-[140px]"
                >
                  {timeRangeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {onRefresh && (
              <Button 
                variant="outline" 
                onClick={onRefresh}
                disabled={refreshing}
                className="gap-2"
              >
                <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
                Refresh
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Mobile Controls */}
      {isMobile && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            {onTimeRangeChange && (
              <div className="flex items-center gap-2 flex-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <select 
                  value={timeRange} 
                  onChange={(e) => onTimeRangeChange(e.target.value)}
                  className="w-full bg-background border border-border rounded-lg px-3 py-2 text-sm font-medium focus:ring-2 focus:ring-primary focus:border-primary transition-colors min-h-[44px] touch-manipulation"
                >
                  {timeRangeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {onRefresh && (
              <Button 
                variant="outline" 
                onClick={onRefresh}
                disabled={refreshing}
                className="gap-2 flex-shrink-0 min-h-[44px] min-w-[44px] touch-manipulation"
              >
                <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
              </Button>
            )}
          </div>

          {/* Quick Stats Toggle */}
          {showQuickStats && onToggleQuickStats && (
            <button
              onClick={onToggleQuickStats}
              className="flex items-center justify-center gap-2 w-full py-2 text-sm text-muted-foreground hover:text-foreground transition-colors min-h-[44px] touch-manipulation border border-border rounded-lg bg-background/50 hover:bg-background"
            >
              {quickStatsVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {quickStatsVisible ? 'Hide' : 'Show'} Quick Stats
              {quickStatsVisible ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </button>
          )}
        </div>
      )}

      {/* Quick Stats Bar - Mobile Only */}
      {isMobile && quickStatsVisible && metrics && (
        <div className="grid grid-cols-2 gap-3">
          <div className="p-3 bg-background border border-border rounded-lg text-center">
            <div className="text-2xl font-bold text-primary">{metrics.totalConnections}</div>
            <div className="text-xs text-muted-foreground">Connections</div>
          </div>
          
          <div className="p-3 bg-background border border-border rounded-lg text-center">
            <div className="text-2xl font-bold text-success">{metrics.totalConversations}</div>
            <div className="text-xs text-muted-foreground">Conversations</div>
          </div>
          
          <div className="p-3 bg-background border border-border rounded-lg text-center">
            <div className="text-2xl font-bold text-warning">{metrics.responseRate}%</div>
            <div className="text-xs text-muted-foreground">Response Rate</div>
          </div>
          
          <div className="p-3 bg-background border border-border rounded-lg text-center">
            <div className="text-2xl font-bold text-secondary">{metrics.totalStores}</div>
            <div className="text-xs text-muted-foreground">Stores</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PageHeader; 