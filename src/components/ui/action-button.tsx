// @ts-nocheck
// ActionButton Component
// Enterprise-grade action buttons with animations and states

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { LucideIcon, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const actionButtonVariants = cva(
  "relative group overflow-hidden transition-all duration-300 cursor-pointer",
  {
    variants: {
      variant: {
        default: "bg-card hover:bg-card/80 border-border hover:border-primary/20",
        primary: "bg-gradient-to-br from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 border-primary/20 hover:border-primary/40",
        secondary: "bg-gradient-to-br from-secondary/10 to-secondary/5 hover:from-secondary/20 hover:to-secondary/10 border-secondary/20 hover:border-secondary/40",
        success: "bg-gradient-to-br from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 border-green-200 hover:border-green-300",
        warning: "bg-gradient-to-br from-amber-50 to-yellow-50 hover:from-amber-100 hover:to-yellow-100 border-amber-200 hover:border-amber-300",
        danger: "bg-gradient-to-br from-red-50 to-rose-50 hover:from-red-100 hover:to-rose-100 border-red-200 hover:border-red-300",
        glass: "bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
      },
      glow: {
        none: "",
        subtle: "hover:shadow-lg",
        primary: "hover:glow-primary",
        secondary: "hover:glow-secondary",
        success: "hover:shadow-green-500/20",
        warning: "hover:shadow-amber-500/20",
        danger: "hover:shadow-red-500/20",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      glow: "subtle",
    },
  }
);

const iconContainerVariants = cva(
  "flex items-center justify-center rounded-lg transition-all duration-300 group-hover:scale-110",
  {
    variants: {
      variant: {
        default: "bg-primary/10 text-primary group-hover:bg-primary/20",
        primary: "bg-primary/20 text-primary group-hover:bg-primary/30",
        secondary: "bg-secondary/20 text-secondary-foreground group-hover:bg-secondary/30",
        success: "bg-green-100 text-green-600 group-hover:bg-green-200",
        warning: "bg-amber-100 text-amber-600 group-hover:bg-amber-200",
        danger: "bg-red-100 text-red-600 group-hover:bg-red-200",
        glass: "bg-white/20 text-white group-hover:bg-white/30",
      },
      size: {
        sm: "h-8 w-8",
        default: "h-12 w-12",
        lg: "h-16 w-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// ============================================================================
// Component Types
// ============================================================================

export interface ActionButtonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof actionButtonVariants> {
  title: string;
  description?: string;
  icon?: LucideIcon;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  loading?: boolean;
  disabled?: boolean;
  animated?: boolean;
  shortcut?: string;
  onAction?: () => void | Promise<void>;
}

// ============================================================================
// Helper Components
// ============================================================================

const LoadingSpinner: React.FC<{ size?: 'sm' | 'default' | 'lg' }> = ({ size = 'default' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  return (
    <Loader2 className={cn('animate-spin text-muted-foreground', sizeClasses[size])} />
  );
};

const ShortcutBadge: React.FC<{ shortcut: string }> = ({ shortcut }) => (
  <Badge 
    variant="outline" 
    className="absolute top-2 right-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-background/80 backdrop-blur-sm"
  >
    {shortcut}
  </Badge>
);

// ============================================================================
// Main Component
// ============================================================================

export const ActionButton: React.FC<ActionButtonProps> = ({
  className,
  variant,
  size,
  glow,
  title,
  description,
  icon: Icon,
  badge,
  loading = false,
  disabled = false,
  animated = true,
  shortcut,
  onAction,
  ...props
}) => {
  const [isPressed, setIsPressed] = React.useState(false);

  const handleClick = async () => {
    if (disabled || loading) return;
    
    setIsPressed(true);
    try {
      await onAction?.();
    } finally {
      setTimeout(() => setIsPressed(false), 150);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <Card
      className={cn(
        actionButtonVariants({ variant, size, glow }),
        animated && "hover:scale-[1.02] active:scale-[0.98]",
        isPressed && "scale-[0.95]",
        disabled && "opacity-50 cursor-not-allowed",
        loading && "cursor-wait",
        "focus-ring",
        className
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-disabled={disabled}
      aria-label={description || title}
      {...props}
    >
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      {/* Ripple Effect */}
      {animated && (
        <div className="absolute inset-0 overflow-hidden rounded-lg">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
        </div>
      )}

      {/* Content */}
      <div className="relative flex flex-col items-center justify-center space-y-3 text-center">
        {/* Icon Container */}
        <div className={cn(iconContainerVariants({ variant, size }))}>
          {loading ? (
            <LoadingSpinner size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default'} />
          ) : Icon ? (
            <Icon className={cn(
              size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-8 w-8' : 'h-6 w-6'
            )} />
          ) : (
            <div className={cn(
              'rounded-full bg-muted',
              size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-8 w-8' : 'h-6 w-6'
            )} />
          )}
        </div>

        {/* Text Content */}
        <div className="space-y-1">
          <h3 className={cn(
            'font-semibold text-foreground group-hover:text-primary transition-colors duration-300',
            size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'
          )}>
            {title}
          </h3>
          
          {description && (
            <p className={cn(
              'text-muted-foreground transition-colors duration-300',
              size === 'sm' ? 'text-xs' : 'text-sm'
            )}>
              {description}
            </p>
          )}
        </div>

        {/* Badge */}
        {badge && (
          <Badge variant={badge.variant} className="text-xs">
            {badge.text}
          </Badge>
        )}
      </div>

      {/* Shortcut Badge */}
      {shortcut && <ShortcutBadge shortcut={shortcut} />}

      {/* Hover Border Accent */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-secondary to-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </Card>
  );
};

// ============================================================================
// Action Grid Component
// ============================================================================

export interface ActionGridProps extends React.HTMLAttributes<HTMLDivElement> {
  actions: ActionButtonProps[];
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'default' | 'lg';
  loading?: boolean;
}

export const ActionGrid: React.FC<ActionGridProps> = ({
  className,
  actions = [],
  columns = 2,
  gap = 'default',
  loading = false,
  ...props
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
  };

  const gapClasses = {
    sm: 'gap-3',
    default: 'gap-4',
    lg: 'gap-6',
  };

  // Early return if no actions provided
  if (!actions || !Array.isArray(actions) || actions.length === 0) {
    return (
      <div className={cn('text-center text-muted-foreground py-8', className)}>
        No actions available
      </div>
    );
  }

  return (
    <div
      className={cn(
        'grid',
        gridCols[columns],
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {actions.map((action, index) => (
        <ActionButton
          key={action.title || index}
          loading={loading}
          {...action}
        />
      ))}
    </div>
  );
};

// ============================================================================
// Quick Action Card (Specialized)
// ============================================================================

export interface QuickActionCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  actions: ActionButtonProps[];
  columns?: ActionGridProps['columns'];
  loading?: boolean;
}

export const QuickActionCard: React.FC<QuickActionCardProps> = ({
  className,
  title = "Quick Actions",
  subtitle = "Common tasks and shortcuts",
  actions = [],
  columns = 2,
  loading = false,
  ...props
}) => {
  return (
    <Card className={cn("p-6", className)} {...props}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="font-semibold text-foreground">{title}</h3>
        {subtitle && (
          <p className="text-sm text-muted-foreground mt-1">{subtitle}</p>
        )}
      </div>

      {/* Actions Grid */}
      <ActionGrid
        actions={actions}
        columns={columns}
        loading={loading}
      />
    </Card>
  );
};

export default ActionButton; 