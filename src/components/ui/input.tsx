// @ts-nocheck
import React from 'react';
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const inputVariants = cva(
  "flex w-full rounded-xl border bg-white/70 backdrop-blur-sm text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 touch-manipulation transition-all duration-200 shadow-sm hover:shadow-md focus-visible:shadow-lg",
  {
    variants: {
      size: {
        sm: "h-9 px-3 py-2 text-xs min-h-[36px]",
        default: "h-12 px-4 py-3 text-sm min-h-[48px]", // Enhanced height and padding
        lg: "h-14 px-5 py-4 text-base min-h-[52px]",
      },
      variant: {
        default: "border-slate-200/60 bg-white/70 hover:border-slate-300/80 focus-visible:ring-emerald-500/30 focus-visible:border-emerald-400",
        error: "border-red-300/80 bg-red-50/50 focus-visible:ring-red-500/30 focus-visible:border-red-400",
        success: "border-green-300/80 bg-green-50/50 focus-visible:ring-green-500/30 focus-visible:border-green-400",
        ghost: "border-transparent bg-transparent shadow-none hover:shadow-none focus-visible:shadow-none",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
      fullWidth: true,
    },
  }
)

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string
  description?: string
  error?: string
  success?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  loading?: boolean
  clearable?: boolean
  onClear?: () => void
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type = "text", 
    size, 
    variant, 
    fullWidth,
    label,
    description,
    error,
    success,
    leftIcon,
    rightIcon,
    loading,
    clearable,
    onClear,
    value,
    disabled,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false)
    
    const finalVariant = error ? "error" : success ? "success" : variant
    const showClearButton = clearable && value && !disabled && !loading

    const handleClear = () => {
      if (onClear) {
        onClear()
      }
      // Create a synthetic event to trigger onChange with empty value
      if (props.onChange) {
        const syntheticEvent = {
          target: { value: '' },
          currentTarget: { value: '' },
        } as React.ChangeEvent<HTMLInputElement>
        props.onChange(syntheticEvent)
      }
    }

    return (
      <div className={cn("space-y-3", !fullWidth && "inline-block")}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={props.id}
            className={cn(
              "block text-sm font-semibold leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 tracking-tight",
              error ? "text-red-700" : success ? "text-green-700" : "text-slate-700"
            )}
          >
            {label}
          </label>
        )}
        
        {/* Input Container */}
        <div className="relative group">
          {/* Left Icon */}
          {leftIcon && (
            <div className={cn(
              "absolute left-4 top-1/2 transform -translate-y-1/2 transition-colors duration-200",
              isFocused ? "text-emerald-500" : "text-slate-400",
              error && "text-red-500",
              success && "text-green-500"
            )}>
              {leftIcon}
            </div>
          )}
          
          {/* Input */}
          <input
            type={type}
            className={cn(
              inputVariants({ size, variant: finalVariant, fullWidth }),
              leftIcon && "pl-12",
              (rightIcon || loading || showClearButton) && "pr-12",
              "font-medium text-slate-700 placeholder:font-normal",
              className
            )}
            ref={ref}
            disabled={disabled || loading}
            value={value}
            onFocus={(e) => {
              setIsFocused(true)
              props.onFocus?.(e)
            }}
            onBlur={(e) => {
              setIsFocused(false)
              props.onBlur?.(e)
            }}
            {...props}
          />
          
          {/* Right Content */}
          {(rightIcon || loading || showClearButton) && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
              {/* Loading Spinner */}
              {loading && (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-transparent border-t-emerald-500" />
              )}
              
              {/* Clear Button */}
              {showClearButton && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="h-5 w-5 rounded-full hover:bg-slate-100 flex items-center justify-center text-slate-400 hover:text-slate-600 transition-all duration-200"
                  aria-label="Clear input"
                >
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 13A6 6 0 1 0 7 1a6 6 0 0 0 0 12ZM5 5l4 4m0-4l-4 4"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              )}
              
              {/* Right Icon */}
              {rightIcon && !loading && (
                <div className={cn(
                  "transition-colors duration-200",
                  isFocused ? "text-emerald-500" : "text-slate-400",
                  error && "text-red-500",
                  success && "text-green-500"
                )}>
                  {rightIcon}
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Description */}
        {description && !error && !success && (
          <p className="text-xs text-slate-500 leading-relaxed">
            {description}
          </p>
        )}
        
        {/* Error Message */}
        {error && (
          <p className="text-xs text-red-600 flex items-center gap-2 font-medium">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="flex-shrink-0"
            >
              <path
                d="M7 13A6 6 0 1 0 7 1a6 6 0 0 0 0 12ZM7 4v4M7 9.5h.01"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {error}
          </p>
        )}
        
        {/* Success Message */}
        {success && (
          <p className="text-xs text-green-600 flex items-center gap-2 font-medium">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="flex-shrink-0"
            >
              <path
                d="M7 13A6 6 0 1 0 7 1a6 6 0 0 0 0 12ZM9.5 5.5L6.5 8.5 4.5 6.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {success}
          </p>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

// ============================================================================
// Mobile-First Textarea Component
// ============================================================================

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof inputVariants> {
  label?: string
  description?: string
  error?: string
  success?: string
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
  autoResize?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    className, 
    size, 
    variant, 
    fullWidth,
    label,
    description,
    error,
    success,
    resize = 'vertical',
    autoResize = false,
    disabled,
    ...props 
  }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    const finalVariant = error ? "error" : success ? "success" : variant

    React.useImperativeHandle(ref, () => textareaRef.current!)

    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
    }, [autoResize, props.value])

    const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      if (autoResize) {
        const textarea = e.currentTarget
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
      props.onInput?.(e)
    }

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize'
    }

    return (
      <div className={cn("space-y-2", !fullWidth && "inline-block")}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={props.id}
            className={cn(
              "block text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              error ? "text-destructive" : success ? "text-green-700" : "text-foreground"
            )}
          >
            {label}
          </label>
        )}
        
        {/* Textarea */}
        <textarea
          className={cn(
            inputVariants({ size, variant: finalVariant, fullWidth }),
            resizeClasses[resize],
            autoResize && 'overflow-hidden',
            "min-h-[80px]",
            className
          )}
          ref={textareaRef}
          disabled={disabled}
          onInput={handleInput}
          {...props}
        />
        
        {/* Description */}
        {description && !error && !success && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
        
        {/* Error Message */}
        {error && (
          <p className="text-xs text-destructive flex items-center gap-1">
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12ZM6 4v3M6 8.5h.01"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {error}
          </p>
        )}
        
        {/* Success Message */}
        {success && (
          <p className="text-xs text-green-700 flex items-center gap-1">
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12ZM8.5 4.5L5 8 3.5 6.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {success}
          </p>
        )}
      </div>
    )
  }
)
Textarea.displayName = "Textarea"

// ============================================================================
// Mobile-First Input Group
// ============================================================================

export interface InputGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string
  description?: string
  error?: string
  success?: string
  orientation?: 'horizontal' | 'vertical'
  children: React.ReactNode
}

const InputGroup = React.forwardRef<HTMLDivElement, InputGroupProps>(
  ({ className, label, description, error, success, orientation = 'horizontal', children, ...props }, ref) => {
    const orientationClasses = {
      horizontal: "flex gap-2",
      vertical: "space-y-2"
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <label className={cn(
            "block text-sm font-medium leading-none",
            error ? "text-destructive" : success ? "text-green-700" : "text-foreground"
          )}>
            {label}
          </label>
        )}
        
        {/* Input Group */}
        <div
          ref={ref}
          className={cn(
            orientationClasses[orientation],
            orientation === 'horizontal' && "[&>*:first-child]:rounded-r-none [&>*:last-child]:rounded-l-none [&>*:not(:first-child):not(:last-child)]:rounded-none",
            className
          )}
          {...props}
        >
          {children}
        </div>
        
        {/* Description */}
        {description && !error && !success && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
        
        {/* Error Message */}
        {error && (
          <p className="text-xs text-destructive flex items-center gap-1">
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12ZM6 4v3M6 8.5h.01"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {error}
          </p>
        )}
        
        {/* Success Message */}
        {success && (
          <p className="text-xs text-green-700 flex items-center gap-1">
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 12A6 6 0 1 0 6 0a6 6 0 0 0 0 12ZM8.5 4.5L5 8 3.5 6.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {success}
          </p>
        )}
      </div>
    )
  }
)
InputGroup.displayName = "InputGroup"

export { Input, Textarea, InputGroup, inputVariants } 