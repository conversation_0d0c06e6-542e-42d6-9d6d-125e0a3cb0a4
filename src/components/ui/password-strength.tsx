import React from 'react';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PasswordStrengthProps {
  password: string;
  validation: {
    isValid: boolean;
    score: number;
    feedback: string[];
  };
  className?: string;
}

export const PasswordStrength: React.FC<PasswordStrengthProps> = ({
  password,
  validation,
  className
}) => {
  const getStrengthText = (score: number): string => {
    if (score === 0) return 'Enter password';
    if (score <= 2) return 'Very weak';
    if (score <= 4) return 'Weak';
    if (score <= 6) return 'Good';
    if (score <= 7) return 'Strong';
    return 'Very strong';
  };

  const getStrengthColor = (score: number): string => {
    if (score === 0) return 'bg-slate-200';
    if (score <= 2) return 'bg-red-500';
    if (score <= 4) return 'bg-orange-500';
    if (score <= 6) return 'bg-yellow-500';
    if (score <= 7) return 'bg-blue-500';
    return 'bg-emerald-500';
  };

  const getTextColor = (score: number): string => {
    if (score === 0) return 'text-slate-500';
    if (score <= 2) return 'text-red-600';
    if (score <= 4) return 'text-orange-600';
    if (score <= 6) return 'text-yellow-600';
    if (score <= 7) return 'text-blue-600';
    return 'text-emerald-600';
  };

  const requirements = [
    { text: 'At least 8 characters', test: (pwd: string) => pwd.length >= 8 },
    { text: 'Contains lowercase letter', test: (pwd: string) => /[a-z]/.test(pwd) },
    { text: 'Contains uppercase letter', test: (pwd: string) => /[A-Z]/.test(pwd) },
    { text: 'Contains number', test: (pwd: string) => /\d/.test(pwd) },
    { text: 'Contains special character', test: (pwd: string) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd) },
  ];

  if (!password) {
    return (
      <div className={cn('space-y-3', className)}>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-slate-600">Password strength</span>
            <span className="text-sm text-slate-500">Enter password</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div className="h-2 rounded-full bg-slate-200 transition-all duration-300" />
          </div>
        </div>
        
        <div className="space-y-2">
          <p className="text-sm font-medium text-slate-600">Requirements:</p>
          <ul className="space-y-1">
            {requirements.map((req, index) => (
              <li key={index} className="flex items-center space-x-2 text-sm text-slate-500">
                <div className="w-4 h-4 rounded-full border border-slate-300 flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-slate-300" />
                </div>
                <span>{req.text}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-slate-600">Password strength</span>
          <span className={cn('text-sm font-medium', getTextColor(validation.score))}>
            {getStrengthText(validation.score)}
          </span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-2">
          <div 
            className={cn(
              'h-2 rounded-full transition-all duration-300',
              getStrengthColor(validation.score)
            )}
            style={{ width: `${(validation.score / 8) * 100}%` }}
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <p className="text-sm font-medium text-slate-600">Requirements:</p>
        <ul className="space-y-1">
          {requirements.map((req, index) => {
            const isValid = req.test(password);
            return (
              <li key={index} className="flex items-center space-x-2 text-sm">
                <div className={cn(
                  'w-4 h-4 rounded-full flex items-center justify-center transition-colors',
                  isValid ? 'bg-emerald-100 text-emerald-600' : 'bg-slate-100 text-slate-400'
                )}>
                  {isValid ? (
                    <Check className="w-3 h-3" />
                  ) : (
                    <X className="w-3 h-3" />
                  )}
                </div>
                <span className={cn(
                  'transition-colors',
                  isValid ? 'text-emerald-600' : 'text-slate-500'
                )}>
                  {req.text}
                </span>
              </li>
            );
          })}
        </ul>
      </div>
      
      {validation.feedback.length > 0 && (
        <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <p className="text-sm font-medium text-amber-800 mb-1">Suggestions:</p>
          <ul className="space-y-1">
            {validation.feedback.map((feedback, index) => (
              <li key={index} className="text-sm text-amber-700">
                • {feedback}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}; 