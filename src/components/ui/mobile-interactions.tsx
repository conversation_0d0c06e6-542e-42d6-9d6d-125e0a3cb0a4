// @ts-nocheck
import React from 'react';
import { cn } from "@/lib/utils"

// Haptic feedback utilities
export const hapticFeedback = {
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(1)
    }
  },
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(5)
    }
  },
  heavy: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
  },
  success: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([100, 50, 100])
    }
  },
  error: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([200, 100, 200])
    }
  },
  notification: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 50, 50])
    }
  },
  selection: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(3)
    }
  },
}

// Touch gesture detection
export interface TouchGestureCallbacks {
  onSwipeLeft?: (event: TouchEvent) => void
  onSwipeRight?: (event: TouchEvent) => void
  onSwipeUp?: (event: TouchEvent) => void
  onSwipeDown?: (event: TouchEvent) => void
  onTap?: (event: TouchEvent) => void
  onDoubleTap?: (event: TouchEvent) => void
  onLongPress?: (event: TouchEvent) => void
  onPinch?: (event: TouchEvent, scale: number) => void
}

export const useTouchGestures = (
  callbacks: TouchGestureCallbacks,
  options: {
    swipeThreshold?: number
    longPressDelay?: number
    doubleTapDelay?: number
    pinchThreshold?: number
  } = {}
) => {
  const {
    swipeThreshold = 50,
    longPressDelay = 500,
    doubleTapDelay = 300,
    pinchThreshold = 0.1,
  } = options

  const touchStart = React.useRef({ x: 0, y: 0, time: 0 })
  const touchEnd = React.useRef({ x: 0, y: 0, time: 0 })
  const lastTap = React.useRef(0)
  const longPressTimer = React.useRef<NodeJS.Timeout>()
  const lastTouchDistance = React.useRef(0)

  const handleTouchStart = React.useCallback((event: React.TouchEvent) => {
    const touch = event.touches[0]
    touchStart.current = { x: touch.clientX, y: touch.clientY, time: Date.now() }
    
    // Handle pinch
    if (event.touches.length === 2) {
      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      lastTouchDistance.current = distance
    }

    // Set up long press timer
    if (callbacks.onLongPress) {
      longPressTimer.current = setTimeout(() => {
        callbacks.onLongPress!(event.nativeEvent)
        hapticFeedback.heavy()
      }, longPressDelay)
    }
  }, [callbacks, longPressDelay])

  const handleTouchMove = React.useCallback((event: React.TouchEvent) => {
    // Cancel long press if user moves finger
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = undefined
    }

    // Handle pinch
    if (event.touches.length === 2 && callbacks.onPinch) {
      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      
      const scale = distance / lastTouchDistance.current
      if (Math.abs(scale - 1) > pinchThreshold) {
        callbacks.onPinch(event.nativeEvent, scale)
        lastTouchDistance.current = distance
      }
    }
  }, [callbacks, pinchThreshold])

  const handleTouchEnd = React.useCallback((event: React.TouchEvent) => {
    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = undefined
    }

    const touch = event.changedTouches[0]
    touchEnd.current = { x: touch.clientX, y: touch.clientY, time: Date.now() }

    const deltaX = touchEnd.current.x - touchStart.current.x
    const deltaY = touchEnd.current.y - touchStart.current.y
    const deltaTime = touchEnd.current.time - touchStart.current.time

    // Handle swipe gestures
    if (Math.abs(deltaX) > swipeThreshold || Math.abs(deltaY) > swipeThreshold) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > 0) {
          callbacks.onSwipeRight?.(event.nativeEvent)
        } else {
          callbacks.onSwipeLeft?.(event.nativeEvent)
        }
      } else {
        // Vertical swipe
        if (deltaY > 0) {
          callbacks.onSwipeDown?.(event.nativeEvent)
        } else {
          callbacks.onSwipeUp?.(event.nativeEvent)
        }
      }
      hapticFeedback.light()
      return
    }

    // Handle tap and double tap
    if (deltaTime < 300 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
      const now = Date.now()
      
      if (now - lastTap.current < doubleTapDelay) {
        callbacks.onDoubleTap?.(event.nativeEvent)
        hapticFeedback.medium()
        lastTap.current = 0
      } else {
        lastTap.current = now
        setTimeout(() => {
          if (lastTap.current === now) {
            callbacks.onTap?.(event.nativeEvent)
            hapticFeedback.light()
          }
        }, doubleTapDelay)
      }
    }
  }, [callbacks, swipeThreshold, doubleTapDelay])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
  }
}

// Mobile interaction wrapper component
export interface MobileInteractionProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Enable swipe gestures */
  enableSwipe?: boolean
  /** Enable tap gestures */
  enableTap?: boolean
  /** Enable long press */
  enableLongPress?: boolean
  /** Enable pinch gestures */
  enablePinch?: boolean
  /** Haptic feedback intensity */
  hapticIntensity?: 'light' | 'medium' | 'heavy'
  /** Touch gesture callbacks */
  gestures?: TouchGestureCallbacks
  /** Additional touch interaction props */
  touchProps?: {
    swipeThreshold?: number
    longPressDelay?: number
    doubleTapDelay?: number
    pinchThreshold?: number
  }
}

export const MobileInteraction = React.forwardRef<HTMLDivElement, MobileInteractionProps>(
  ({ 
    className,
    enableSwipe = false,
    enableTap = false,
    enableLongPress = false,
    enablePinch = false,
    hapticIntensity = 'light',
    gestures = {},
    touchProps = {},
    children,
    ...props
  }, ref) => {
    const touchGestureHandlers = useTouchGestures(gestures, touchProps)

    const handleClick = React.useCallback((event: React.MouseEvent) => {
      if (hapticIntensity) {
        hapticFeedback[hapticIntensity]()
      }
      props.onClick?.(event)
    }, [hapticIntensity, props.onClick])

    return (
      <div
        ref={ref}
        className={cn(
          "touch-manipulation select-none",
          (enableSwipe || enableTap || enableLongPress || enablePinch) && "cursor-pointer",
          className
        )}
        {...(enableSwipe || enableTap || enableLongPress || enablePinch ? touchGestureHandlers : {})}
        {...props}
        onClick={handleClick}
      >
        {children}
      </div>
    )
  }
)
MobileInteraction.displayName = "MobileInteraction"

// Swipeable card component
export interface SwipeableCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Callback when swiped left */
  onSwipeLeft?: () => void
  /** Callback when swiped right */
  onSwipeRight?: () => void
  /** Show visual feedback during swipe */
  showSwipeFeedback?: boolean
  /** Swipe threshold in pixels */
  swipeThreshold?: number
}

export const SwipeableCard = React.forwardRef<HTMLDivElement, SwipeableCardProps>(
  ({ 
    className,
    onSwipeLeft,
    onSwipeRight,
    showSwipeFeedback = true,
    swipeThreshold = 100,
    children,
    ...props
  }, ref) => {
    const [swipeOffset, setSwipeOffset] = React.useState(0)
    const [isSwipingLeft, setIsSwipingLeft] = React.useState(false)
    const [isSwipingRight, setIsSwipingRight] = React.useState(false)

    const gestures = React.useMemo(() => ({
      onSwipeLeft: () => {
        setIsSwipingLeft(true)
        setTimeout(() => {
          onSwipeLeft?.()
          setIsSwipingLeft(false)
          setSwipeOffset(0)
        }, 150)
      },
      onSwipeRight: () => {
        setIsSwipingRight(true)
        setTimeout(() => {
          onSwipeRight?.()
          setIsSwipingRight(false)
          setSwipeOffset(0)
        }, 150)
      },
    }), [onSwipeLeft, onSwipeRight])

    return (
      <MobileInteraction
        ref={ref}
        className={cn(
          "transition-transform duration-150 ease-out",
          isSwipingLeft && "translate-x-[-100%] opacity-0",
          isSwipingRight && "translate-x-[100%] opacity-0",
          showSwipeFeedback && "hover:scale-[1.02] active:scale-[0.98]",
          className
        )}
        enableSwipe
        gestures={gestures}
        touchProps={{ swipeThreshold }}
        {...props}
      >
        {children}
      </MobileInteraction>
    )
  }
)
SwipeableCard.displayName = "SwipeableCard"

// Pull-to-refresh component
export interface PullToRefreshProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Callback when pull to refresh is triggered */
  onRefresh?: () => Promise<void>
  /** Refresh threshold in pixels */
  refreshThreshold?: number
  /** Show refresh indicator */
  showRefreshIndicator?: boolean
  /** Loading state */
  isLoading?: boolean
}

export const PullToRefresh = React.forwardRef<HTMLDivElement, PullToRefreshProps>(
  ({ 
    className,
    onRefresh,
    refreshThreshold = 60,
    showRefreshIndicator = true,
    isLoading = false,
    children,
    ...props
  }, ref) => {
    const [pullOffset, setPullOffset] = React.useState(0)
    const [isRefreshing, setIsRefreshing] = React.useState(false)
    const startY = React.useRef(0)

    const handleTouchStart = React.useCallback((event: React.TouchEvent) => {
      startY.current = event.touches[0].clientY
    }, [])

    const handleTouchMove = React.useCallback((event: React.TouchEvent) => {
      const currentY = event.touches[0].clientY
      const diff = currentY - startY.current
      
      if (diff > 0 && window.scrollY === 0) {
        setPullOffset(Math.min(diff, refreshThreshold * 1.5))
      }
    }, [refreshThreshold])

    const handleTouchEnd = React.useCallback(async () => {
      if (pullOffset >= refreshThreshold && onRefresh && !isRefreshing) {
        setIsRefreshing(true)
        hapticFeedback.success()
        try {
          await onRefresh()
        } finally {
          setIsRefreshing(false)
        }
      }
      setPullOffset(0)
    }, [pullOffset, refreshThreshold, onRefresh, isRefreshing])

    return (
      <div
        ref={ref}
        className={cn("relative overflow-hidden", className)}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        {...props}
      >
        {showRefreshIndicator && (
          <div
            className="absolute top-0 left-0 right-0 flex justify-center items-center bg-gradient-to-b from-white to-transparent transition-transform duration-200"
            style={{
              transform: `translateY(${pullOffset - refreshThreshold}px)`,
              height: `${refreshThreshold}px`,
            }}
          >
            <div className={cn(
              "w-6 h-6 rounded-full border-2 border-emerald-600 border-t-transparent animate-spin",
              (isRefreshing || isLoading) ? "opacity-100" : "opacity-0"
            )} />
          </div>
        )}
        <div
          className="transition-transform duration-200"
          style={{ transform: `translateY(${Math.max(0, pullOffset)}px)` }}
        >
          {children}
        </div>
      </div>
    )
  }
)
PullToRefresh.displayName = "PullToRefresh"

// Long press menu component
export interface LongPressMenuProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Menu items */
  items: Array<{
    label: string
    icon?: React.ReactNode
    onClick: () => void
    variant?: 'default' | 'destructive'
  }>
  /** Long press delay in milliseconds */
  longPressDelay?: number
  /** Show menu */
  showMenu?: boolean
  /** Menu position */
  menuPosition?: { x: number; y: number }
}

export const LongPressMenu = React.forwardRef<HTMLDivElement, LongPressMenuProps>(
  ({ 
    className,
    items,
    longPressDelay = 500,
    showMenu = false,
    menuPosition = { x: 0, y: 0 },
    children,
    ...props
  }, ref) => {
    const [isMenuOpen, setIsMenuOpen] = React.useState(false)
    const [menuPos, setMenuPos] = React.useState({ x: 0, y: 0 })

    const handleLongPress = React.useCallback((event: TouchEvent) => {
      setMenuPos({ x: event.touches[0].clientX, y: event.touches[0].clientY })
      setIsMenuOpen(true)
      hapticFeedback.heavy()
    }, [])

    const handleMenuItemClick = React.useCallback((onClick: () => void) => {
      onClick()
      setIsMenuOpen(false)
      hapticFeedback.light()
    }, [])

    const gestures = React.useMemo(() => ({
      onLongPress: handleLongPress,
    }), [handleLongPress])

    return (
      <>
        <MobileInteraction
          ref={ref}
          className={cn("relative", className)}
          enableLongPress
          gestures={gestures}
          touchProps={{ longPressDelay }}
          {...props}
        >
          {children}
        </MobileInteraction>
        
        {isMenuOpen && (
          <>
            <div
              className="fixed inset-0 z-40 bg-black/20"
              onClick={() => setIsMenuOpen(false)}
            />
            <div
              className="fixed z-50 min-w-[200px] rounded-lg bg-white shadow-xl border p-2"
              style={{
                left: `${menuPos.x}px`,
                top: `${menuPos.y}px`,
                transform: 'translate(-50%, -100%)',
              }}
            >
              {items.map((item, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors min-h-[44px]",
                    item.variant === 'destructive' 
                      ? "hover:bg-red-50 text-red-600" 
                      : "hover:bg-gray-50 text-gray-900"
                  )}
                  onClick={() => handleMenuItemClick(item.onClick)}
                >
                  {item.icon && <span className="w-5 h-5">{item.icon}</span>}
                  <span className="text-sm font-medium">{item.label}</span>
                </button>
              ))}
            </div>
          </>
        )}
      </>
    )
  }
)
LongPressMenu.displayName = "LongPressMenu"

// Touch feedback button
export interface TouchFeedbackButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Haptic feedback intensity */
  hapticIntensity?: 'light' | 'medium' | 'heavy'
  /** Show ripple effect */
  showRipple?: boolean
  /** Loading state */
  isLoading?: boolean
}

export const TouchFeedbackButton = React.forwardRef<HTMLButtonElement, TouchFeedbackButtonProps>(
  ({ 
    className,
    hapticIntensity = 'light',
    showRipple = true,
    isLoading = false,
    onClick,
    children,
    ...props
  }, ref) => {
    const [ripples, setRipples] = React.useState<Array<{ x: number; y: number; id: number }>>([])

    const handleClick = React.useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      if (hapticIntensity) {
        hapticFeedback[hapticIntensity]()
      }

      if (showRipple) {
        const rect = event.currentTarget.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top
        const id = Date.now()
        
        setRipples(prev => [...prev, { x, y, id }])
        setTimeout(() => {
          setRipples(prev => prev.filter(ripple => ripple.id !== id))
        }, 600)
      }

      onClick?.(event)
    }, [hapticIntensity, showRipple, onClick])

    return (
      <button
        ref={ref}
        className={cn(
          "relative overflow-hidden touch-manipulation min-h-[44px] min-w-[44px]",
          "transition-all duration-150 ease-out",
          "active:scale-95 active:brightness-90",
          className
        )}
        onClick={handleClick}
        disabled={isLoading}
        {...props}
      >
        {children}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        {showRipple && ripples.map(ripple => (
          <span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full animate-ping"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
          />
        ))}
      </button>
    )
  }
)
TouchFeedbackButton.displayName = "TouchFeedbackButton"