// MetricCard Component
// Enterprise-grade metric display with animations and theming

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const metricCardVariants = cva(
  "relative overflow-hidden transition-all duration-300 hover:shadow-lg group",
  {
    variants: {
      variant: {
        default: "bg-card border-border hover:border-primary/20",
        primary: "bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 hover:border-primary/40",
        secondary: "bg-gradient-to-br from-secondary/5 to-secondary/10 border-secondary/20 hover:border-secondary/40",
        success: "bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 hover:border-green-300",
        warning: "bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200 hover:border-amber-300",
        danger: "bg-gradient-to-br from-red-50 to-rose-50 border-red-200 hover:border-red-300",
        glass: "bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
      },
      glow: {
        none: "",
        subtle: "hover:glow-primary/20",
        primary: "glow-primary",
        secondary: "glow-secondary",
        success: "hover:shadow-green-500/20",
        warning: "hover:shadow-amber-500/20",
        danger: "hover:shadow-red-500/20",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      glow: "none",
    },
  }
);

const metricValueVariants = cva(
  "font-bold transition-colors duration-300",
  {
    variants: {
      size: {
        sm: "text-lg",
        default: "text-2xl",
        lg: "text-3xl",
        xl: "text-4xl",
      },
      color: {
        default: "text-foreground",
        primary: "text-primary",
        secondary: "text-secondary-foreground",
        success: "text-green-600",
        warning: "text-amber-600",
        danger: "text-red-600",
        gradient: "text-gradient-primary",
      },
    },
    defaultVariants: {
      size: "default",
      color: "default",
    },
  }
);

const trendVariants = cva(
  "inline-flex items-center gap-1 text-xs font-medium transition-colors duration-300",
  {
    variants: {
      trend: {
        up: "text-green-600",
        down: "text-red-600",
        neutral: "text-muted-foreground",
      },
    },
  }
);

// ============================================================================
// Component Types
// ============================================================================

export interface MetricCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof metricCardVariants> {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  trend?: {
    value: number;
    label?: string;
    direction: 'up' | 'down' | 'neutral';
  };
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  loading?: boolean;
  animated?: boolean;
  valueSize?: VariantProps<typeof metricValueVariants>['size'];
  valueColor?: VariantProps<typeof metricValueVariants>['color'];
}

// ============================================================================
// Helper Components
// ============================================================================

const TrendIndicator: React.FC<{ trend: NonNullable<MetricCardProps['trend']> }> = ({ trend }) => {
  const TrendIcon = trend.direction === 'up' ? TrendingUp : trend.direction === 'down' ? TrendingDown : Minus;
  
  return (
    <div className={cn(trendVariants({ trend: trend.direction }))}>
      <TrendIcon className="h-3 w-3" />
      <span>{Math.abs(trend.value)}%</span>
      {trend.label && <span className="text-muted-foreground">• {trend.label}</span>}
    </div>
  );
};

const LoadingSkeleton: React.FC = () => (
  <div className="animate-pulse space-y-3">
    <div className="flex items-center justify-between">
      <div className="h-4 bg-muted rounded w-24"></div>
      <div className="h-6 w-6 bg-muted rounded"></div>
    </div>
    <div className="h-8 bg-muted rounded w-20"></div>
    <div className="h-3 bg-muted rounded w-16"></div>
  </div>
);

// ============================================================================
// Main Component
// ============================================================================

export const MetricCard: React.FC<MetricCardProps> = ({
  className,
  variant,
  size,
  glow,
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  badge,
  loading = false,
  animated = true,
  valueSize,
  valueColor,
  ...props
}) => {
  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      // Format large numbers with appropriate suffixes
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <Card
      className={cn(
        metricCardVariants({ variant, size, glow }),
        animated && "hover:scale-[1.02] active:scale-[0.98]",
        className
      )}
      {...props}
    >
      {loading ? (
        <LoadingSkeleton />
      ) : (
        <>
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Content */}
          <div className="relative space-y-3">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">
                {title}
              </h3>
              <div className="flex items-center gap-2">
                {badge && (
                  <Badge variant={badge.variant} className="text-xs">
                    {badge.text}
                  </Badge>
                )}
                {Icon && (
                  <div className="p-2 rounded-lg bg-primary/10 text-primary group-hover:bg-primary/20 transition-colors duration-300">
                    <Icon className="h-4 w-4" />
                  </div>
                )}
              </div>
            </div>

            {/* Value */}
            <div className="space-y-1">
              <div className={cn(
                metricValueVariants({ size: valueSize, color: valueColor }),
                animated && "group-hover:scale-105 transition-transform duration-300"
              )}>
                {formatValue(value)}
              </div>
              
              {/* Subtitle and Trend */}
              <div className="flex items-center justify-between">
                {subtitle && (
                  <p className="text-sm text-muted-foreground">
                    {subtitle}
                  </p>
                )}
                {trend && <TrendIndicator trend={trend} />}
              </div>
            </div>
          </div>

          {/* Hover Accent */}
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-secondary to-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </>
      )}
    </Card>
  );
};

// ============================================================================
// Metric Grid Component
// ============================================================================

export interface MetricGridProps extends React.HTMLAttributes<HTMLDivElement> {
  metrics: MetricCardProps[];
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'default' | 'lg';
  loading?: boolean;
}

export const MetricGrid: React.FC<MetricGridProps> = ({
  className,
  metrics,
  columns = 4,
  gap = 'default',
  loading = false,
  ...props
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
  };

  const gapClasses = {
    sm: 'gap-3',
    default: 'gap-4',
    lg: 'gap-6',
  };

  return (
    <div
      className={cn(
        'grid',
        gridCols[columns],
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          loading={loading}
          {...metric}
        />
      ))}
    </div>
  );
};

export default MetricCard; 