// @ts-nocheck
import React, { forwardRef } from 'react';
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 touch-manipulation select-none",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/80",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/60",
        ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        link: "text-primary underline-offset-4 hover:underline active:opacity-80",
        gradient:
          "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70 active:from-primary/80 active:to-primary/60",
      },
      size: {
        default: "h-10 px-4 py-2 min-h-[44px] min-w-[44px]", // iOS touch target recommendation
        sm: "h-9 rounded-md px-3 min-h-[40px] text-sm",
        lg: "h-11 rounded-md px-8 min-h-[48px] text-base",
        icon: "h-10 w-10 min-h-[44px] min-w-[44px]",
        "icon-sm": "h-8 w-8 min-h-[36px] min-w-[36px]",
        "icon-lg": "h-12 w-12 min-h-[48px] min-w-[48px]",
      },
      responsive: {
        true: "px-3 py-2 sm:px-4 sm:py-2 md:px-6 md:py-3",
        false: "",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
      loading: {
        true: "cursor-not-allowed opacity-70",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      responsive: false,
      fullWidth: false,
      loading: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  hapticFeedback?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    responsive, 
    fullWidth, 
    loading, 
    asChild = false, 
    leftIcon,
    rightIcon,
    hapticFeedback = false,
    children,
    onClick,
    disabled,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) return
      
      // Haptic feedback for mobile devices
      if (hapticFeedback && 'vibrate' in navigator) {
        navigator.vibrate(50)
      }
      
      onClick?.(e)
    }

    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, responsive, fullWidth, loading }),
          className
        )}
        ref={ref}
        onClick={handleClick}
        disabled={disabled || loading}
        {...props}
      >
        {/* Loading spinner */}
        {loading && (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-transparent border-t-current" />
        )}
        
        {/* Left icon */}
        {leftIcon && !loading && (
          <span className="mr-2 flex items-center">
            {leftIcon}
          </span>
        )}
        
        {/* Button content */}
        <span className="flex items-center justify-center">
          {children}
        </span>
        
        {/* Right icon */}
        {rightIcon && !loading && (
          <span className="ml-2 flex items-center">
            {rightIcon}
          </span>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

// ============================================================================
// Mobile-First Button Group
// ============================================================================

export interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical' | 'responsive'
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  fullWidth?: boolean
  children: React.ReactNode
}

const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, orientation = 'horizontal', variant = 'default', size = 'default', fullWidth, children, ...props }, ref) => {
    const orientationClasses = {
      horizontal: "flex-row",
      vertical: "flex-col",
      responsive: "flex-col sm:flex-row"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "flex gap-2",
          orientationClasses[orientation],
          fullWidth && "w-full",
          orientation === 'horizontal' && "[&>*]:rounded-none [&>*:first-child]:rounded-l-md [&>*:last-child]:rounded-r-md",
          orientation === 'vertical' && "[&>*]:rounded-none [&>*:first-child]:rounded-t-md [&>*:last-child]:rounded-b-md",
          orientation === 'responsive' && "sm:[&>*]:rounded-none sm:[&>*:first-child]:rounded-l-md sm:[&>*:last-child]:rounded-r-md",
          className
        )}
        {...props}
      >
        {React.Children.map(children, (child, index) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              variant: child.props.variant || variant,
              size: child.props.size || size,
              fullWidth: fullWidth,
              ...(child.props as any)
            })
          }
          return child
        })}
      </div>
    )
  }
)
ButtonGroup.displayName = "ButtonGroup"

// ============================================================================
// Mobile-First Floating Action Button
// ============================================================================

export interface FloatingActionButtonProps extends Omit<ButtonProps, 'size'> {
  size?: 'default' | 'lg'
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  shadow?: boolean
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({ className, size = 'default', position = 'bottom-right', shadow = true, ...props }, ref) => {
    const positionClasses = {
      'bottom-right': 'fixed bottom-4 right-4 sm:bottom-6 sm:right-6',
      'bottom-left': 'fixed bottom-4 left-4 sm:bottom-6 sm:left-6',
      'top-right': 'fixed top-4 right-4 sm:top-6 sm:right-6',
      'top-left': 'fixed top-4 left-4 sm:top-6 sm:left-6'
    }

    const sizeClasses = {
      default: 'h-14 w-14 rounded-full',
      lg: 'h-16 w-16 rounded-full'
    }

    return (
      <Button
        ref={ref}
        className={cn(
          positionClasses[position],
          sizeClasses[size],
          shadow && 'shadow-lg hover:shadow-xl',
          'z-50 transition-all duration-200',
          className
        )}
        hapticFeedback={true}
        {...props}
      />
    )
  }
)
FloatingActionButton.displayName = "FloatingActionButton"

// ============================================================================
// Mobile-First Toggle Button
// ============================================================================

export interface ToggleButtonProps extends Omit<ButtonProps, 'variant'> {
  pressed?: boolean
  onPressedChange?: (pressed: boolean) => void
  variant?: 'default' | 'outline' | 'ghost'
}

const ToggleButton = React.forwardRef<HTMLButtonElement, ToggleButtonProps>(
  ({ className, pressed = false, onPressedChange, variant = 'outline', onClick, ...props }, ref) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      const newPressed = !pressed
      onPressedChange?.(newPressed)
      onClick?.(e)
    }

    return (
      <Button
        ref={ref}
        variant={pressed ? 'default' : variant}
        className={cn(
          'data-[state=on]:bg-accent data-[state=on]:text-accent-foreground',
          className
        )}
        onClick={handleClick}
        aria-pressed={pressed}
        data-state={pressed ? 'on' : 'off'}
        {...props}
      />
    )
  }
)
ToggleButton.displayName = "ToggleButton"

export { Button, ButtonGroup, FloatingActionButton, ToggleButton, buttonVariants } 