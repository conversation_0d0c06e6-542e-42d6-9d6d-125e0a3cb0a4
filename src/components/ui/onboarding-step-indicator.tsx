// @ts-nocheck
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { CheckCircle, Circle, ArrowRight, ArrowLeft, ShoppingBag, MessageSquare, Settings, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { OnboardingStep } from './onboarding-progress-badge';

// ============================================================================
// Component Variants
// ============================================================================

const stepIndicatorVariants = cva(
  "w-full max-w-4xl mx-auto",
  {
    variants: {
      variant: {
        horizontal: "space-y-4",
        vertical: "space-y-6",
        compact: "space-y-2",
      },
      size: {
        sm: "text-sm",
        default: "text-base",
        lg: "text-lg",
      },
    },
    defaultVariants: {
      variant: "horizontal",
      size: "default",
    },
  }
);

const stepItemVariants = cva(
  "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200",
  {
    variants: {
      state: {
        completed: "bg-green-50 border-green-200 text-green-800",
        current: "bg-blue-50 border-blue-200 text-blue-800 shadow-sm",
        upcoming: "bg-gray-50 border-gray-200 text-gray-600",
        optional: "bg-yellow-50 border-yellow-200 text-yellow-800",
      },
    },
    defaultVariants: {
      state: "upcoming",
    },
  }
);

// ============================================================================
// Types
// ============================================================================

export interface OnboardingStepIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stepIndicatorVariants> {
  steps: OnboardingStep[];
  currentStep: string;
  onStepClick?: (stepId: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onSkip?: () => void;
  showNavigation?: boolean;
  showStepNumbers?: boolean;
  allowStepJumping?: boolean;
  interactive?: boolean;
}

// ============================================================================
// Step Icons
// ============================================================================

const getStepIcon = (stepId: string) => {
  switch (stepId) {
    case 'welcome':
      return Users;
    case 'shopify':
      return ShoppingBag;
    case 'whatsapp':
      return MessageSquare;
    case 'configuration':
      return Settings;
    default:
      return Circle;
  }
};

// ============================================================================
// Main Component
// ============================================================================

export const OnboardingStepIndicator: React.FC<OnboardingStepIndicatorProps> = ({
  className,
  variant,
  size,
  steps,
  currentStep,
  onStepClick,
  onNext,
  onPrevious,
  onSkip,
  showNavigation = true,
  showStepNumbers = true,
  allowStepJumping = false,
  interactive = true,
  ...props
}) => {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;
  
  const getStepState = (step: OnboardingStep, index: number) => {
    if (step.completed) return 'completed';
    if (step.id === currentStep) return 'current';
    if (step.optional && !step.completed) return 'optional';
    return 'upcoming';
  };

  const handleStepClick = (stepId: string) => {
    if (interactive && allowStepJumping && onStepClick) {
      onStepClick(stepId);
    }
  };

  return (
    <div
      className={cn(stepIndicatorVariants({ variant, size }), className)}
      {...props}
    >
      {/* Step Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-foreground">
            Setup Progress
          </h2>
          <Badge variant="secondary" className="text-xs">
            {steps.filter(s => s.completed).length} of {steps.length} completed
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {steps.map((step, index) => {
            const Icon = getStepIcon(step.id);
            const state = getStepState(step, index);
            const isClickable = interactive && allowStepJumping && (step.completed || step.id === currentStep);
            
            return (
              <React.Fragment key={step.id}>
                <div
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200",
                    isClickable && "cursor-pointer hover:bg-muted/50",
                    state === 'completed' && "bg-green-100 text-green-700",
                    state === 'current' && "bg-blue-100 text-blue-700 shadow-sm",
                    state === 'upcoming' && "bg-gray-100 text-gray-500",
                    state === 'optional' && "bg-yellow-100 text-yellow-700"
                  )}
                  onClick={() => isClickable && handleStepClick(step.id)}
                >
                  <div className="flex items-center gap-2">
                    {step.completed ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                    
                    {showStepNumbers && (
                      <span className="text-xs font-medium">
                        {index + 1}
                      </span>
                    )}
                    
                    <span className="text-xs font-medium hidden sm:block">
                      {step.name}
                    </span>
                  </div>
                  
                  {step.optional && (
                    <Badge variant="outline" className="text-xs">
                      Optional
                    </Badge>
                  )}
                </div>
                
                {index < steps.length - 1 && (
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>

      {/* Current Step Details */}
      {currentStepData && (
        <div className="bg-background border border-border rounded-lg p-6 mb-6">
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-lg bg-primary/10 text-primary">
              {React.createElement(getStepIcon(currentStepData.id), { className: "h-6 w-6" })}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-lg font-semibold text-foreground">
                  {currentStepData.name}
                </h3>
                
                {currentStepData.optional && (
                  <Badge variant="outline" className="text-xs">
                    Optional
                  </Badge>
                )}
                
                {currentStepData.completed && (
                  <Badge variant="default" className="text-xs">
                    Completed
                  </Badge>
                )}
              </div>
              
              <p className="text-muted-foreground mb-4">
                {currentStepData.description}
              </p>
              
              {/* Step Navigation */}
              {showNavigation && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {!isFirstStep && onPrevious && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onPrevious}
                        className="gap-2"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        Previous
                      </Button>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {currentStepData.optional && onSkip && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onSkip}
                        className="text-muted-foreground"
                      >
                        Skip this step
                      </Button>
                    )}
                    
                    {!isLastStep && onNext && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={onNext}
                        className="gap-2"
                      >
                        {currentStepData.completed ? 'Next' : 'Continue'}
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    )}
                    
                    {isLastStep && currentStepData.completed && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={onNext}
                        className="gap-2"
                      >
                        Finish Setup
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* All Steps List */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-muted-foreground mb-3">
          All Steps
        </h3>
        
        {steps.map((step, index) => {
          const Icon = getStepIcon(step.id);
          const state = getStepState(step, index);
          const isClickable = interactive && allowStepJumping && (step.completed || step.id === currentStep);
          
          return (
            <div
              key={step.id}
              className={cn(
                stepItemVariants({ state }),
                isClickable && "cursor-pointer hover:shadow-md"
              )}
              onClick={() => isClickable && handleStepClick(step.id)}
            >
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Icon className="h-5 w-5" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    {showStepNumbers && (
                      <span className="text-xs font-medium bg-background rounded-full w-6 h-6 flex items-center justify-center">
                        {index + 1}
                      </span>
                    )}
                    
                    <h4 className="font-medium text-foreground">
                      {step.name}
                    </h4>
                    
                    {step.optional && (
                      <Badge variant="outline" className="text-xs">
                        Optional
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {step.description}
                  </p>
                </div>
                
                {step.id === currentStep && (
                  <div className="flex-shrink-0">
                    <Badge variant="default" className="text-xs">
                      Current
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// ============================================================================
// Compact Variant
// ============================================================================

export interface CompactStepIndicatorProps extends Omit<OnboardingStepIndicatorProps, 'variant' | 'showNavigation'> {
  showProgress?: boolean;
}

export const CompactStepIndicator: React.FC<CompactStepIndicatorProps> = ({
  showProgress = true,
  ...props
}) => {
  return (
    <OnboardingStepIndicator
      {...props}
      variant="compact"
      showNavigation={false}
      className="max-w-2xl"
    />
  );
};

export default OnboardingStepIndicator; 