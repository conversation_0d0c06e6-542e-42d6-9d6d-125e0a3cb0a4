import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-xl border bg-white text-slate-900 shadow transition-all duration-200 touch-manipulation",
  {
    variants: {
      variant: {
        default: "border-slate-200",
        outline: "border-2 border-slate-200",
        ghost: "border-transparent shadow-none",
        elevated: "shadow-lg hover:shadow-xl",
        glass: "bg-white/80 backdrop-blur-sm border-white/20",
        gradient: "bg-gradient-to-r from-emerald-600 to-teal-600 text-white border-transparent",
        mobile: "border-slate-200 active:bg-slate-50 active:scale-[0.98]",
        "mobile-elevated": "shadow-lg hover:shadow-xl active:shadow-md active:scale-[0.98]",
        "mobile-glass": "bg-white/80 backdrop-blur-sm border-white/20 active:bg-white/90",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
        "mobile-xs": "p-3",
        "mobile-sm": "p-4",
        "mobile-md": "p-5",
        "mobile-lg": "p-6",
        "mobile-responsive": "p-3 sm:p-4 md:p-5 lg:p-6",
      },
      spacing: {
        default: "space-y-4",
        compact: "space-y-2",
        comfortable: "space-y-6",
        "mobile-compact": "space-y-3",
        "mobile-comfortable": "space-y-4",
      },
      interactive: {
        none: "",
        hover: "hover:shadow-lg hover:-translate-y-1 cursor-pointer",
        "mobile-tap": "active:scale-[0.98] active:shadow-md transition-transform duration-150",
        "mobile-hover": "hover:shadow-lg hover:-translate-y-1 active:scale-[0.98] cursor-pointer",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      spacing: "default",
      interactive: "none",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  hover?: boolean
  /** Mobile-specific prop for touch interactions */
  mobileOptimized?: boolean
  /** Adds haptic feedback on mobile devices */
  hapticFeedback?: boolean
  /** Loading state for async operations */
  loading?: boolean
  /** Clickable card with proper accessibility */
  clickable?: boolean
  /** Custom minimum height for mobile */
  minHeight?: string
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ 
    className, 
    variant, 
    size, 
    spacing,
    interactive,
    hover = false, 
    mobileOptimized = false,
    hapticFeedback = false,
    loading = false,
    clickable = false,
    minHeight,
    onClick,
    ...props 
  }, ref) => {
    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (hapticFeedback && 'vibrate' in navigator) {
        navigator.vibrate(1)
      }
      onClick?.(e)
    }

    return (
      <div
        ref={ref}
        className={cn(
          cardVariants({ 
            variant: mobileOptimized ? "mobile" : variant, 
            size: mobileOptimized ? "mobile-responsive" : size,
            spacing: mobileOptimized ? "mobile-comfortable" : spacing,
            interactive: mobileOptimized ? "mobile-tap" : (hover || clickable) ? "hover" : interactive,
          }),
          loading && "opacity-70 pointer-events-none",
          clickable && "cursor-pointer focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2",
          mobileOptimized && "min-h-[44px] touch-manipulation",
          className
        )}
        style={{ minHeight }}
        onClick={onClick ? handleClick : undefined}
        role={clickable ? "button" : undefined}
        tabIndex={clickable ? 0 : undefined}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Mobile-optimized header with better spacing */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col space-y-1.5 pb-6",
      mobileOptimized && "pb-4 space-y-2",
      className
    )}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement> & {
    /** Mobile-optimized title with responsive typography */
    mobileOptimized?: boolean
    /** Typography size variant */
    size?: "sm" | "md" | "lg" | "xl"
  }
>(({ className, children, mobileOptimized = false, size = "md", ...props }, ref) => {
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl",
    xl: "text-3xl",
  }

  return (
    <h3
      ref={ref}
      className={cn(
        "font-semibold leading-none tracking-tight",
        mobileOptimized ? "text-lg sm:text-xl" : sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </h3>
  )
})
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement> & {
    /** Mobile-optimized description with better readability */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      "text-sm text-slate-600 leading-relaxed",
      mobileOptimized && "text-sm sm:text-base leading-relaxed",
      className
    )}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Mobile-optimized content with better spacing */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <div 
    ref={ref} 
    className={cn(
      "pt-0", 
      mobileOptimized && "space-y-3 sm:space-y-4",
      className
    )} 
    {...props} 
  />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Mobile-optimized footer with better button spacing */
    mobileOptimized?: boolean
  }
>(({ className, mobileOptimized = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center pt-6",
      mobileOptimized && "pt-4 gap-3 flex-wrap",
      className
    )}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// Mobile-specific card variants
const MobileCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ ...props }, ref) => (
    <Card
      ref={ref}
      mobileOptimized
      hapticFeedback
      {...props}
    />
  )
)
MobileCard.displayName = "MobileCard"

const MobileActionCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ ...props }, ref) => (
    <Card
      ref={ref}
      mobileOptimized
      hapticFeedback
      clickable
      variant="mobile-elevated"
      {...props}
    />
  )
)
MobileActionCard.displayName = "MobileActionCard"

const MobileInfoCard = React.forwardRef<HTMLDivElement, CardProps>(
  ({ ...props }, ref) => (
    <Card
      ref={ref}
      mobileOptimized
      variant="mobile-glass"
      {...props}
    />
  )
)
MobileInfoCard.displayName = "MobileInfoCard"

export { 
  Card, 
  CardHeader, 
  CardFooter, 
  CardTitle, 
  CardDescription, 
  CardContent,
  MobileCard,
  MobileActionCard,
  MobileInfoCard,
  cardVariants,
} 