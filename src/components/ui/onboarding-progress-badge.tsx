// @ts-nocheck
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { CheckCircle, Circle, Settings, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MobileTouchArea } from '@/components/ui/mobile-spacing';
import { hapticFeedback } from '@/components/ui/mobile-interactions';
import { cn } from '@/lib/utils';

// ============================================================================
// Component Variants
// ============================================================================

const progressBadgeVariants = cva(
  "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-background/95 backdrop-blur-sm border border-border/50 rounded-full shadow-lg transition-all duration-300 touch-manipulation",
  {
    variants: {
      variant: {
        incomplete: "border-yellow-200 bg-yellow-50/90 text-yellow-800",
        complete: "border-green-200 bg-green-50/90 text-green-800",
        skipped: "border-gray-200 bg-gray-50/90 text-gray-600",
      },
      size: {
        sm: "px-3 py-1.5 text-xs",
        default: "px-4 py-2 text-sm",
        lg: "px-6 py-3 text-base",
        // Mobile-specific sizes
        "mobile-sm": "px-3 py-2 text-xs min-h-[44px]",
        "mobile-md": "px-4 py-3 text-sm min-h-[48px]",
        "mobile-lg": "px-6 py-4 text-base min-h-[52px]",
      },
      responsive: {
        true: "px-3 py-1.5 text-xs sm:px-4 sm:py-2 sm:text-sm md:px-6 md:py-3 md:text-base",
        false: "",
      },
    },
    defaultVariants: {
      variant: "incomplete",
      size: "default",
      responsive: false,
    },
  }
);

// ============================================================================
// Types
// ============================================================================

export interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  completed: boolean;
  optional?: boolean;
}

export interface OnboardingProgressBadgeProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof progressBadgeVariants> {
  steps: OnboardingStep[];
  currentStep?: string;
  onContinue?: () => void;
  onSkip?: () => void;
  onDismiss?: () => void;
  showSkipOption?: boolean;
  showDismissOption?: boolean;
  isVisible?: boolean;
  /** Mobile-optimized variant with better touch targets */
  mobileOptimized?: boolean;
  /** Enable haptic feedback on mobile devices */
  hapticFeedback?: boolean;
}

// ============================================================================
// Mobile-First Progress Indicator
// ============================================================================

const MobileProgressIndicator: React.FC<{
  completedSteps: number;
  totalSteps: number;
  isComplete: boolean;
  mobileOptimized?: boolean;
}> = ({ completedSteps, totalSteps, isComplete, mobileOptimized = false }) => {
  const progressPercentage = (completedSteps / totalSteps) * 100;
  
  return (
    <div className="flex items-center gap-2">
      <div className={cn(
        "bg-gray-200 rounded-full overflow-hidden",
        mobileOptimized ? "w-20 h-3" : "w-16 h-2"
      )}>
        <div 
          className={cn(
            "h-full rounded-full transition-all duration-500",
            isComplete ? "bg-green-500" : "bg-yellow-500"
          )}
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
      <span className={cn(
        "font-medium",
        mobileOptimized ? "text-sm" : "text-xs"
      )}>
        {Math.round(progressPercentage)}%
      </span>
    </div>
  );
};

// ============================================================================
// Mobile-First Action Buttons
// ============================================================================

const MobileActionButtons: React.FC<{
  onContinue?: () => void;
  onSkip?: () => void;
  onDismiss?: () => void;
  showSkipOption?: boolean;
  showDismissOption?: boolean;
  isComplete?: boolean;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}> = ({ 
  onContinue, 
  onSkip, 
  onDismiss, 
  showSkipOption = true, 
  showDismissOption = true, 
  isComplete = false,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
}) => {
  const handleContinue = () => {
    if (enableHaptic) hapticFeedback.light();
    onContinue?.();
  };

  const handleSkip = () => {
    if (enableHaptic) hapticFeedback.medium();
    onSkip?.();
  };

  const handleDismiss = () => {
    if (enableHaptic) hapticFeedback.light();
    onDismiss?.();
  };

  return (
    <div className="flex items-center gap-2">
      {/* Continue Button */}
      {onContinue && (
        <Button
          variant="default"
          size={mobileOptimized ? "sm" : "sm"}
          onClick={handleContinue}
          className={cn(
            "gap-1 text-xs font-medium",
            mobileOptimized && "min-h-[36px] min-w-[36px] px-3 py-1.5"
          )}
        >
          {isComplete ? 'View' : 'Continue'}
        </Button>
      )}

      {/* Skip Button */}
      {showSkipOption && onSkip && (
        <Button
          variant="ghost"
          size={mobileOptimized ? "sm" : "sm"}
          onClick={handleSkip}
          className={cn(
            "text-xs text-muted-foreground hover:text-foreground",
            mobileOptimized && "min-h-[36px] min-w-[36px] px-3 py-1.5"
          )}
        >
          Skip
        </Button>
      )}

      {/* Dismiss Button */}
      {showDismissOption && onDismiss && (
        <Button
          variant="ghost"
          size={mobileOptimized ? "sm" : "sm"}
          onClick={handleDismiss}
          className={cn(
            "p-0 h-auto w-auto text-muted-foreground hover:text-foreground",
            mobileOptimized && "min-h-[36px] min-w-[36px] flex items-center justify-center"
          )}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

// ============================================================================
// Main Component
// ============================================================================

export const OnboardingProgressBadge: React.FC<OnboardingProgressBadgeProps> = ({
  className,
  variant,
  size,
  responsive = false,
  steps,
  currentStep,
  onContinue,
  onSkip,
  onDismiss,
  showSkipOption = true,
  showDismissOption = true,
  isVisible = true,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
  ...props
}) => {
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const isComplete = completedSteps === totalSteps;
  
  // Auto-determine variant based on completion
  const displayVariant = variant || (isComplete ? 'complete' : 'incomplete');
  
  // Auto-determine size based on mobile optimization
  const displaySize = mobileOptimized ? 'mobile-md' : size;
  
  // Don't render if not visible
  if (!isVisible) return null;

  return (
    <div
      className={cn(
        progressBadgeVariants({ 
          variant: displayVariant, 
          size: displaySize,
          responsive: responsive || mobileOptimized,
        }),
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        {/* Progress Icon */}
        <div className="flex items-center gap-2">
          {isComplete ? (
            <CheckCircle className={cn(
              "text-green-600",
              mobileOptimized ? "h-5 w-5" : "h-5 w-5"
            )} />
          ) : (
            <Circle className={cn(
              "text-yellow-600",
              mobileOptimized ? "h-5 w-5" : "h-5 w-5"
            )} />
          )}
          
          {/* Progress Text */}
          <div className="flex items-center gap-2">
            <span className={cn(
              "font-medium",
              mobileOptimized ? "text-sm" : "text-sm"
            )}>
              {isComplete ? 'Setup Complete!' : 'Setup Progress'}
            </span>
            <Badge 
              variant={isComplete ? 'default' : 'secondary'} 
              className={cn(
                "text-xs",
                mobileOptimized && "px-2 py-0.5"
              )}
            >
              {completedSteps}/{totalSteps}
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <MobileProgressIndicator
          completedSteps={completedSteps}
          totalSteps={totalSteps}
          isComplete={isComplete}
          mobileOptimized={mobileOptimized}
        />

        {/* Action Buttons */}
        <MobileActionButtons
          onContinue={onContinue}
          onSkip={onSkip}
          onDismiss={onDismiss}
          showSkipOption={showSkipOption}
          showDismissOption={showDismissOption}
          isComplete={isComplete}
          mobileOptimized={mobileOptimized}
          hapticFeedback={enableHaptic}
        />
      </div>
    </div>
  );
};

// ============================================================================
// Compact Mobile-First Progress Badge
// ============================================================================

export interface CompactProgressBadgeProps extends OnboardingProgressBadgeProps {
  showPercentage?: boolean;
}

export const CompactProgressBadge: React.FC<CompactProgressBadgeProps> = ({
  showPercentage = true,
  mobileOptimized = true,
  ...props
}) => {
  const completedSteps = props.steps.filter(step => step.completed).length;
  const totalSteps = props.steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;

  return (
    <OnboardingProgressBadge
      {...props}
      size="mobile-sm"
      mobileOptimized={mobileOptimized}
      className={cn("px-3 py-1", props.className)}
    >
      <div className="flex items-center gap-2">
        {isComplete ? (
          <CheckCircle className="h-4 w-4 text-green-600" />
        ) : (
          <Settings className="h-4 w-4 text-yellow-600 animate-pulse" />
        )}
        
        <span className="text-xs font-medium">
          {isComplete ? 'Ready' : 'Setup'}
        </span>
        
        {showPercentage && (
          <span className="text-xs text-muted-foreground">
            {Math.round(progressPercentage)}%
          </span>
        )}
      </div>
    </OnboardingProgressBadge>
  );
};

// ============================================================================
// Mobile-First Inline Progress Badge
// ============================================================================

export interface InlineProgressBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  steps: OnboardingStep[];
  currentStep?: string;
  onContinue?: () => void;
  onSkip?: () => void;
  showActions?: boolean;
  mobileOptimized?: boolean;
  hapticFeedback?: boolean;
}

export const InlineProgressBadge: React.FC<InlineProgressBadgeProps> = ({
  className,
  steps,
  currentStep,
  onContinue,
  onSkip,
  showActions = true,
  mobileOptimized = false,
  hapticFeedback: enableHaptic = false,
  ...props
}) => {
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;

  return (
    <div
      className={cn(
        "flex items-center justify-between p-3 bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg",
        mobileOptimized && "min-h-[56px] touch-manipulation",
        className
      )}
      {...props}
    >
      {/* Progress Info */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          {isComplete ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <Settings className="h-5 w-5 text-yellow-600" />
          )}
          
          <div className="flex items-center gap-2">
            <span className={cn(
              "font-medium",
              mobileOptimized ? "text-sm" : "text-sm"
            )}>
              Setup Progress
            </span>
            <Badge variant="secondary" className="text-xs">
              {completedSteps}/{totalSteps}
            </Badge>
          </div>
        </div>

        <MobileProgressIndicator
          completedSteps={completedSteps}
          totalSteps={totalSteps}
          isComplete={isComplete}
          mobileOptimized={mobileOptimized}
        />
      </div>

      {/* Actions */}
      {showActions && (
        <MobileActionButtons
          onContinue={onContinue}
          onSkip={onSkip}
          showSkipOption={true}
          showDismissOption={false}
          isComplete={isComplete}
          mobileOptimized={mobileOptimized}
          hapticFeedback={enableHaptic}
        />
      )}
    </div>
  );
};

// ============================================================================
// Utility Functions
// ============================================================================

export const createOnboardingSteps = (customSteps?: Partial<OnboardingStep>[]): OnboardingStep[] => {
  const defaultSteps: OnboardingStep[] = [
    {
      id: 'welcome',
      name: 'Welcome & Account Setup',
      description: 'Complete your account setup',
      completed: true, // Auto-completed after signup
    },
    {
      id: 'shopify',
      name: 'Shopify Store Connection',
      description: 'Connect your Shopify store',
      completed: false,
      optional: true,
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp Business Integration',
      description: 'Connect your WhatsApp Business account',
      completed: false,
      optional: true,
    },
    {
      id: 'configuration',
      name: 'Initial Configuration',
      description: 'Configure your preferences',
      completed: false,
      optional: true,
    },
  ];

  // Merge custom steps with defaults
  if (customSteps) {
    return defaultSteps.map(defaultStep => {
      const customStep = customSteps.find(step => step.id === defaultStep.id);
      return customStep ? { ...defaultStep, ...customStep } : defaultStep;
    });
  }

  return defaultSteps;
};

// ============================================================================
// Mobile-First Hook
// ============================================================================

export const useMobileProgressBadge = (steps: OnboardingStep[]) => {
  const [isMobile, setIsMobile] = React.useState(false);
  
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;
  const isComplete = completedSteps === totalSteps;

  return {
    isMobile,
    completedSteps,
    totalSteps,
    progressPercentage,
    isComplete,
    mobileOptimized: isMobile,
  };
};

export { progressBadgeVariants }; 