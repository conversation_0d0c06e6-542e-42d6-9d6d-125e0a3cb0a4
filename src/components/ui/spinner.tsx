import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const spinnerVariants = cva("animate-spin", {
  variants: {
    variant: {
      default: "text-primary",
      secondary: "text-secondary-foreground",
      muted: "text-muted-foreground",
      white: "text-white",
      gradient: "text-transparent bg-gradient-primary bg-clip-text",
    },
    size: {
      xs: "h-3 w-3",
      sm: "h-4 w-4",
      default: "h-6 w-6",
      lg: "h-8 w-8",
      xl: "h-12 w-12",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  },
})

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  icon?: React.ComponentType<{ className?: string }>
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, variant, size, icon: Icon = Loader2, ...props }, ref) => {
    return (
      <div ref={ref} {...props}>
        <Icon className={cn(spinnerVariants({ variant, size }), className)} />
      </div>
    )
  }
)
Spinner.displayName = "Spinner"

// Loading Skeleton Component
export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "rounded" | "circle"
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = "default", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "loading-skeleton",
          variant === "rounded" && "rounded-md",
          variant === "circle" && "rounded-full",
          className
        )}
        {...props}
      />
    )
  }
)
Skeleton.displayName = "Skeleton"

// Full Page Loader Component
export interface LoaderProps {
  text?: string
  variant?: "default" | "gradient" | "minimal"
  size?: "sm" | "default" | "lg"
}

const Loader: React.FC<LoaderProps> = ({
  text = "Loading...",
  variant = "default",
  size = "default",
}) => {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="flex flex-col items-center space-y-4">
        {variant === "gradient" ? (
          <div className="relative">
            <div className="h-12 w-12 rounded-full bg-gradient-primary opacity-20 animate-ping"></div>
            <div className="absolute inset-0 h-12 w-12 rounded-full bg-gradient-primary animate-pulse"></div>
            <Spinner
              variant="white"
              size={size === "sm" ? "default" : size === "lg" ? "xl" : "lg"}
              className="absolute inset-0 m-auto"
            />
          </div>
        ) : variant === "minimal" ? (
          <Spinner
            variant="default"
            size={size === "sm" ? "default" : size === "lg" ? "xl" : "lg"}
          />
        ) : (
          <div className="relative">
            <div className="h-16 w-16 rounded-full border-4 border-muted animate-pulse"></div>
            <Spinner
              variant="default"
              size={size === "sm" ? "lg" : size === "lg" ? "xl" : "xl"}
              className="absolute inset-0 m-auto"
            />
          </div>
        )}
        {text && (
          <p className="text-sm text-muted-foreground font-medium animate-pulse">
            {text}
          </p>
        )}
      </div>
    </div>
  )
}

// Dots Loader Component
export interface DotsLoaderProps {
  variant?: "default" | "gradient"
  size?: "sm" | "default" | "lg"
}

const DotsLoader: React.FC<DotsLoaderProps> = ({
  variant = "default",
  size = "default",
}) => {
  const dotSize = {
    sm: "h-1 w-1",
    default: "h-2 w-2",
    lg: "h-3 w-3",
  }[size]

  const dotClass = cn(
    "rounded-full animate-pulse",
    dotSize,
    variant === "gradient"
      ? "bg-gradient-primary"
      : "bg-primary"
  )

  return (
    <div className="flex items-center space-x-1">
      <div className={cn(dotClass, "animation-delay-0")}></div>
      <div className={cn(dotClass, "animation-delay-150")}></div>
      <div className={cn(dotClass, "animation-delay-300")}></div>
    </div>
  )
}

// Pulse Loader Component
export interface PulseLoaderProps {
  variant?: "default" | "gradient"
  size?: "sm" | "default" | "lg"
}

const PulseLoader: React.FC<PulseLoaderProps> = ({
  variant = "default",
  size = "default",
}) => {
  const pulseSize = {
    sm: "h-8 w-8",
    default: "h-12 w-12",
    lg: "h-16 w-16",
  }[size]

  return (
    <div className="relative flex items-center justify-center">
      <div
        className={cn(
          "rounded-full animate-ping",
          pulseSize,
          variant === "gradient"
            ? "bg-gradient-primary opacity-30"
            : "bg-primary opacity-30"
        )}
      ></div>
      <div
        className={cn(
          "absolute rounded-full animate-pulse",
          pulseSize,
          variant === "gradient"
            ? "bg-gradient-primary opacity-60"
            : "bg-primary opacity-60"
        )}
      ></div>
    </div>
  )
}

export { Spinner, Skeleton, Loader, DotsLoader, PulseLoader, spinnerVariants } 