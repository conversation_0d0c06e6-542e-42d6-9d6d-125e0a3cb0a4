import { useState, useEffect, useCallback, useMemo } from 'react';
import { executeGraphQLOperation } from '../lib/graphql-client';
import { GET_CONVERSATION_MESSAGES } from '../lib/graphql/queries/chat';
import { useChatSubscriptions } from './useChatSubscriptions';
import type { ConversationMessage } from './useConversationMessages';
import type { Conversation } from '../types/chat';

export interface UseRealTimeConversationMessagesOptions {
  conversationId: string;
  connectionId?: string;
  organizationId?: string;
  limit?: number;
  enabled?: boolean;
}

export interface UseRealTimeConversationMessagesResult {
  messages: ConversationMessage[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadingMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  refreshing: boolean;
  isConnected: boolean;
  subscriptionErrors: string[];
  reconnectSubscriptions: () => void;
}

interface ConversationMessagesResponse {
  items: ConversationMessage[];
  nextToken?: string;
  scannedCount?: number;
}

const DEFAULT_LIMIT = 50;

/**
 * Enhanced hook that combines initial message loading with real-time subscriptions
 * This replaces the old auto-refresh polling with true real-time updates
 */
export function useRealTimeConversationMessages({
  conversationId,
  connectionId,
  organizationId,
  limit = DEFAULT_LIMIT,
  enabled = true,
}: UseRealTimeConversationMessagesOptions): UseRealTimeConversationMessagesResult {
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load messages from GraphQL API
  const loadMessages = useCallback(async (
    reset = false,
    token?: string
  ): Promise<ConversationMessagesResponse | null> => {
    if (!conversationId) return null;
    
    try {
      setError(null);
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const variables = {
        conversationId,
        limit,
        ...(token && { nextToken: token })
      };

      console.log('📚 Loading messages:', { conversationId, limit, token });

      const result = await executeGraphQLOperation(
        () => import('../lib/graphql-client').then(({ graphqlClient }) => 
          graphqlClient.graphql({
            query: GET_CONVERSATION_MESSAGES,
            variables
          }) as any
        )
      );

      if (result?.data?.getConversationMessages) {
        const { items, nextToken: newNextToken, scannedCount } = result.data.getConversationMessages;
        
        console.log(`📚 Loaded ${items.length} messages`, { nextToken: newNextToken, scannedCount });
        
        if (reset) {
          // Reverse messages for proper chat display (oldest at top, newest at bottom)
          setMessages(items.reverse());
        } else {
          setMessages(prev => {
            // Add older messages to the beginning, but reverse them first for proper order
            const existingIds = new Set(prev.map((m: ConversationMessage) => m.id));
            const newMessages = items.filter((m: ConversationMessage) => !existingIds.has(m.id));
            return [...newMessages.reverse(), ...prev];
          });
        }

        setNextToken(newNextToken);
        setHasMore(!!newNextToken);
        
        return result.data.getConversationMessages;
      }
      
      return null;
    } catch (err: any) {
      console.error('❌ Error loading conversation messages:', err);
      const errorMessage = err.message || 'Failed to load conversation messages';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [conversationId, limit]);

  // Real-time subscription callbacks
  const subscriptionCallbacks = useMemo(() => ({
    onNewMessage: (newMessage: ConversationMessage) => {
      console.log('📨 Real-time: New message received', newMessage);
      
      // Only add if it belongs to the current conversation
      if (newMessage.conversationId === conversationId) {
        setMessages(prev => {
          // Check if message already exists to avoid duplicates
          const exists = prev.some(m => m.id === newMessage.id || m.messageId === newMessage.messageId);
          if (exists) {
            console.log('📨 Message already exists, skipping duplicate');
            return prev;
          }
          
          // Add new message to the end (newest messages at bottom)
          console.log('📨 Adding new message to conversation');
          return [...prev, newMessage];
        });
      }
    },
    
    onMessageSent: (sentMessage: ConversationMessage) => {
      console.log('📤 Real-time: Message sent', sentMessage);
      
      // Only add if it belongs to the current conversation
      if (sentMessage.conversationId === conversationId) {
        setMessages(prev => {
          // Check if message already exists to avoid duplicates
          const exists = prev.some(m => m.id === sentMessage.id || m.messageId === sentMessage.messageId);
          if (exists) {
            console.log('📤 Sent message already exists, skipping duplicate');
            return prev;
          }
          
          // Add sent message to the end
          console.log('📤 Adding sent message to conversation');
          return [...prev, sentMessage];
        });
      }
    },
    
    onConversationUpdated: (updatedConversation: Conversation) => {
      console.log('💬 Real-time: Conversation updated', updatedConversation);
      // Handle conversation updates if needed
      // This could update unread counts, status, etc.
    },
    
    onError: (error: string) => {
      console.error('❌ Real-time subscription error:', error);
      setError(prev => prev ? `${prev}; ${error}` : error);
    }
  }), [conversationId]);

  // Set up real-time subscriptions
  const extractedConnectionId = useMemo(() => {
    // Extract connectionId from the first message that has one
    const messageWithConnection = messages.find(m => m.phoneNumberId || m.wabaId);
    return connectionId || messageWithConnection?.phoneNumberId || messageWithConnection?.wabaId;
  }, [connectionId, messages]);

  const {
    isConnected,
    errors: subscriptionErrors,
    reconnect: reconnectSubscriptions
  } = useChatSubscriptions(subscriptionCallbacks, {
    connectionId: extractedConnectionId,
    organizationId,
    enabled: enabled && !!organizationId
  });

  // Initial load when conversation changes
  useEffect(() => {
    if (conversationId && enabled) {
      console.log('🔄 Conversation changed, loading messages:', conversationId);
      setMessages([]);
      setNextToken(undefined);
      setHasMore(true);
      loadMessages(true);
    }
  }, [conversationId, enabled, loadMessages]);

  // Load more messages (pagination)
  const loadMore = useCallback(async () => {
    if (!hasMore || loadingMore || loading || !enabled) return;

    try {
      console.log('📖 Loading more messages...');
      setLoadingMore(true);
      await loadMessages(false, nextToken);
    } catch (err) {
      console.error('❌ Error loading more messages:', err);
    } finally {
      setLoadingMore(false);
    }
  }, [hasMore, loadingMore, loading, enabled, loadMessages, nextToken]);

  // Refresh messages (reset)
  const refresh = useCallback(async () => {
    if (loading || refreshing || !enabled) return;

    try {
      console.log('🔄 Refreshing messages...');
      setRefreshing(true);
      setNextToken(undefined);
      setHasMore(true);
      await loadMessages(true);
    } catch (err) {
      console.error('❌ Error refreshing messages:', err);
    } finally {
      setRefreshing(false);
    }
  }, [loading, refreshing, enabled, loadMessages]);

  return {
    messages,
    loading,
    error,
    hasMore,
    loadMore,
    loadingMore,
    refresh,
    refreshing,
    isConnected,
    subscriptionErrors,
    reconnectSubscriptions,
  };
} 