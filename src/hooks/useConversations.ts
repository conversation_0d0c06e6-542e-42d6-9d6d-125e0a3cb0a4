import { useState, useEffect, useCallback, useMemo } from 'react';
import { executeQuery } from '../lib/graphql-client';
import { LIST_ORGANIZATION_CONVERSATIONS } from '../lib/graphql/queries/chat';
import { useChatSubscriptions } from './useChatSubscriptions';
import { useAuth } from './useAuth';
import type { ConversationMessage } from './useConversationMessages';
import type { 
  Conversation, 
  ConversationConnection, 
  ConversationFilter, 
  UseConversationsResult
} from '../types/chat';

// Helper function to extract preview text from message content
const extractPreviewText = (content: string, messageType: string): string => {
  try {
    // Try to parse as JSON first (from real-time notifications)
    const parsed = JSON.parse(content);
    if (parsed && typeof parsed === 'object') {
      switch (parsed.type?.toLowerCase() || messageType?.toLowerCase()) {
        case 'text':
          return parsed.text || 'Text message';
        case 'image':
          return '📷 Image';
        case 'video':
          return '🎥 Video';
        case 'audio':
          return '🎵 Voice message';
        case 'document':
          return '📎 Document';
        case 'location':
          return '📍 Location';
        case 'contacts':
          return '👤 Contact';
        case 'sticker':
          return '😄 Sticker';
        default:
          return parsed.text || 'New message';
      }
    }
  } catch (e) {
    // Not JSON, return as is (already formatted)
  }
  
  // Return content as-is if it's already a preview text
  return content;
};

export const useConversations = (
  filter?: ConversationFilter,
  limit: number = 20
): UseConversationsResult => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);

  // Fetch conversations
  const fetchConversations = useCallback(async (reset: boolean = false) => {
    // Don't fetch if user or organizationId is not available
    if (!user?.organizationId) {
      setError('Organization ID not available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const variables = {
        organizationId: user.organizationId,
        limit,
        filter,
        nextToken: reset ? undefined : nextToken
      };

      const result = await executeQuery<{ listOrganizationConversations: ConversationConnection }>({
        query: LIST_ORGANIZATION_CONVERSATIONS,
        variables
      });

      if (result.data?.listOrganizationConversations) {
        const response = result.data.listOrganizationConversations;
        
        if (reset) {
          setConversations(response.items);
        } else {
          setConversations(prev => [...prev, ...response.items]);
        }
        
        setNextToken(response.nextToken);
        setHasMore(!!response.nextToken);
      } else {
        setError(result.errors?.[0]?.message || 'Failed to fetch conversations');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [user?.organizationId, filter, limit, nextToken]);

  // Real-time subscription callbacks for organization-wide updates
  const subscriptionCallbacks = useMemo(() => ({
    onNewMessage: (message: ConversationMessage) => {
      console.log('🔔 Conversation list: New message notification for conversation:', message.conversationId, {
        direction: message.direction,
        messageType: message.messageType,
        content: message.content
      });
      
      // Update the conversation list to reflect the new message
      setConversations(prev => {
        const existingIndex = prev.findIndex(conv => conv.conversationId === message.conversationId);
        
        if (existingIndex >= 0) {
          // Update existing conversation
          const updated = [...prev];
          const existingConv = updated[existingIndex];
          
          // Calculate new unread count - only increment for inbound messages (case-insensitive)
          const isInboundMessage = message.direction?.toLowerCase() === 'inbound';
          const newUnreadCount = isInboundMessage ? existingConv.unreadCount + 1 : existingConv.unreadCount;
          
          console.log('🔔 Updating conversation:', {
            conversationId: message.conversationId,
            direction: message.direction,
            oldUnreadCount: existingConv.unreadCount,
            newUnreadCount: newUnreadCount,
            isInbound: isInboundMessage
          });
          
          // Update conversation metadata
          updated[existingIndex] = {
            ...existingConv,
            lastMessageAt: message.timestamp,
            lastMessageContent: extractPreviewText(message.content, message.messageType),
            messageCount: existingConv.messageCount + 1,
            unreadCount: newUnreadCount,
            updatedAt: message.timestamp
          };
          
          // Move updated conversation to top of list (most recent first)
          const [updatedConv] = updated.splice(existingIndex, 1);
          return [updatedConv, ...updated];
        }
        
        // If conversation doesn't exist in list, we might need to refetch
        // This can happen if it's a new conversation or outside current page
        console.log('🔔 New message for conversation not in current list, may need refresh');
        return prev;
      });
    },
    
    onMessageSent: (message: ConversationMessage) => {
      console.log('📤 Conversation list: Message sent for conversation:', message.conversationId);
      
      // Update conversation for sent messages (similar to new messages but don't increment unread)
      setConversations(prev => {
        const existingIndex = prev.findIndex(conv => conv.conversationId === message.conversationId);
        
        if (existingIndex >= 0) {
          const updated = [...prev];
          const existingConv = updated[existingIndex];
          
          // Update conversation metadata for sent message
          updated[existingIndex] = {
            ...existingConv,
            lastMessageAt: message.timestamp,
            lastMessageContent: extractPreviewText(message.content, message.messageType),
            messageCount: existingConv.messageCount + 1,
            // Don't increment unread count for sent messages
            updatedAt: message.timestamp
          };
          
          // Move updated conversation to top of list
          const [updatedConv] = updated.splice(existingIndex, 1);
          return [updatedConv, ...updated];
        }
        
        return prev;
      });
    },
    
    onConversationUpdated: (conversation: Conversation) => {
      console.log('💬 Conversation list: Conversation updated:', conversation.conversationId);
      
      // Update specific conversation data
      setConversations(prev => {
        const existingIndex = prev.findIndex(conv => conv.conversationId === conversation.conversationId);
        
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = {
            ...updated[existingIndex],
            ...conversation,
          };
          return updated;
        }
        
        return prev;
      });
    },
    
    onError: (error: string) => {
      console.error('❌ Conversation list subscription error:', error);
      setError(prev => prev ? `${prev}; ${error}` : error);
    }
  }), []);

  // Set up organization-wide subscriptions for conversation list updates
  const {
    isConnected: subscriptionConnected,
    errors: subscriptionErrors,
    reconnect: reconnectSubscriptions
  } = useChatSubscriptions(subscriptionCallbacks, {
    organizationId: user?.organizationId,
    enabled: !!user?.organizationId
  });

  // Load more conversations (pagination)
  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    await fetchConversations(false);
  }, [fetchConversations, hasMore, loading]);

  // Refetch conversations (reset)
  const refetch = useCallback(async () => {
    setNextToken(undefined);
    setHasMore(true);
    await fetchConversations(true);
  }, [fetchConversations]);

  // Initial load and when filter or user changes
  useEffect(() => {
    if (user?.organizationId) {
      refetch();
    }
  }, [filter, user?.organizationId]);

  // Function to clear unread count for a specific conversation
  const clearUnreadCount = useCallback((conversationId: string) => {
    console.log('🔄 Clearing unread count for conversation:', conversationId);
    setConversations(prev => 
      prev.map(conv => {
        if (conv.conversationId === conversationId) {
          console.log('🔄 Cleared unread count:', { conversationId, oldCount: conv.unreadCount });
          return { ...conv, unreadCount: 0 };
        }
        return conv;
      })
    );
  }, []);

  return {
    conversations,
    loading,
    error: error || (subscriptionErrors.length > 0 ? subscriptionErrors.join(', ') : null),
    hasMore,
    loadMore,
    refetch,
    clearUnreadCount,
    // Add subscription status for debugging
    subscriptionConnected,
    reconnectSubscriptions
  };
}; 