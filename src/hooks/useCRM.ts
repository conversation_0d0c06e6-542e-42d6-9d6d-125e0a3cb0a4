import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import { 
  listCustomers,
  Customer, 
  CustomerFilterInput,
  FrontendFilters
} from '@/lib/graphql/queries/crmQueries';
import {
  createCustomer,
  updateCustomer,
  deleteCustomer as deleteCustomerMutation,
  CreateCustomerInput,
  UpdateCustomerInput
} from '@/lib/graphql/mutations/crmMutations';

export interface UseCRMResult {
  customers: Customer[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  
  // Data operations
  fetchCustomers: (filters?: CustomerFilterInput & FrontendFilters) => Promise<void>;
  refreshCustomers: () => Promise<void>;
  
  // CRUD operations
  createNewCustomer: (input: CreateCustomerInput) => Promise<Customer>;
  updateExistingCustomer: (input: UpdateCustomerInput) => Promise<Customer>;
  deleteCustomer: (businessPhone: string, customerPhone: string) => Promise<boolean>;
  
  // Frontend filtering
  filterByBusinessPhone: (businessPhone: string) => void;
  filterByStoreId: (storeId: string) => void;
  searchCustomers: (query: string) => void;
  clearFilters: () => void;
}

export const useCRM = (): UseCRMResult => {
  const { user } = useAuth();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  
  // Frontend filter states
  const [frontendFilters, setFrontendFilters] = useState<FrontendFilters>({});
  const lastGraphQLFiltersRef = useRef<CustomerFilterInput>({});
  const hasInitializedRef = useRef(false);

  // Apply frontend filters to customer list - stable function
  const applyFrontendFilters = useCallback((customerList: Customer[], filters: FrontendFilters) => {
    let filtered = [...customerList];

    // Filter by business phone
    if (filters.businessPhone) {
      filtered = filtered.filter(customer => 
        (customer.display_phone_number || customer.businessPhone || '').includes(filters.businessPhone!)
      );
    }

    // Filter by store ID
    if (filters.storeId) {
      filtered = filtered.filter(customer => 
        customer.shopify_store_id === filters.storeId ||
        customer.shopify_customer_id?.includes(filters.storeId!)
      );
    }

    // Search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(customer => 
        customer.customer_name.toLowerCase().includes(query) ||
        customer.customer_phone.includes(query) ||
        (customer.tags || []).some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, []);

  // Fetch customers from GraphQL (organization-scoped) - stable function
  const fetchCustomers = useCallback(async (filters: CustomerFilterInput & FrontendFilters = {}) => {
    if (!user?.organizationId) {
      console.warn('❌ CRM: No organization ID found in user:', user);
      setError('User organization not found. Please ensure you are properly logged in.');
      return;
    }

    console.log('🔍 CRM: Fetching customers for organization:', user.organizationId);
    setLoading(true);
    setError(null);

    try {
      // For now, we'll fetch all customers and filter on the frontend
      // This should be replaced with a more efficient paginated query
      const result = await listCustomers({ organizationId: user.organizationId });
      
      console.log('✅ CRM: Received customers:', result.items.length, 'total:', result.totalCount);
      setCustomers(result.items);
      setTotalCount(result.totalCount);
      
      // Apply frontend filters immediately
      const filtered = applyFrontendFilters(result.items, filters);
      setFilteredCustomers(filtered);
      setFrontendFilters(filters);
      
    } catch (err) {
      console.error('❌ CRM: Error fetching customers:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch customers');
    } finally {
      setLoading(false);
    }
  }, [user?.organizationId, applyFrontendFilters]);

  // Refresh with same filters
  const refreshCustomers = useCallback(async () => {
    await fetchCustomers({ ...lastGraphQLFiltersRef.current, ...frontendFilters });
  }, [fetchCustomers, frontendFilters]);

  // CRUD operations
  const createNewCustomer = useCallback(async (input: CreateCustomerInput): Promise<Customer> => {
    setLoading(true);
    try {
      const newCustomer = await createCustomer(input);
      await refreshCustomers(); // Refresh the list
      return newCustomer;
    } catch (err) {
      console.error('Error creating customer:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [refreshCustomers]);

  const updateExistingCustomer = useCallback(async (input: UpdateCustomerInput): Promise<Customer> => {
    setLoading(true);
    try {
      const updatedCustomer = await updateCustomer(input);
      await refreshCustomers(); // Refresh the list
      return updatedCustomer;
    } catch (err) {
      console.error('Error updating customer:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [refreshCustomers]);

  const deleteCustomer = useCallback(async (businessPhone: string, customerPhone: string): Promise<boolean> => {
    setLoading(true);
    try {
      await deleteCustomerMutation(businessPhone, customerPhone);
      await refreshCustomers(); // Refresh the list
      return true;
    } catch (err) {
      console.error('Error deleting customer:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [refreshCustomers]);

  // Frontend filtering functions - these update filters and re-apply them
  const filterByBusinessPhone = useCallback((businessPhone: string) => {
    const newFilters = { ...frontendFilters, businessPhone };
    setFrontendFilters(newFilters);
    const filtered = applyFrontendFilters(customers, newFilters);
    setFilteredCustomers(filtered);
  }, [frontendFilters, customers, applyFrontendFilters]);

  const filterByStoreId = useCallback((storeId: string) => {
    const newFilters = { ...frontendFilters, storeId };
    setFrontendFilters(newFilters);
    const filtered = applyFrontendFilters(customers, newFilters);
    setFilteredCustomers(filtered);
  }, [frontendFilters, customers, applyFrontendFilters]);

  const searchCustomersLocal = useCallback((query: string) => {
    const newFilters = { ...frontendFilters, searchQuery: query };
    setFrontendFilters(newFilters);
    const filtered = applyFrontendFilters(customers, newFilters);
    setFilteredCustomers(filtered);
  }, [frontendFilters, customers, applyFrontendFilters]);

  const clearFilters = useCallback(() => {
    setFrontendFilters({});
    setFilteredCustomers(customers);
  }, [customers]);

  // Initial data fetch
  useEffect(() => {
    if (user?.organizationId && !hasInitializedRef.current) {
      console.log('🚀 CRM: Initial load for organization:', user.organizationId);
      fetchCustomers();
      hasInitializedRef.current = true;
    }
  }, [user?.organizationId, fetchCustomers]);

  // Return filtered customers instead of all customers
  return {
    customers: filteredCustomers, // Return filtered list
    loading,
    error,
    totalCount,
    fetchCustomers,
    refreshCustomers,
    createNewCustomer,
    updateExistingCustomer,
    deleteCustomer,
    filterByBusinessPhone,
    filterByStoreId,
    searchCustomers: searchCustomersLocal,
    clearFilters
  };
}; 