// @ts-nocheck
// GraphQL hooks for AWS Amplify integration

import { useState, useEffect, useCallback, useRef } from 'react';
import { graphqlClient, executeGraphQLOperation, checkAuthentication } from '../lib/graphql-client';
import type {
  User,
  WhatsAppConnection,
  // Conversation and Message types removed - will be rebuilt with unified system
  ShopifyStore,
  CustomerContext,
  UserPreferencesInput,
  SendMessageInput,
  CreateWhatsAppConnectionInput,
  UpdateWhatsAppConnectionInput,
  ConnectShopifyStoreInput
} from '../lib/graphql-operations';
import {
  GET_CURRENT_USER,
  GET_WHATSAPP_CONNECTIONS,
  GET_WHATSAPP_CONNECTION,
  GET_SHOPIFY_STORES,
  GET_ANALYTICS_OVERVIEW,
  GET_CUSTOMER_CONTEXT,
  LIST_CUSTOMERS,
  UPDATE_USER_PREFERENCES,
  CREATE_WHATSAPP_CONNECTION,
  UPDATE_WHATSAPP_CONNECTION,
  CONNECT_SHOPIFY_STORE,
  ON_CONNECTION_STATUS_CHANGED,
  ON_USER_PRESENCE_CHANGED,
  GET_USER_SHOPIFY_STORES,
  GET_USER_WHATSAPP_CONNECTIONS
} from '../lib/graphql-operations';

// ============================================================================
// Common Hook Types
// ============================================================================

interface UseQueryResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseMutationResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: (variables?: any) => Promise<T | null>;
}

interface UseSubscriptionResult<T> {
  data: T | null;
  error: string | null;
  connected: boolean;
}

// ============================================================================
// User Hooks
// ============================================================================

export const useCurrentUser = (): UseQueryResult<User> => {
  const [data, setData] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fetchingRef = useRef(false);

  const fetchUser = useCallback(async () => {
    if (fetchingRef.current) return;
    
    try {
      fetchingRef.current = true;
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({ query: GET_CURRENT_USER })
      );
      setData(result.data?.getCurrentUser || null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch user');
      setData(null);
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  }, []);

  useEffect(() => {
    fetchUser();
  }, []); // Remove fetchUser from dependencies to prevent re-runs

  return { data, loading, error, refetch: fetchUser };
};

export const useUpdateUserPreferences = (): UseMutationResult<User> => {
  const [data, setData] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (preferences: UserPreferencesInput): Promise<User | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: UPDATE_USER_PREFERENCES,
          variables: { preferences }
        })
      );
      const userData = result.data?.updateUserPreferences;
      setData(userData);
      return userData;
    } catch (err: any) {
      setError(err.message || 'Failed to update preferences');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, execute };
};

// ============================================================================
// WhatsApp Connection Hooks
// ============================================================================

export const useWhatsAppConnections = (organizationId?: string, status?: string): UseQueryResult<WhatsAppConnection[]> => {
  const [data, setData] = useState<WhatsAppConnection[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConnections = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({ 
          query: GET_WHATSAPP_CONNECTIONS,
          variables: { organizationId, status }
        })
      );
      setData(result.data?.listWhatsAppConnections || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch WhatsApp connections');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [organizationId, status]);

  useEffect(() => {
    fetchConnections();
  }, [fetchConnections]);

  return { data, loading, error, refetch: fetchConnections };
};

export const useUserWhatsAppConnections = (): UseQueryResult<WhatsAppConnection[]> => {
  const [data, setData] = useState<WhatsAppConnection[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserConnections = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if user is authenticated before making the query
      const isAuthenticated = await checkAuthentication();
      if (!isAuthenticated) {
        console.log('🔐 User not authenticated, skipping WhatsApp connections query');
        setData([]);
        setLoading(false);
        return;
      }
      
      console.log('🔍 Fetching WhatsApp connections with authentication...');
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({ query: GET_USER_WHATSAPP_CONNECTIONS })
      );
      const connectionsData = result.data?.getUserWhatsAppConnections || [];
      console.log('✅ WhatsApp connections fetched successfully:', connectionsData.length);
      
      setData(connectionsData);
    } catch (err: any) {
      console.error('❌ Error fetching WhatsApp connections:', err);
      setError(err.message || 'Failed to fetch user WhatsApp connections');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUserConnections();
  }, [fetchUserConnections]);

  return { data, loading, error, refetch: fetchUserConnections };
};

export const useWhatsAppConnection = (connectionId: string): UseQueryResult<WhatsAppConnection> => {
  const [data, setData] = useState<WhatsAppConnection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConnection = useCallback(async () => {
    if (!connectionId) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: GET_WHATSAPP_CONNECTION,
          variables: { connectionId }
        })
      );
      setData(result.data?.getWhatsAppConnection || null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch WhatsApp connection');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [connectionId]);

  useEffect(() => {
    fetchConnection();
  }, [fetchConnection]);

  return { data, loading, error, refetch: fetchConnection };
};

export const useCreateWhatsAppConnection = (): UseMutationResult<WhatsAppConnection> => {
  const [data, setData] = useState<WhatsAppConnection | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (input: CreateWhatsAppConnectionInput): Promise<WhatsAppConnection | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: CREATE_WHATSAPP_CONNECTION,
          variables: { input }
        })
      );
      const connectionData = result.data?.createWhatsAppConnection;
      setData(connectionData);
      return connectionData;
    } catch (err: any) {
      setError(err.message || 'Failed to create WhatsApp connection');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, execute };
};

// ============================================================================
// Conversation Hooks
// ============================================================================

// OLD CONVERSATION HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

// OLD CONVERSATION MESSAGES HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

// OLD SEND MESSAGE HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

// ============================================================================
// Shopify Store Hooks
// ============================================================================

export const useShopifyStores = (organizationId?: string, status?: string): UseQueryResult<ShopifyStore[]> => {
  const [data, setData] = useState<ShopifyStore[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStores = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({ 
          query: GET_SHOPIFY_STORES,
          variables: { organizationId, status }
        })
      );
      setData(result.data?.listShopifyStores || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch Shopify stores');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [organizationId, status]);

  useEffect(() => {
    fetchStores();
  }, [fetchStores]);

  return { data, loading, error, refetch: fetchStores };
};

export const useUserShopifyStores = (): UseQueryResult<ShopifyStore[]> => {
  const [data, setData] = useState<ShopifyStore[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserStores = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({ query: GET_USER_SHOPIFY_STORES })
      );
      setData(result.data?.getUserShopifyStores || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch user Shopify stores');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUserStores();
  }, [fetchUserStores]);

  return { data, loading, error, refetch: fetchUserStores };
};

export const useConnectShopifyStore = (): UseMutationResult<ShopifyStore> => {
  const [data, setData] = useState<ShopifyStore | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async (input: ConnectShopifyStoreInput): Promise<ShopifyStore | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: CONNECT_SHOPIFY_STORE,
          variables: { input }
        })
      );
      const storeData = result.data?.connectShopifyStore;
      setData(storeData);
      return storeData;
    } catch (err: any) {
      setError(err.message || 'Failed to connect Shopify store');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, execute };
};

// ============================================================================
// Real-time Subscription Hooks
// ============================================================================

// OLD MESSAGE SUBSCRIPTION HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

// OLD CONVERSATION SUBSCRIPTION HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

export const useConnectionStatusSubscription = (userId?: string): UseSubscriptionResult<WhatsAppConnection> => {
  const [data, setData] = useState<WhatsAppConnection | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if (!userId) return;

    const subscription = graphqlClient.graphql({
      query: ON_CONNECTION_STATUS_CHANGED,
      variables: { userId }
    }).subscribe({
      next: (result: any) => {
        if (result.data?.onConnectionStatusChanged) {
          setData(result.data.onConnectionStatusChanged);
          setConnected(true);
          setError(null);
        }
      },
      error: (err: any) => {
        console.error('Connection status subscription error:', err);
        setError(err.message || 'Subscription error');
        setConnected(false);
      },
      complete: () => {
        setConnected(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [userId]);

  return { data, error, connected };
};

// ============================================================================
// Combined Real-time Hook for Chat Interface
// ============================================================================

export interface ChatSubscriptions {
  newMessage: Message | null;
  conversationUpdate: Conversation | null;
  connectionStatus: WhatsAppConnection | null;
  connected: boolean;
  errors: string[];
}

// OLD CHAT SUBSCRIPTIONS HOOK REMOVED - REPLACED BY NEW CHAT SYSTEM

// ============================================================================
// Analytics Hook
// ============================================================================

export const useAnalytics = (timeRange: string): UseQueryResult<any> => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: GET_ANALYTICS_OVERVIEW,
          variables: { timeRange }
        })
      );
      setData(result.data?.getAnalyticsOverview || null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch analytics');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return { data, loading, error, refetch: fetchAnalytics };
};

// ============================================================================
// CRM Hooks
// ============================================================================

export const useCustomerContext = (businessPhone?: string, customerPhone?: string): UseQueryResult<CustomerContext> => {
  const [data, setData] = useState<CustomerContext | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCustomerContext = useCallback(async () => {
    if (!businessPhone || !customerPhone) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: GET_CUSTOMER_CONTEXT,
          variables: { businessPhone, customerPhone }
        })
      );
      
      setData(result.data?.getCustomerContext || null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch customer context');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [businessPhone, customerPhone]);

  useEffect(() => {
    fetchCustomerContext();
  }, [fetchCustomerContext]);

  return { data, loading, error, refetch: fetchCustomerContext };
};

export const useCustomers = (organizationId?: string): UseQueryResult<CustomerContext[]> => {
  const [data, setData] = useState<CustomerContext[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCustomers = useCallback(async () => {
    if (!organizationId) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: LIST_CUSTOMERS,
          variables: { organizationId, limit: 100 }
        })
      );
      
      setData(result.data?.listCustomers || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch customers');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return { data, loading, error, refetch: fetchCustomers };
}; 