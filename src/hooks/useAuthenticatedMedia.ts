import { useState, useCallback, useEffect } from 'react';
import { getMediaUrl, downloadMedia, isProxyUrl } from '@/utils/mediaUtils';

interface UseMediaProps {
  mediaUrl?: string;
  organizationId?: string;
  phoneId?: string;
  mediaId?: string;
}

interface UseMediaReturn {
  finalUrl: string | null;
  isLoading: boolean;
  error: string | null;
  download: (filename?: string) => Promise<void>;
  createBlobUrl: () => Promise<string | null>;
}

/**
 * Hook for handling media URLs - simplified for permanent proxy URLs
 * Automatically processes media URLs and provides download functionality
 */
export function useMedia({
  mediaUrl,
  organizationId,
  phoneId,
  mediaId
}: UseMediaProps): UseMediaReturn {
  const [finalUrl, setFinalUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate final media URL
  const generateFinalUrl = useCallback(async () => {
    if (!mediaUrl) {
      setFinalUrl(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const url = await getMediaUrl(mediaUrl, organizationId, phoneId, mediaId);
      setFinalUrl(url);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process media URL';
      setError(errorMessage);
      console.error('❌ Error processing media URL:', err);
      // Fallback to original URL
      setFinalUrl(mediaUrl);
    } finally {
      setIsLoading(false);
    }
  }, [mediaUrl, organizationId, phoneId, mediaId]);

  // Download function
  const download = useCallback(async (filename?: string) => {
    if (!mediaUrl) return;

    try {
      await downloadMedia(mediaUrl, filename, organizationId, phoneId, mediaId);
    } catch (err) {
      console.error('❌ Error downloading media:', err);
      throw err;
    }
  }, [mediaUrl, organizationId, phoneId, mediaId]);

  // Create blob URL for media that needs authentication headers
  // NOTE: For proxy URLs, this is usually not needed since they're permanent and public
  const createBlobUrl = useCallback(async (): Promise<string | null> => {
    if (!finalUrl) return null;

    // For proxy URLs, we can return them directly since they're permanent
    if (isProxyUrl(finalUrl)) {
      return finalUrl;
    }

    try {
      const response = await fetch(finalUrl);
      if (response.ok) {
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      } else {
        console.warn('⚠️  Failed to fetch media for blob creation:', response.status);
        return finalUrl; // Fallback to direct URL
      }
    } catch (err) {
      console.error('❌ Error creating blob URL:', err);
      return finalUrl; // Fallback to direct URL
    }
  }, [finalUrl]);

  // Generate final URL when dependencies change
  useEffect(() => {
    generateFinalUrl();
  }, [generateFinalUrl]);

  return {
    finalUrl,
    isLoading,
    error,
    download,
    createBlobUrl
  };
}

/**
 * Hook specifically for images that need blob URLs for display
 * Simplified for permanent proxy URLs
 */
export function useMediaImage({
  mediaUrl,
  organizationId,
  phoneId,
  mediaId
}: UseMediaProps) {
  const { finalUrl, isLoading, error, createBlobUrl } = useMedia({
    mediaUrl,
    organizationId,
    phoneId,
    mediaId
  });

  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(false);

  // Create image URL when final URL is ready
  useEffect(() => {
    if (!finalUrl || isLoading) return;

    // For proxy URLs, use them directly (no need for blob URLs)
    if (isProxyUrl(finalUrl)) {
      setImageUrl(finalUrl);
      return;
    }

    // For other URLs, create blob URL if needed
    setImageLoading(true);
    createBlobUrl()
      .then((url) => {
        setImageUrl(url);
      })
      .catch((err) => {
        console.error('❌ Error creating image URL:', err);
        setImageUrl(finalUrl); // Fallback to final URL
      })
      .finally(() => {
        setImageLoading(false);
      });

    // Cleanup function to revoke blob URL
    return () => {
      if (imageUrl && imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [finalUrl, isLoading, createBlobUrl]);

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (imageUrl && imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imageUrl]);

  return {
    imageUrl,
    isLoading: isLoading || imageLoading,
    error
  };
}

// Legacy exports for backward compatibility
export const useAuthenticatedMedia = useMedia;
export const useAuthenticatedImage = useMediaImage;

/*
// Extract media ID from URL patterns - unused function
function extractMediaIdFromUrl(url?: string): string | undefined {
  if (!url) return undefined;

  // Try to extract from S3 URL pattern
  if (url.includes('.s3.') && url.includes('.amazonaws.com/')) {
    const pathParts = url.split('/');
    if (pathParts.length >= 6) {
      return pathParts[pathParts.length - 2]; // media_id is second to last part
    }
  }

  // Try to extract from proxy URL pattern
  if (url.includes('/apis/v1/media/')) {
    const match = url.match(/\/apis\/v1\/media\/[^\/]+\/[^\/]+\/([^\/\?]+)/);
    return match?.[1];
  }

  return undefined;
}
*/ 