import { useState, useCallback } from 'react';
import { generateClient } from 'aws-amplify/api';
import {
  PROCESS_WHATSAPP_EMBEDDED_SIGNUP,
  CONNECT_WHATSAPP_TO_STORE,
  CREATE_WHATSAPP_CONNECTION,
  UPDATE_WHATSAPP_CONNECTION,
  DELETE_WHATSAPP_CONNECTION,
  VERIFY_WHATSAPP_CONNECTION,
  type WhatsAppEmbeddedSignupInput,
  type WhatsAppEmbeddedSignupResult,
  type WhatsAppStoreConnectionInput,
  type CreateWhatsAppConnectionInput,
  type UpdateWhatsAppConnectionInput
} from '@/lib/graphql/mutations/whatsapp';
import {
  GET_WHATSAPP_CONNECTION,
  GET_USER_WHATSAPP_CONNECTIONS,
  LIST_WHATSAPP_CONNECTIONS,
  type WhatsAppConnection
} from '@/lib/graphql/queries/whatsapp';

const client = generateClient();

export interface UseWhatsAppReturn {
  // State
  isLoading: boolean;
  error: string | null;
  
  // Operations
  processEmbeddedSignup: (input: WhatsAppEmbeddedSignupInput) => Promise<WhatsAppEmbeddedSignupResult | null>;
  connectToStore: (input: WhatsAppStoreConnectionInput) => Promise<any>;
  createConnection: (input: CreateWhatsAppConnectionInput) => Promise<WhatsAppConnection | null>;
  updateConnection: (id: string, input: UpdateWhatsAppConnectionInput) => Promise<WhatsAppConnection | null>;
  deleteConnection: (id: string) => Promise<boolean>;
  verifyConnection: (id: string) => Promise<WhatsAppConnection | null>;
  getConnection: (id: string) => Promise<WhatsAppConnection | null>;
  getUserConnections: () => Promise<WhatsAppConnection[]>;
  listConnections: (organizationId?: string, status?: string) => Promise<WhatsAppConnection[]>;
  
  // Utils
  clearError: () => void;
}

export const useWhatsApp = (): UseWhatsAppReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const processEmbeddedSignup = useCallback(async (input: WhatsAppEmbeddedSignupInput): Promise<WhatsAppEmbeddedSignupResult | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: PROCESS_WHATSAPP_EMBEDDED_SIGNUP,
        variables: { input }
      });

      if ('data' in result && result.data?.processWhatsAppEmbeddedSignup) {
        const data = result.data.processWhatsAppEmbeddedSignup;
        
        if (!data.success && data.errorMessage) {
          setError(data.errorMessage);
          return data;
        }
        
        return data;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process embedded signup';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const connectToStore = useCallback(async (input: WhatsAppStoreConnectionInput) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: CONNECT_WHATSAPP_TO_STORE,
        variables: { input }
      });

      if ('data' in result && result.data?.connectWhatsAppToStore) {
        const data = result.data.connectWhatsAppToStore;
        
        if (!data.success && data.message) {
          setError(data.message);
        }
        
        return data;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect to store';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createConnection = useCallback(async (input: CreateWhatsAppConnectionInput): Promise<WhatsAppConnection | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: CREATE_WHATSAPP_CONNECTION,
        variables: { input }
      });

      if ('data' in result && result.data?.createWhatsAppConnection) {
        return result.data.createWhatsAppConnection;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create connection';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateConnection = useCallback(async (id: string, input: UpdateWhatsAppConnectionInput): Promise<WhatsAppConnection | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: UPDATE_WHATSAPP_CONNECTION,
        variables: { id, input }
      });

      if ('data' in result && result.data?.updateWhatsAppConnection) {
        return result.data.updateWhatsAppConnection;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update connection';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteConnection = useCallback(async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: DELETE_WHATSAPP_CONNECTION,
        variables: { id }
      });

      if ('data' in result && result.data?.deleteWhatsAppConnection !== undefined) {
        return result.data.deleteWhatsAppConnection;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete connection';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const verifyConnection = useCallback(async (id: string): Promise<WhatsAppConnection | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: VERIFY_WHATSAPP_CONNECTION,
        variables: { id }
      });

      if ('data' in result && result.data?.verifyWhatsAppConnection) {
        return result.data.verifyWhatsAppConnection;
      }
      
      throw new Error('No response data received');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify connection';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getConnection = useCallback(async (id: string): Promise<WhatsAppConnection | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: GET_WHATSAPP_CONNECTION,
        variables: { id }
      });

      if ('data' in result && result.data?.getWhatsAppConnection) {
        return result.data.getWhatsAppConnection;
      }
      
      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get connection';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getUserConnections = useCallback(async (): Promise<WhatsAppConnection[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: GET_USER_WHATSAPP_CONNECTIONS
      });

      if ('data' in result && result.data?.getUserWhatsAppConnections) {
        return result.data.getUserWhatsAppConnections;
      }
      
      return [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get user connections';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  const listConnections = useCallback(async (organizationId?: string, status?: string): Promise<WhatsAppConnection[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await client.graphql({
        query: LIST_WHATSAPP_CONNECTIONS,
        variables: { 
          organizationId,
          status: status as any
        }
      });

      if ('data' in result && result.data?.listWhatsAppConnections) {
        return result.data.listWhatsAppConnections;
      }
      
      return [];
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to list connections';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    processEmbeddedSignup,
    connectToStore,
    createConnection,
    updateConnection,
    deleteConnection,
    verifyConnection,
    getConnection,
    getUserConnections,
    listConnections,
    clearError
  };
}; 