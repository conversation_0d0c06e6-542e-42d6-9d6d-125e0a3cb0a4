// @ts-nocheck
// Authentication hook for Notefy Platform

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  authService, 
  type EnterpriseUser,
  type AuthResult,
  type SignUpResult,
  type PasswordResetResult
} from '@/lib/auth';
import { 
  type AuthState, 
  type SignUpData, 
  type SignInData,
  type ResetPasswordData,
  type ConfirmResetPasswordData,
  featureFlags
} from '@/lib/aws-config';

// Enhanced auth state for enterprise features
interface EnterpriseAuthState extends Omit<AuthState, 'user'> {
  user: EnterpriseUser | null;
  mfaRequired: boolean;
  challengeName?: string;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<EnterpriseAuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    error: null,
    mfaRequired: false,
  });

  // Prevent multiple simultaneous auth checks
  const isCheckingRef = useRef(false);
  const hasInitializedRef = useRef(false);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      if (isCheckingRef.current || hasInitializedRef.current) {
        return;
      }

      isCheckingRef.current = true;

      try {
        const user = await authService.getCurrentUser();
        setAuthState({
          isAuthenticated: !!user,
          isLoading: false,
          user,
          error: null,
          mfaRequired: false,
        });
      } catch (error: any) {
        console.error('Auth initialization error:', error);
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          error: null,
          mfaRequired: false,
        });
      } finally {
        isCheckingRef.current = false;
        hasInitializedRef.current = true;
      }
    };

    initializeAuth();
  }, []);

  // Sign up with enhanced error handling
  const signUp = useCallback(async (data: SignUpData): Promise<SignUpResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.signUp(data);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Sign up failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return {
        success: false,
        isSignUpComplete: false,
        error: errorMessage,
      };
    }
  }, []);

  // Confirm sign up
  const confirmSignUp = useCallback(async (username: string, confirmationCode: string): Promise<SignUpResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.confirmSignUp(username, confirmationCode);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Email verification failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return {
        success: false,
        isSignUpComplete: false,
        error: errorMessage,
      };
    }
  }, []);

  // Resend confirmation code
  const resendConfirmationCode = useCallback(async (username: string): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.resendSignUpCode(username);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return result.success;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to resend confirmation code'
      }));
      return false;
    }
  }, []);

  // Enhanced sign in with MFA support
  const signIn = useCallback(async (data: SignInData): Promise<AuthResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.signIn(data);
      
      if (result.success && result.user) {
        setAuthState({
          isAuthenticated: true,
          isLoading: false,
          user: result.user,
          error: null,
          mfaRequired: false,
        });
      } else if (result.requiresMFA) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          mfaRequired: true,
          challengeName: result.challengeName,
        }));
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Sign in failed',
        }));
      }
      
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Sign in failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  // Confirm MFA challenge
  const confirmSignIn = useCallback(async (challengeResponse: string): Promise<AuthResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.confirmSignIn(challengeResponse);
      
      if (result.success && result.user) {
        setAuthState({
          isAuthenticated: true,
          isLoading: false,
          user: result.user,
          error: null,
          mfaRequired: false,
        });
      } else if (result.requiresMFA) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          mfaRequired: true,
          challengeName: result.challengeName,
        }));
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'MFA confirmation failed',
        }));
      }
      
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'MFA confirmation failed';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  // Sign out with enhanced cleanup
  const signOut = useCallback(async (global: boolean = false): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.signOut(global);
      
      if (result.success) {
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          error: null,
          mfaRequired: false,
        });
        return true;
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Sign out failed'
        }));
        return false;
      }
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Sign out failed'
      }));
      return false;
    }
  }, []);

  // ✅ FORCE REFRESH: Refresh user attributes from Cognito
  const refreshUserAttributes = useCallback(async (): Promise<EnterpriseUser | null> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const user = await authService.refreshUserAttributes();
      
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: !!user,
        isLoading: false,
        user,
        error: null,
        mfaRequired: false,
      }));
      
      return user;
    } catch (error: any) {
      console.error('❌ Failed to refresh user attributes:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to refresh user attributes'
      }));
      return null;
    }
  }, []);

  // Social sign in
  const signInWithRedirect = useCallback(async (provider: 'Google' | 'Amazon'): Promise<boolean> => {
    if (!featureFlags.socialLogin) {
      setAuthState(prev => ({
        ...prev,
        error: 'Social login is not available in this environment'
      }));
      return false;
    }

    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.signInWithRedirect(provider);
      if (!result.success) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Social login failed'
        }));
      }
      return result.success;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Social login failed'
      }));
      return false;
    }
  }, []);

  // Password reset
  const forgotPassword = useCallback(async (email: string): Promise<PasswordResetResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.resetPassword({ email });
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to send reset code';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return {
        success: false,
        isPasswordReset: false,
        error: errorMessage,
      };
    }
  }, []);

  // Confirm password reset
  const confirmPasswordReset = useCallback(async (data: ConfirmResetPasswordData): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.confirmResetPassword(data);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      
      if (!result.success && result.error) {
        setAuthState(prev => ({ ...prev, error: result.error! }));
      }
      
      return result.success;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to reset password'
      }));
      return false;
    }
  }, []);

  // Update user profile
  const updateUserProfile = useCallback(async (attributes: Record<string, string>): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      let allSuccessful = true;
      
      for (const [key, value] of Object.entries(attributes)) {
        const result = await authService.updateUserAttribute(key, value);
        if (!result.success) {
          allSuccessful = false;
          setAuthState(prev => ({ ...prev, error: result.error || 'Failed to update profile' }));
          break;
        }
      }
      
      if (allSuccessful) {
        // Refresh user data
        const updatedUser = await authService.getCurrentUser();
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          user: updatedUser,
        }));
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
      
      return allSuccessful;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to update profile'
      }));
      return false;
    }
  }, []);

  // Change password
  const changePassword = useCallback(async (oldPassword: string, newPassword: string): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.updatePassword(oldPassword, newPassword);
      setAuthState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Failed to change password'
      }));
      return result.success;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to change password'
      }));
      return false;
    }
  }, []);

  // MFA setup (enterprise feature)
  const setupMFA = useCallback(async (): Promise<string | null> => {
    if (!featureFlags.mfaEnabled) {
      setAuthState(prev => ({
        ...prev,
        error: 'MFA is not available in this environment'
      }));
      return null;
    }

    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.setupTOTP();
      setAuthState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Failed to setup MFA'
      }));
      return result.success ? result.sharedSecret || null : null;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to setup MFA'
      }));
      return null;
    }
  }, []);

  // Confirm MFA setup
  const confirmMFA = useCallback(async (code: string): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.verifyTOTPSetup(code);
      setAuthState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: result.success ? null : result.error || 'Failed to confirm MFA'
      }));
      return result.success;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to confirm MFA'
      }));
      return false;
    }
  }, []);

  // Refresh session
  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      const session = await authService.getAuthSession();
      return !!session;
    } catch (error) {
      return false;
    }
  }, []);

  // Get current user info
  const getCurrentUserInfo = useCallback(async (): Promise<EnterpriseUser | null> => {
    try {
      return await authService.getCurrentUser();
    } catch (error) {
      return null;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // Validate password strength
  const validatePassword = useCallback((password: string) => {
    return authService.validatePasswordStrength(password);
  }, []);

  // Check user permissions
  const hasPermission = useCallback((permission: string): boolean => {
    if (!authState.user) return false;
    return authState.user.permissions.includes(permission);
  }, [authState.user]);

  // Check user group membership
  const hasGroup = useCallback((group: string): boolean => {
    if (!authState.user) return false;
    return authState.user.userGroups.includes(group);
  }, [authState.user]);

  return {
    // User state
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    loading: authState.isLoading, // Alias for backward compatibility
    error: authState.error,
    mfaRequired: authState.mfaRequired,
    challengeName: authState.challengeName,
    
    // Authentication methods
    signUp,
    confirmSignUp,
    resendConfirmationCode,
    signIn,
    confirmSignIn,
    signOut,
    refreshUserAttributes, // ✅ Add refresh method
    signInWithRedirect,
    forgotPassword,
    confirmPasswordReset,
    
    // User management
    getCurrentUser: getCurrentUserInfo,
    updateUserProfile,
    changePassword,
    
    // MFA methods (enterprise)
    setupMFA,
    confirmMFA,
    
    // Utility methods
    refreshSession,
    clearError,
    validatePassword,
    hasPermission,
    hasGroup,
    
    // Feature flags
    features: featureFlags,
  };
}; 