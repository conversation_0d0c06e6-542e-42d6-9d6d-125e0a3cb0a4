import { useState, useEffect, useCallback, useRef } from 'react';
import { executeSubscription, checkAuthentication } from '../lib/graphql-client';
import { ON_MESSAGE_NOTIFICATION, ON_CONVERSATION_UPDATED, ON_MESSAGE_SENT } from '../lib/graphql-operations';
import type { ConversationMessage } from './useConversationMessages';
import type { Conversation } from '../types/chat';

export interface ChatSubscriptionCallbacks {
  onNewMessage?: (message: ConversationMessage) => void;
  onMessageSent?: (message: ConversationMessage) => void;
  onConversationUpdated?: (conversation: Conversation) => void;
  onError?: (error: string) => void;
}

export interface UseChatSubscriptionsOptions {
  connectionId?: string;
  organizationId?: string;
  enabled?: boolean;
}

export interface UseChatSubscriptionsResult {
  isConnected: boolean;
  errors: string[];
  subscriptionCount: number;
  reconnect: () => void;
  disconnect: () => void;
}

/**
 * Hook to manage real-time chat subscriptions
 * This replaces the auto-refresh polling with true real-time updates
 */
export function useChatSubscriptions(
  callbacks: ChatSubscriptionCallbacks,
  options: UseChatSubscriptionsOptions = {}
): UseChatSubscriptionsResult {
  const [isConnected, setIsConnected] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [subscriptionCount, setSubscriptionCount] = useState(0);
  
  const subscriptionsRef = useRef<Array<{ unsubscribe: () => void }>>([]);
  const optionsRef = useRef(options);
  const callbacksRef = useRef(callbacks);
  
  // Update refs when props change
  useEffect(() => {
    optionsRef.current = options;
    callbacksRef.current = callbacks;
  }, [options, callbacks]);

  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting chat subscriptions...');
    subscriptionsRef.current.forEach(sub => {
      try {
        sub.unsubscribe();
      } catch (error) {
        console.warn('Error unsubscribing:', error);
      }
    });
    subscriptionsRef.current = [];
    setIsConnected(false);
    setSubscriptionCount(0);
  }, []);

  const connect = useCallback(async () => {
    const { connectionId, organizationId, enabled = true } = optionsRef.current;
    
    if (!enabled) {
      console.log('📴 Chat subscriptions disabled');
      return;
    }

    // Check authentication first
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
      console.warn('🔐 User not authenticated, skipping chat subscriptions');
      callbacksRef.current.onError?.('User not authenticated');
      return;
    }

    console.log('🔌 Connecting chat subscriptions...', { connectionId, organizationId });
    
    // Clear any existing subscriptions
    disconnect();
    setErrors([]);
    
    const newSubscriptions: Array<{ unsubscribe: () => void }> = [];
    let connectedCount = 0;

    try {
      // 1. Subscribe to new message notifications - Organization-wide scope
      if (organizationId) {
        console.log('📬 Setting up message notification subscription for organization:', organizationId);
        const messageSubscription = executeSubscription(
          {
            query: ON_MESSAGE_NOTIFICATION,
            variables: { organizationId }
          },
          {
            next: (data: any) => {
              console.log('📨 New message notification received:', data);
              if (data.onMessageNotification) {
                // Convert notification to full message format for compatibility
                const notification = data.onMessageNotification;
                const messageData = {
                  id: notification.id || notification.messageId, // Use id field first, fallback to messageId
                  messageId: notification.messageId,
                  conversationId: notification.conversationId,
                  messageType: notification.messageType, // Use messageType instead of type
                  content: notification.content,
                  direction: notification.direction,
                  timestamp: notification.timestamp,
                  contactName: notification.contactName,
                  organizationId: notification.organizationId,
                  // Add default values for missing fields to maintain compatibility
                  sortKey: `${notification.timestamp}#${notification.messageId}`,
                  businessPhone: '',
                  customerPhone: '',
                  status: notification.status || 'DELIVERED', // Use status from resolver
                  fromNumber: notification.fromNumber || '',
                  phoneNumberId: '',
                  createdAt: notification.timestamp,
                  updatedAt: notification.timestamp,
                  wabaId: '',
                  textContent: '',
                  mediaUrl: undefined,
                  mediaType: undefined,
                  caption: undefined
                };
                callbacksRef.current.onNewMessage?.(messageData);
              }
            },
            error: (errors: string[]) => {
              console.error('❌ Message notification subscription error:', errors);
              setErrors(prev => [...prev, ...errors]);
              callbacksRef.current.onError?.(errors.join(', '));
            },
            complete: () => {
              console.log('📬 Message notification subscription completed');
            }
          }
        );
        newSubscriptions.push(messageSubscription);
        connectedCount++;
      }

      // 2. Subscribe to conversation updates
      if (connectionId) {
        console.log('💬 Setting up conversation updated subscription for connection:', connectionId);
        const conversationSubscription = executeSubscription(
          {
            query: ON_CONVERSATION_UPDATED,
            variables: { connectionId }
          },
          {
            next: (data: any) => {
              console.log('💬 Conversation updated:', data);
              if (data.onConversationUpdated) {
                callbacksRef.current.onConversationUpdated?.(data.onConversationUpdated);
              }
            },
            error: (errors: string[]) => {
              console.error('❌ Conversation subscription error:', errors);
              setErrors(prev => [...prev, ...errors]);
              callbacksRef.current.onError?.(errors.join(', '));
            },
            complete: () => {
              console.log('💬 Conversation subscription completed');
            }
          }
        );
        newSubscriptions.push(conversationSubscription);
        connectedCount++;
      }

      // 3. Subscribe to sent messages - Organization-wide scope
      if (organizationId) {
        console.log('📤 Setting up message sent subscription for organization:', organizationId);
        const sentMessageSubscription = executeSubscription(
          {
            query: ON_MESSAGE_SENT,
            variables: { organizationId }
          },
          {
            next: (data: any) => {
              console.log('📤 Message sent:', data);
              if (data.onMessageSent?.success && data.onMessageSent?.message) {
                callbacksRef.current.onMessageSent?.(data.onMessageSent.message);
              } else if (data.onMessageSent?.error) {
                console.error('❌ Message send error:', data.onMessageSent.error);
              }
            },
            error: (errors: string[]) => {
              console.error('❌ Sent message subscription error:', errors);
              setErrors(prev => [...prev, ...errors]);
              callbacksRef.current.onError?.(errors.join(', '));
            },
            complete: () => {
              console.log('📤 Sent message subscription completed');
            }
          }
        );
        newSubscriptions.push(sentMessageSubscription);
        connectedCount++;
      }

      // Store subscriptions and update state
      subscriptionsRef.current = newSubscriptions;
      setSubscriptionCount(connectedCount);
      setIsConnected(connectedCount > 0);
      
      console.log(`✅ Chat subscriptions connected (${connectedCount} active)`);
      
    } catch (error) {
      console.error('❌ Failed to setup chat subscriptions:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown subscription error';
      setErrors(prev => [...prev, errorMessage]);
      callbacksRef.current.onError?.(errorMessage);
    }
  }, [disconnect]);

  const reconnect = useCallback(() => {
    console.log('🔄 Reconnecting chat subscriptions...');
    disconnect();
    setTimeout(connect, 1000); // Brief delay before reconnecting
  }, [connect, disconnect]);

  // Auto-connect when options change
  useEffect(() => {
    connect();
    
    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [options.connectionId, options.organizationId, options.enabled, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    errors,
    subscriptionCount,
    reconnect,
    disconnect,
  };
} 