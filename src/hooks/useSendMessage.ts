// @ts-nocheck
import { useState, useCallback, useRef } from 'react';
import { executeMutation } from '../lib/graphql-client';
import { SEND_MESSAGE, SendMessageInput, Message } from '../lib/graphql-operations';

interface OptimisticMessage extends Omit<Message, 'id' | 'whatsappMessageId' | 'createdAt' | 'updatedAt'> {
  id: string;
  tempId: string; // Temporary ID for tracking
  whatsappMessageId: string;
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
  createdAt: string;
  updatedAt: string;
  retryCount?: number;
}

interface UseSendMessageOptions {
  onOptimisticUpdate?: (message: OptimisticMessage) => void;
  onMessageStatusUpdate?: (tempId: string, status: OptimisticMessage['status'], error?: string) => void;
  onMessageConfirmed?: (tempId: string, confirmedMessage: Message) => void;
  maxRetries?: number;
  retryDelay?: number;
}

interface UseSendMessageReturn {
  sendMessage: (input: SendMessageInput) => Promise<OptimisticMessage>;
  sendMessageBatch: (inputs: SendMessageInput[]) => Promise<OptimisticMessage[]>;
  retryMessage: (tempId: string) => Promise<void>;
  loading: boolean;
  error: string | null;
  pendingMessages: Map<string, OptimisticMessage>;
}

export const useSendMessage = (options: UseSendMessageOptions = {}): UseSendMessageReturn => {
  const {
    onOptimisticUpdate,
    onMessageStatusUpdate,
    onMessageConfirmed,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingMessages, setPendingMessages] = useState<Map<string, OptimisticMessage>>(new Map());
  const messageQueue = useRef<OptimisticMessage[]>([]);
  const processingQueue = useRef(false);

  // Generate optimistic message
  const createOptimisticMessage = useCallback((input: SendMessageInput): OptimisticMessage => {
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();
    
    return {
      id: tempId,
      tempId,
      conversationId: input.conversationId,
             type: input.type?.toUpperCase() as OptimisticMessage['type'] || 'TEXT',
       content: input.content,
       mediaUrl: input.mediaUrl,
       direction: 'outbound',
      whatsappMessageId: `temp_${tempId}`,
      status: 'PENDING',
      createdAt: now,
      updatedAt: now,
      retryCount: 0
    };
  }, []);

  // Update message status
  const updateMessageStatus = useCallback((tempId: string, status: OptimisticMessage['status'], error?: string) => {
    setPendingMessages(prev => {
      const newMap = new Map(prev);
      const message = newMap.get(tempId);
      if (message) {
        newMap.set(tempId, { ...message, status, updatedAt: new Date().toISOString() });
      }
      return newMap;
    });
    
    onMessageStatusUpdate?.(tempId, status, error);
  }, [onMessageStatusUpdate]);

  // Process message queue with batching
  const processMessageQueue = useCallback(async () => {
    if (processingQueue.current || messageQueue.current.length === 0) return;
    
    processingQueue.current = true;
    const batch = messageQueue.current.splice(0, 5); // Process 5 messages at a time
    
    try {
      await Promise.all(batch.map(async (message) => {
        try {
          const input: SendMessageInput = {
            conversationId: message.conversationId,
            type: message.type.toLowerCase() as any,
            content: message.content,
            mediaUrl: message.mediaUrl
          };

          const result = await executeMutation({
            query: SEND_MESSAGE,
            variables: { input }
          });

          if (result.errors) {
            throw new Error(result.errors[0]?.message || 'Failed to send message');
          }

          const confirmedMessage = result.data?.sendMessage;
          if (confirmedMessage) {
            updateMessageStatus(message.tempId, 'SENT');
            onMessageConfirmed?.(message.tempId, confirmedMessage);
            
            // Remove from pending messages after successful send
            setPendingMessages(prev => {
              const newMap = new Map(prev);
              newMap.delete(message.tempId);
              return newMap;
            });
          }

        } catch (err: any) {
          console.error(`Failed to send message ${message.tempId}:`, err);
          
          const currentMessage = pendingMessages.get(message.tempId);
          const retryCount = (currentMessage?.retryCount || 0) + 1;
          
          if (retryCount <= maxRetries) {
            // Retry after delay
            setTimeout(() => {
              const updatedMessage = { ...message, retryCount };
              messageQueue.current.push(updatedMessage);
              processMessageQueue();
            }, retryDelay * retryCount);
            
            updateMessageStatus(message.tempId, 'PENDING');
          } else {
            updateMessageStatus(message.tempId, 'FAILED', err.message);
          }
        }
      }));
    } finally {
      processingQueue.current = false;
      
      // Process remaining messages
      if (messageQueue.current.length > 0) {
        setTimeout(processMessageQueue, 100);
      }
    }
  }, [maxRetries, retryDelay, updateMessageStatus, onMessageConfirmed, pendingMessages]);

  // Send single message with optimistic update
  const sendMessage = useCallback(async (input: SendMessageInput): Promise<OptimisticMessage> => {
    try {
      setError(null);
      
      // Create optimistic message
      const optimisticMessage = createOptimisticMessage(input);
      
      // Add to pending messages
      setPendingMessages(prev => new Map(prev).set(optimisticMessage.tempId, optimisticMessage));
      
      // Trigger optimistic update
      onOptimisticUpdate?.(optimisticMessage);
      
      // Add to queue for processing
      messageQueue.current.push(optimisticMessage);
      processMessageQueue();
      
      return optimisticMessage;

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send message';
      console.error('Send message error:', errorMessage);
      setError(errorMessage);
      throw err;
    }
  }, [createOptimisticMessage, onOptimisticUpdate, processMessageQueue]);

  // Send multiple messages as batch
  const sendMessageBatch = useCallback(async (inputs: SendMessageInput[]): Promise<OptimisticMessage[]> => {
    try {
      setLoading(true);
      setError(null);
      
      const optimisticMessages = inputs.map(createOptimisticMessage);
      
      // Add all to pending messages
      setPendingMessages(prev => {
        const newMap = new Map(prev);
        optimisticMessages.forEach(msg => newMap.set(msg.tempId, msg));
        return newMap;
      });
      
      // Trigger optimistic updates
      optimisticMessages.forEach(onOptimisticUpdate);
      
      // Add all to queue
      messageQueue.current.push(...optimisticMessages);
      processMessageQueue();
      
      return optimisticMessages;

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send messages';
      console.error('Send message batch error:', errorMessage);
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [createOptimisticMessage, onOptimisticUpdate, processMessageQueue]);

  // Retry failed message
  const retryMessage = useCallback(async (tempId: string): Promise<void> => {
    const message = pendingMessages.get(tempId);
    if (!message || message.status !== 'FAILED') return;
    
    const updatedMessage = { ...message, status: 'PENDING' as const, retryCount: 0 };
    setPendingMessages(prev => new Map(prev).set(tempId, updatedMessage));
    
    messageQueue.current.push(updatedMessage);
    processMessageQueue();
  }, [pendingMessages, processMessageQueue]);

  return {
    sendMessage,
    sendMessageBatch,
    retryMessage,
    loading,
    error,
    pendingMessages
  };
}; 