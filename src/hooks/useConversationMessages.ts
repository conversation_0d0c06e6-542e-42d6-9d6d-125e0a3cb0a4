import { useState, useEffect, useCallback } from 'react';
import { graphqlClient, executeGraphQLOperation } from '../lib/graphql-client';
import { GET_CONVERSATION_MESSAGES } from '../lib/graphql/queries/chat';

export interface ConversationMessage {
  id: string;
  conversationId: string;
  messageId: string;
  sortKey: string;
  organizationId: string;
  businessPhone: string;
  customerPhone: string;
  phoneNumberId: string;
  wabaId: string;
  fromNumber: string;
  contactName: string;
  messageType: string;
  direction: 'INBOUND' | 'OUTBOUND' | 'inbound' | 'outbound'; // Support both backend (uppercase) and legacy (lowercase) formats
  status: string;
  content: string;
  textContent?: string;
  mediaUrl?: string;
  mediaType?: string;
  fileName?: string;
  caption?: string;
  latitude?: number;
  longitude?: number;
  locationName?: string;
  locationAddress?: string;
  templateName?: string;
  templateLanguage?: string;
  templateParameters?: string;
  timestamp: string;
  createdAt: string;
  updatedAt: string;
  deliveredAt?: string;
  readAt?: string;
  ttl?: number;
}

export interface ConversationMessagesResponse {
  items: ConversationMessage[];
  nextToken?: string;
  scannedCount: number;
}

export interface UseConversationMessagesOptions {
  conversationId: string;
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseConversationMessagesResult {
  messages: ConversationMessage[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  loadingMore: boolean;
  refresh: () => void;
  refreshing: boolean;
}

const DEFAULT_LIMIT = 50;
const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds

export function useConversationMessages({
  conversationId,
  limit = DEFAULT_LIMIT,
  autoRefresh = false,
  refreshInterval = DEFAULT_REFRESH_INTERVAL,
}: UseConversationMessagesOptions): UseConversationMessagesResult {
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMessages = useCallback(async (
    reset = false,
    token?: string
  ): Promise<ConversationMessagesResponse | null> => {
    try {
      setError(null);
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const variables = {
        conversationId,
        limit,
        ...(token && { nextToken: token })
      };

      const result = await executeGraphQLOperation(
        () => graphqlClient.graphql({
          query: GET_CONVERSATION_MESSAGES,
          variables
        }) as any
      );

      if (result?.data?.getConversationMessages) {
        const { items, nextToken: newNextToken } = result.data.getConversationMessages;
        
        if (reset) {
          // Reverse messages for proper chat display (oldest at top, newest at bottom)
          setMessages(items.reverse());
        } else {
          setMessages(prev => {
            // Add older messages to the beginning, but reverse them first for proper order
            const existingIds = new Set(prev.map((m: ConversationMessage) => m.id));
            const newMessages = items.filter((m: ConversationMessage) => !existingIds.has(m.id));
            return [...newMessages.reverse(), ...prev];
          });
        }

        setNextToken(newNextToken);
        setHasMore(!!newNextToken);
        
        return result.data.getConversationMessages;
      }
      
      return null;
    } catch (err: any) {
      console.error('Error loading conversation messages:', err);
      setError(err.message || 'Failed to load conversation messages');
      throw err;
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [conversationId, limit]);

  // Initial load
  useEffect(() => {
    if (conversationId) {
      setMessages([]);
      setNextToken(undefined);
      setHasMore(true);
      loadMessages(true);
    }
  }, [conversationId, loadMessages]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh || !conversationId) return;

    const interval = setInterval(async () => {
      try {
        setRefreshing(true);
        await loadMessages(true);
      } catch (err) {
        console.error('Auto refresh failed:', err);
      } finally {
        setRefreshing(false);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, conversationId, refreshInterval, loadMessages]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loadingMore || loading) return;

    try {
      setLoadingMore(true);
      await loadMessages(false, nextToken);
    } catch (err) {
      console.error('Error loading more messages:', err);
    } finally {
      setLoadingMore(false);
    }
  }, [hasMore, loadingMore, loading, loadMessages, nextToken]);

  const refresh = useCallback(async () => {
    if (loading || refreshing) return;

    try {
      setRefreshing(true);
      await loadMessages(true);
    } catch (err) {
      console.error('Error refreshing messages:', err);
    } finally {
      setRefreshing(false);
    }
  }, [loading, refreshing, loadMessages]);

  return {
    messages,
    loading,
    error,
    hasMore,
    loadMore,
    loadingMore,
    refresh,
    refreshing,
  };
} 