import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useChatSubscriptions } from './useChatSubscriptions';
import { useNotifications } from '../components/ui/notification';
import { useAuth } from './useAuth';
import type { ConversationMessage } from './useConversationMessages';
import type { MessageNotificationData } from '../types/chat';

export interface UseOrganizationNotificationsOptions {
  currentConversationId?: string; // To avoid notifying about the currently active chat
  enabled?: boolean;
}

export interface UseOrganizationNotificationsResult {
  unreadNotifications: MessageNotificationData[];
  clearNotification: (id: string) => void;
  clearAllNotifications: () => void;
  isConnected: boolean;
  reconnect: () => void;
}

const MAX_NOTIFICATION_CONTENT_LENGTH = 50;

export function useOrganizationNotifications({
  currentConversationId,
  enabled = true
}: UseOrganizationNotificationsOptions = {}): UseOrganizationNotificationsResult {
  const { user } = useAuth();
  const { info } = useNotifications();
  const [unreadNotifications, setUnreadNotifications] = useState<MessageNotificationData[]>([]);
  const currentConversationRef = useRef(currentConversationId);

  // Update ref when currentConversationId changes
  useEffect(() => {
    currentConversationRef.current = currentConversationId;
  }, [currentConversationId]);

  // Format message content for notification display
  const formatMessageContent = useCallback((message: ConversationMessage): string => {
    let content = '';
    
    try {
      // Try to parse JSON content first (from real-time notifications)
      if (typeof message.content === 'string') {
        try {
          const parsed = JSON.parse(message.content);
          if (parsed && typeof parsed === 'object') {
            switch (parsed.type?.toLowerCase() || message.messageType?.toLowerCase()) {
              case 'text':
                content = parsed.text || 'Text message';
                break;
              case 'image':
                content = '📷 Image';
                break;
              case 'video':
                content = '🎥 Video';
                break;
              case 'audio':
                content = '🎵 Voice message';
                break;
              case 'document':
                content = '📎 Document';
                break;
              case 'location':
                content = '📍 Location';
                break;
              case 'contacts':
                content = '👤 Contact';
                break;
              case 'sticker':
                content = '😄 Sticker';
                break;
              default:
                content = parsed.text || 'New message';
            }
          } else {
            // Fallback to raw content if JSON parsing succeeds but structure is unexpected
            content = message.content;
          }
        } catch (parseError) {
          // Not JSON, use as regular string
          content = message.content;
        }
      } else {
        // Handle object content directly
        const contentObj = message.content as any;
        switch (message.messageType?.toLowerCase()) {
          case 'text':
            content = contentObj?.text || 'Text message';
            break;
          case 'image':
            content = '📷 Image';
            break;
          case 'video':
            content = '🎥 Video';
            break;
          case 'audio':
            content = '🎵 Voice message';
            break;
          case 'document':
            content = '📎 Document';
            break;
          case 'location':
            content = '📍 Location';
            break;
          case 'contact':
          case 'contacts':
            content = '👤 Contact';
            break;
          case 'sticker':
            content = '😄 Sticker';
            break;
          default:
            content = 'New message';
        }
      }
    } catch (err) {
      console.warn('Error formatting message content:', err);
      content = 'New message';
    }

    // Truncate long content
    if (content.length > MAX_NOTIFICATION_CONTENT_LENGTH) {
      content = content.substring(0, MAX_NOTIFICATION_CONTENT_LENGTH) + '...';
    }

    return content;
  }, []);

  // Real-time subscription callbacks for organization-wide notifications
  const subscriptionCallbacks = useMemo(() => ({
    onNewMessage: (message: ConversationMessage) => {
      // Only notify for inbound messages and if not from current conversation (case-insensitive)
      if (message.direction?.toLowerCase() === 'inbound' && 
          message.conversationId !== currentConversationRef.current &&
          enabled) {
        
        console.log('🔔 Organization notification: New message in other conversation:', {
          conversationId: message.conversationId,
          currentConversation: currentConversationRef.current,
          contactName: message.contactName
        });

        const notificationData: MessageNotificationData = {
          id: message.messageId,
          conversationId: message.conversationId,
          contactName: message.contactName || 'Unknown Contact',
          content: formatMessageContent(message),
          timestamp: message.timestamp,
          messageType: message.messageType
        };

        // Add to unread notifications
        setUnreadNotifications(prev => {
          // Avoid duplicates
          const exists = prev.some(n => n.id === notificationData.id);
          if (exists) return prev;
          
          return [notificationData, ...prev];
        });

        // Show toast notification
        info(
          `New message from ${notificationData.contactName}`,
          notificationData.content
        );
      }
    },
    
    onMessageSent: () => {
      // Optionally handle sent message notifications
      // For now, we don't notify about sent messages
    },
    
    onConversationUpdated: () => {
      // Handle conversation updates if needed
    },
    
    onError: (error: string) => {
      console.error('❌ Organization notifications subscription error:', error);
    }
  }), [enabled, formatMessageContent, info]);

  // Set up organization-wide subscriptions
  const {
    isConnected,
    reconnect
  } = useChatSubscriptions(subscriptionCallbacks, {
    organizationId: user?.organizationId,
    enabled: enabled && !!user?.organizationId
  });

  // Clear specific notification
  const clearNotification = useCallback((id: string) => {
    setUnreadNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setUnreadNotifications([]);
  }, []);

  // Clear notifications for current conversation when it changes
  useEffect(() => {
    if (currentConversationId) {
      setUnreadNotifications(prev => 
        prev.filter(n => n.conversationId !== currentConversationId)
      );
    }
  }, [currentConversationId]);

  return {
    unreadNotifications,
    clearNotification,
    clearAllNotifications,
    isConnected,
    reconnect
  };
} 