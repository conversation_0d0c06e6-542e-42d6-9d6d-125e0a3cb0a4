@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    height: 100%;
  }
  
  #root {
    height: 100%;
  }
  
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 196 80% 36%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;
    --accent: 196 80% 36%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 196 80% 36%;
    --radius: 0.75rem;
    
    /* Custom Notefy brand colors */
    --notefy-primary: 196 80% 36%;
    --notefy-secondary: 174 75% 37%;
    --notefy-accent: 162 73% 46%;
    --notefy-success: 142 76% 36%;
    --notefy-warning: 38 92% 50%;
    --notefy-error: 0 84% 60%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(196 80% 36%) 0%, hsl(174 75% 37%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(162 73% 46%) 0%, hsl(196 80% 36%) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* WhatsApp-inspired Chat Colors */
    --chat-primary: #075E54;
    --chat-primary-light: #128C7E;
    --chat-outbound: #DCF8C6;
    --chat-inbound: #FFFFFF;
    --chat-bg: #EAFFDA;
    --chat-input-bg: #F8FFF2;
    --chat-error: #FF0000;
    --chat-success: #075E54;
    --chat-text-primary: #000000;
    --chat-text-secondary: #666666;
    --chat-text-on-primary: #FFFFFF;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 196 80% 50%;
    --primary-foreground: 222 47% 11%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 196 80% 50%;
    
    /* Dark mode shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }
}

@layer components {
  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px hsl(var(--notefy-primary) / 0.3);
  }
  
  .glow-secondary {
    box-shadow: 0 0 20px hsl(var(--notefy-secondary) / 0.3);
  }
  
  .glow-accent {
    box-shadow: 0 0 20px hsl(var(--notefy-accent) / 0.3);
  }
  
  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  
  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  /* Glass morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }
  
  /* Enterprise animations */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }
}

@layer utilities {
  /* Text gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Custom keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Loading states */
.loading-skeleton {
  @apply animate-pulse bg-muted rounded;
}

.loading-spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}
