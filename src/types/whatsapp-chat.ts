// WhatsApp-style Chat Types for Mock Implementation
// Simplified types for demo purposes

export interface Chat {
  id: string;
  name: string;
  avatar?: string;
  lastMessage?: Message;
  unreadCount: number;
  isOnline: boolean;
  lastSeen?: Date;
  isPinned: boolean;
  isMuted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  chatId: string;
  content: string;
  type: 'text' | 'voice' | 'image' | 'document';
  isOutbound: boolean;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: Date;
  voiceData?: VoiceMessageData;
  replyTo?: string;
  isForwarded?: boolean;
  isStarred?: boolean;
}

export interface VoiceMessageData {
  duration: number; // in seconds
  audioBlob?: Blob;
  audioUrl?: string;
  waveform?: number[]; // for visualization
}

export interface TypingIndicator {
  chatId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface ChatState {
  chats: Chat[];
  messages: Record<string, Message[]>;
  currentChatId: string | null;
  typingIndicators: Record<string, TypingIndicator>;
  isRecording: boolean;
  recordingChatId: string | null;
  searchQuery: string;
  theme: 'light' | 'dark';
}

export interface MockChatService {
  listChats(): Promise<Chat[]>;
  getMessages(chatId: string): Promise<Message[]>;
  sendText(chatId: string, text: string): Promise<Message>;
  sendVoice(chatId: string, audioBlob: Blob, duration: number): Promise<Message>;
  markAsRead(chatId: string, messageId: string): Promise<void>;
  setTyping(chatId: string, isTyping: boolean): Promise<void>;
}

// Component Props
export interface ChatListProps {
  chats: Chat[];
  selectedChatId: string | null;
  onChatSelect: (chatId: string) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export interface ChatItemProps {
  chat: Chat;
  isSelected: boolean;
  onClick: () => void;
}

export interface MessageListProps {
  messages: Message[];
  isTyping: boolean;
  onMessageRead: (messageId: string) => void;
}

export interface MessageBubbleProps {
  message: Message;
  showTimestamp?: boolean;
  showStatus?: boolean;
}

export interface VoiceBubbleProps {
  message: Message;
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
}

export interface ComposerProps {
  onSendText: (text: string) => void;
  onSendVoice: (audioBlob: Blob, duration: number) => void;
  onTyping: (isTyping: boolean) => void;
  disabled?: boolean;
}

export interface ChatHeaderProps {
  chat: Chat | null;
  onBack?: () => void;
  showBackButton?: boolean;
}

export interface TypingIndicatorProps {
  isVisible: boolean;
  contactName?: string;
}
