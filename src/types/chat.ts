// Chat System TypeScript Types
// Based on unified schema from Task 1-3

export interface Conversation {
  id: string;
  conversationId: string;
  organizationId: string;
  businessPhone: string;
  customerPhone: string;
  phoneNumberId: string;
  wabaId?: string;
  customerName?: string;
  customerProfilePicture?: string;
  status: ConversationStatus;
  assignedUserId?: string;
  messageCount: number;
  unreadCount: number;
  lastMessageAt: string; // AWSDateTime
  lastMessageContent?: string;
  createdAt: string; // AWSDateTime
  updatedAt: string; // AWSDateTime
  tags: string[];
  shopifyCustomerId?: string;
  storeId?: string;
  storeName?: string;
  // Resolved fields (loaded separately)
  recentOrders?: ShopifyOrderSummary[];
  messages?: ChatMessage[];
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  messageId: string;
  sortKey: string;
  organizationId: string;
  businessPhone: string;
  customerPhone: string;
  phoneNumberId: string;
  wabaId?: string;
  fromNumber: string;
  contactName?: string;
  messageType: MessageType;
  direction: MessageDirection;
  status: MessageStatus;
  content: Record<string, any>; // AWSJSON
  textContent?: string;
  mediaUrl?: string;
  mediaType?: string;
  fileName?: string;
  caption?: string;
  latitude?: number;
  longitude?: number;
  locationName?: string;
  locationAddress?: string;
  templateName?: string;
  templateLanguage?: string;
  templateParameters?: Record<string, any>;
  timestamp: string; // AWSDateTime
  createdAt: string; // AWSDateTime
  updatedAt: string; // AWSDateTime
  deliveredAt?: string; // AWSDateTime
  readAt?: string; // AWSDateTime
  ttl?: number;
}

export interface ConversationConnection {
  items: Conversation[];
  nextToken?: string;
  scannedCount?: number;
}

export interface ChatMessageConnection {
  items: ChatMessage[];
  nextToken?: string;
  scannedCount?: number;
}

export interface ConversationFilter {
  phoneNumberId?: string;
  status?: ConversationStatus;
  assignedUserId?: string;
  hasUnread?: boolean;
}

export interface MessageFilter {
  messageType?: MessageType;
  direction?: MessageDirection;
  dateFrom?: string; // AWSDateTime
  dateTo?: string; // AWSDateTime
}

export interface ShopifyOrderSummary {
  orderId: string;
  orderNumber: string;
  totalPrice: string;
  currency: string;
  status: string;
  createdAt: string; // AWSDateTime
}

// Enums
export enum ConversationStatus {
  OPEN = 'OPEN',
  ASSIGNED = 'ASSIGNED',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  ARCHIVED = 'ARCHIVED'
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  DOCUMENT = 'DOCUMENT',
  LOCATION = 'LOCATION',
  CONTACT = 'CONTACT',
  TEMPLATE = 'TEMPLATE',
  INTERACTIVE = 'INTERACTIVE',
  STICKER = 'STICKER'
}

export enum MessageDirection {
  INBOUND = 'INBOUND',
  OUTBOUND = 'OUTBOUND'
}

export enum MessageStatus {
  RECEIVED = 'RECEIVED',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED'
}

// Component Props
export interface ConversationListProps {
  onConversationSelect: (conversationId: string) => void;
  selectedConversationId?: string;
  unreadNotifications?: MessageNotificationData[];
  className?: string;
  searchQuery?: string;
  activeFilters?: ConversationFilter;
}

// Import for notification data type
export interface MessageNotificationData {
  id: string;
  conversationId: string;
  contactName: string;
  content: string;
  timestamp: string;
  messageType: string;
}

export interface ConversationItemProps {
  conversation: Conversation;
  isSelected?: boolean;
  onClick: () => void;
  unreadNotificationCount?: number;
  className?: string;
}

// Hook Return Types
export interface UseConversationsResult {
  conversations: Conversation[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refetch: () => Promise<void>;
  clearUnreadCount: (conversationId: string) => void;
  // Real-time subscription status
  subscriptionConnected?: boolean;
  reconnectSubscriptions?: () => void;
}

export interface UseConversationMessagesResult {
  messages: ChatMessage[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refetch: () => Promise<void>;
}

// Utility Types
export type ConversationGrouping = {
  phoneNumberId: string;
  storeName?: string;
  conversations: Conversation[];
};

export type ConversationSortOrder = 'recent' | 'alphabetical' | 'unread';

export type FilterOption = {
  label: string;
  value: string;
  count?: number;
}; 