/**
 * Message Content Parser
 * Handles parsing of escaped JSON content strings from WhatsApp messages
 * Based on Task 1-9: Fix Chat Message Content Display Issues
 */

// Constants for repeated values
export const DEFAULT_UNKNOWN_MEDIA_TYPE = 'unknown';
export const DEFAULT_FILE_NAME = 'Download';
export const CONTENT_TYPE_TEXT = 'text';
export const CONTENT_TYPE_LOCATION = 'location';
export const CONTENT_TYPE_CONTACTS = 'contacts';
export const CONTENT_TYPE_IMAGE = 'image';
export const CONTENT_TYPE_VIDEO = 'video';
export const CONTENT_TYPE_DOCUMENT = 'document';
export const CONTENT_TYPE_AUDIO = 'audio';
export const CONTENT_TYPE_STICKER = 'sticker';

export interface ParsedTextContent {
  type: typeof CONTENT_TYPE_TEXT;
  text: string;
}

export interface ParsedLocationContent {
  type: typeof CONTENT_TYPE_LOCATION;
  latitude: number;
  longitude: number;
  name?: string;
  address?: string;
}

export interface ParsedContactContent {
  type: typeof CONTENT_TYPE_CONTACTS;
  contacts?: ContactInfo[];
}

export interface ParsedMediaContent {
  type: typeof CONTENT_TYPE_IMAGE | typeof CONTENT_TYPE_VIDEO | typeof CONTENT_TYPE_DOCUMENT | typeof CONTENT_TYPE_AUDIO | typeof CONTENT_TYPE_STICKER;
  s3_url: string;
  mime_type: string;
  file_size?: number;
  image_id?: string;
  video_id?: string;
  document_id?: string;
  audio_id?: string;
  sticker_id?: string;
  caption?: string;
}

export interface ContactInfo {
  name?: string;
  phone?: string;
  email?: string;
  organization?: string;
}

export type ParsedContent = 
  | ParsedTextContent 
  | ParsedLocationContent 
  | ParsedContactContent 
  | ParsedMediaContent;

/**
 * Parses escaped JSON content string into structured content object
 * @param content - The escaped JSON string from message.content
 * @returns Parsed content object or null if parsing fails
 */
export function parseMessageContent(content: string): ParsedContent | null {
  if (!content || typeof content !== 'string') {
    return null;
  }

  try {
    // Handle already parsed objects
    if (typeof content === 'object') {
      return content as ParsedContent;
    }

    // Try to parse as JSON - handle double-escaped JSON from GraphQL
    let parsed = JSON.parse(content);
    
    // If the result is a string, it might be double-escaped JSON - try parsing again
    if (typeof parsed === 'string') {
      try {
        parsed = JSON.parse(parsed);
      } catch {
        // If second parse fails, treat the first parsed string as text content
        return {
          type: CONTENT_TYPE_TEXT,
          text: parsed
        };
      }
    }
    
    // Validate and normalize the parsed content
    return validateAndNormalizeParsedContent(parsed);
  } catch (error) {
    // If JSON parsing fails, treat as plain text
    console.warn('Failed to parse message content as JSON:', error);
    return {
      type: CONTENT_TYPE_TEXT,
      text: content
    };
  }
}

/**
 * Validates and normalizes parsed content to ensure it matches expected structure
 */
function validateAndNormalizeParsedContent(parsed: any): ParsedContent | null {
  if (!parsed || typeof parsed !== 'object') {
    return null;
  }

  const contentType = parsed.type;

  switch (contentType) {
    case CONTENT_TYPE_TEXT:
      return {
        type: CONTENT_TYPE_TEXT,
        text: parsed.text || ''
      };

    case CONTENT_TYPE_LOCATION:
      if (typeof parsed.latitude === 'number' && typeof parsed.longitude === 'number') {
        return {
          type: CONTENT_TYPE_LOCATION,
          latitude: parsed.latitude,
          longitude: parsed.longitude,
          name: parsed.name,
          address: parsed.address
        };
      }
      break;

    case CONTENT_TYPE_CONTACTS:
      return {
        type: CONTENT_TYPE_CONTACTS,
        contacts: normalizeWhatsAppContacts(parsed.contacts || [])
      };

    case CONTENT_TYPE_IMAGE:
    case CONTENT_TYPE_VIDEO:
    case CONTENT_TYPE_DOCUMENT:
    case CONTENT_TYPE_AUDIO:
    case CONTENT_TYPE_STICKER:
      if (parsed.s3_url && parsed.mime_type) {
        return {
          type: contentType,
          s3_url: parsed.s3_url,
          mime_type: parsed.mime_type,
          file_size: parsed.file_size,
          image_id: parsed.image_id,
          video_id: parsed.video_id,
          document_id: parsed.document_id,
          audio_id: parsed.audio_id,
          sticker_id: parsed.sticker_id,
          caption: parsed.caption
        };
      }
      break;
  }

  // Return null if content doesn't match expected structure
  return null;
}

/**
 * Parses the "last message" string format, e.g., 'Text { body: "hi there" }'
 * @param content - The string to parse
 * @returns The extracted message body or the original string
 */
export function parseLastMessageContent(content: string): string {
  if (!content) {
    return '';
  }
  
  const match = content.match(/Text { body: "([^"]*)" }/);
  
  if (match && match[1]) {
    return match[1];
  }
  
  return content;
}


/**
 * Normalizes WhatsApp contact structure to our expected ContactInfo format
 */
function normalizeWhatsAppContacts(contacts: any[]): ContactInfo[] {
  if (!Array.isArray(contacts)) {
    return [];
  }

  return contacts.map((contact: any) => {
    const normalizedContact: ContactInfo = {};

    // Handle WhatsApp's nested name structure
    if (contact.name) {
      if (typeof contact.name === 'string') {
        normalizedContact.name = contact.name;
      } else if (typeof contact.name === 'object') {
        // WhatsApp structure: { formatted_name, first_name, last_name, etc. }
        normalizedContact.name = contact.name.formatted_name || 
                                contact.name.first_name ||
                                `${contact.name.first_name || ''} ${contact.name.last_name || ''}`.trim() ||
                                'Contact';
      }
    }

    // Handle phones array - take the first one or the one marked as primary
    if (contact.phones && Array.isArray(contact.phones) && contact.phones.length > 0) {
      const primaryPhone = contact.phones.find((p: any) => p.type === 'MAIN') || contact.phones[0];
      normalizedContact.phone = primaryPhone.phone || primaryPhone.wa_id;
    }

    // Handle emails array - take the first one
    if (contact.emails && Array.isArray(contact.emails) && contact.emails.length > 0) {
      normalizedContact.email = contact.emails[0].email;
    }

    // Handle organization
    if (contact.org && contact.org.company) {
      normalizedContact.organization = contact.org.company;
    }

    return normalizedContact;
  }).filter(contact => contact.name || contact.phone || contact.email); // Filter out empty contacts
}

/**
 * Generates a meaningful description for media content
 */
export function getMediaDescription(parsedContent: ParsedMediaContent, fallback: string = DEFAULT_FILE_NAME): string {
  const { type, mime_type, file_size } = parsedContent;

  let description = '';

  switch (type) {
    case CONTENT_TYPE_IMAGE:
      description = 'Image';
      break;
    case CONTENT_TYPE_VIDEO:
      description = 'Video';
      break;
    case CONTENT_TYPE_DOCUMENT:
      description = 'Document';
      break;
    case CONTENT_TYPE_AUDIO:
      description = 'Audio';
      break;
    case CONTENT_TYPE_STICKER:
      description = 'Sticker';
      break;
    default:
      description = fallback;
  }

  // Add file type information
  if (mime_type) {
    const fileExtension = getFileExtensionFromMimeType(mime_type);
    if (fileExtension) {
      description += ` (${fileExtension.toUpperCase()})`;
    }
  }

  // Add file size if available
  if (file_size) {
    description += ` - ${formatFileSize(file_size)}`;
  }

  return description;
}

/**
 * Extracts file extension from MIME type
 */
function getFileExtensionFromMimeType(mimeType: string): string | null {
  const mimeToExtension: Record<string, string> = {
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'video/mp4': 'mp4',
    'video/webm': 'webm',
    'video/quicktime': 'mov',
    'audio/mpeg': 'mp3',
    'audio/mp4': 'm4a',
    'audio/ogg': 'ogg',
    'audio/wav': 'wav',
    'application/pdf': 'pdf',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'text/plain': 'txt',
    'text/csv': 'csv'
  };

  return mimeToExtension[mimeType] || null;
}

/**
 * Formats file size in human readable format
 */
function formatFileSize(bytes: number): string {
  const KB = 1024;
  const MB = KB * 1024;
  const GB = MB * 1024;

  if (bytes >= GB) {
    return `${(bytes / GB).toFixed(1)} GB`;
  } else if (bytes >= MB) {
    return `${(bytes / MB).toFixed(1)} MB`;
  } else if (bytes >= KB) {
    return `${(bytes / KB).toFixed(1)} KB`;
  } else {
    return `${bytes} bytes`;
  }
}

/**
 * Checks if content string looks like escaped JSON
 */
export function isEscapedJson(content: string): boolean {
  return typeof content === 'string' && 
         content.startsWith('"{') && 
         content.endsWith('}"');
}

/**
 * Gets appropriate file icon emoji based on content type and MIME type
 */
export function getContentIcon(parsedContent: ParsedContent): string {
  switch (parsedContent.type) {
    case CONTENT_TYPE_TEXT:
      return '💬';
    case CONTENT_TYPE_LOCATION:
      return '📍';
    case CONTENT_TYPE_CONTACTS:
      return '👤';
    case CONTENT_TYPE_IMAGE:
      return '🖼️';
    case CONTENT_TYPE_VIDEO:
      return '🎬';
    case CONTENT_TYPE_AUDIO:
      return '🎵';
    case CONTENT_TYPE_STICKER:
      return '😀';
    case CONTENT_TYPE_DOCUMENT:
      if ('mime_type' in parsedContent) {
        const { mime_type } = parsedContent;
        if (mime_type.includes('pdf')) return '📄';
        if (mime_type.includes('word')) return '📝';
        if (mime_type.includes('excel') || mime_type.includes('spreadsheet')) return '📊';
        if (mime_type.includes('powerpoint') || mime_type.includes('presentation')) return '📺';
        if (mime_type.includes('zip') || mime_type.includes('archive')) return '🗜️';
      }
      return '📁';
    default:
      return '📄';
  }
} 