/**
 * S3 Utilities for Private Media Bucket Access
 * <PERSON>les signed URLs for private WhatsApp media files
 * Based on Task 1-9: Fix Chat Message Content Display Issues
 */

import React from 'react';

// Constants for S3 configuration
const PRIVATE_MEDIA_BUCKET = 'notefy-whatsapp-media-dev';
const DEFAULT_SIGNED_URL_EXPIRY = 3600; // 1 hour in seconds
const S3_REGION = 'me-central-1';

/**
 * Checks if a URL is from our private media bucket
 */
export function isPrivateMediaUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  return url.includes(PRIVATE_MEDIA_BUCKET) || 
         url.includes(`s3.${S3_REGION}.amazonaws.com`);
}

/**
 * Extracts the S3 key from a full S3 URL
 */
export function extractS3KeyFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    // Handle s3://bucket/key format
    if (urlObj.protocol === 's3:') {
      return urlObj.pathname.substring(1); // Remove leading slash
    }
    
    // Handle https://bucket.s3.region.amazonaws.com/key format
    if (urlObj.hostname.includes('s3') && urlObj.hostname.includes('amazonaws.com')) {
      return urlObj.pathname.substring(1); // Remove leading slash
    }
    
    // Handle https://s3.region.amazonaws.com/bucket/key format
    if (urlObj.hostname.includes('s3') && urlObj.pathname.includes(PRIVATE_MEDIA_BUCKET)) {
      const bucketIndex = urlObj.pathname.indexOf(PRIVATE_MEDIA_BUCKET);
      const keyStart = bucketIndex + PRIVATE_MEDIA_BUCKET.length + 1; // +1 for the slash
      return urlObj.pathname.substring(keyStart);
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to extract S3 key from URL:', url, error);
    return null;
  }
}

/**
 * Generates a signed URL for private S3 media access
 * Falls back to original URL if signing fails
 */
export async function getSignedMediaUrl(
  originalUrl: string,
  _expirySeconds: number = DEFAULT_SIGNED_URL_EXPIRY
): Promise<string> {
  // If not a private media URL, return as-is
  if (!isPrivateMediaUrl(originalUrl)) {
    return originalUrl;
  }
  
  try {
    // Extract S3 key from the URL
    const s3Key = extractS3KeyFromUrl(originalUrl);
    
    if (!s3Key) {
      console.warn('Could not extract S3 key from URL:', originalUrl);
      return originalUrl;
    }
    
    // Generate signed URL using Amplify Storage
    // TODO: Replace with proper AWS SDK v3 implementation
    // For now, return the original URL to avoid build errors
    return originalUrl;
  } catch (error) {
    console.warn('Failed to generate signed URL for media:', originalUrl, error);
    
    // Fallback to original URL - browser will handle auth if possible
    return originalUrl;
  }
}

/**
 * Batch generates signed URLs for multiple media files
 */
export async function getSignedMediaUrls(
  urls: string[], 
  expirySeconds: number = DEFAULT_SIGNED_URL_EXPIRY
): Promise<Record<string, string>> {
  const results: Record<string, string> = {};
  
  // Process URLs in parallel
  const promises = urls.map(async (url) => {
    try {
      const signedUrl = await getSignedMediaUrl(url, expirySeconds);
      results[url] = signedUrl;
    } catch (error) {
      console.warn('Failed to sign URL:', url, error);
      results[url] = url; // Fallback to original
    }
  });
  
  await Promise.allSettled(promises);
  return results;
}

/**
 * Creates a cached signed URL generator with expiry management
 */
export class SignedUrlCache {
  private cache = new Map<string, { url: string; expiresAt: number }>();
  private readonly cacheExpiryBuffer = 300; // 5 minutes buffer before actual expiry
  
  constructor(private defaultExpiry = DEFAULT_SIGNED_URL_EXPIRY) {}
  
  /**
   * Gets a signed URL from cache or generates a new one
   */
  async getSignedUrl(originalUrl: string, expirySeconds?: number): Promise<string> {
    const expiry = expirySeconds || this.defaultExpiry;
    const now = Date.now();
    
    // Check cache first
    const cached = this.cache.get(originalUrl);
    if (cached && cached.expiresAt > now + (this.cacheExpiryBuffer * 1000)) {
      return cached.url;
    }
    
    // Generate new signed URL
    const signedUrl = await getSignedMediaUrl(originalUrl, expiry);
    
    // Cache the result
    this.cache.set(originalUrl, {
      url: signedUrl,
      expiresAt: now + (expiry * 1000)
    });
    
    return signedUrl;
  }
  
  /**
   * Clears expired entries from cache
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (value.expiresAt <= now) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * Clears all cached URLs
   */
  clear(): void {
    this.cache.clear();
  }
}

/**
 * Global signed URL cache instance
 */
export const signedUrlCache = new SignedUrlCache();

/**
 * React hook for getting signed media URLs
 */
export function useSignedMediaUrl(originalUrl: string | null): {
  signedUrl: string | null;
  loading: boolean;
  error: string | null;
} {
  const [signedUrl, setSignedUrl] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  
  React.useEffect(() => {
    if (!originalUrl) {
      setSignedUrl(null);
      setLoading(false);
      setError(null);
      return;
    }
    
    // If not a private URL, use as-is
    if (!isPrivateMediaUrl(originalUrl)) {
      setSignedUrl(originalUrl);
      setLoading(false);
      setError(null);
      return;
    }
    
    let cancelled = false;
    
    const generateSignedUrl = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const url = await signedUrlCache.getSignedUrl(originalUrl);
        
        if (!cancelled) {
          setSignedUrl(url);
          setLoading(false);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err instanceof Error ? err.message : 'Failed to generate signed URL');
          setSignedUrl(originalUrl); // Fallback
          setLoading(false);
        }
      }
    };
    
    generateSignedUrl();
    
    return () => {
      cancelled = true;
    };
  }, [originalUrl]);
  
  return { signedUrl, loading, error };
}

// Cleanup cache periodically
if (typeof window !== 'undefined') {
  setInterval(() => {
    signedUrlCache.cleanup();
  }, 5 * 60 * 1000); // Cleanup every 5 minutes
} 