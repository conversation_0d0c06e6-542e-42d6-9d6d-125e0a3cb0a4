/**
 * Image processing utilities for chat media
 */

export interface ImageProcessingOptions {
  quality?: number; // 0-1, default 0.8
  maxWidth?: number; // default 1920
  maxHeight?: number; // default 1080
  format?: 'jpeg' | 'png' | 'webp'; // default 'jpeg'
}

/**
 * Compress and resize an image file
 */
export async function compressImage(
  file: File, 
  options: ImageProcessingOptions = {}
): Promise<File> {
  const {
    quality = 0.8,
    maxWidth = 1920,
    maxHeight = 1080,
    format = 'jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: `image/${format}`,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Remove background from image using a simple edge detection approach
 * Note: This is a basic implementation. For production, consider using AI services like Remove.bg
 */
export async function removeBackground(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      ctx?.drawImage(img, 0, 0);
      
      const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
      if (!imageData) {
        reject(new Error('Failed to get image data'));
        return;
      }

      // Simple background removal - detect edges and make background transparent
      const data = imageData.data;
      const threshold = 30; // Adjust for sensitivity
      
      // Sample corner pixels to determine background color
      const corners = [
        [0, 0], // top-left
        [canvas.width - 1, 0], // top-right
        [0, canvas.height - 1], // bottom-left
        [canvas.width - 1, canvas.height - 1] // bottom-right
      ];
      
      const bgColors = corners.map(([x, y]) => {
        const index = (y * canvas.width + x) * 4;
        return [data[index], data[index + 1], data[index + 2]];
      });
      
      // Use most common corner color as background
      const avgBgColor = [
        Math.round(bgColors.reduce((sum, color) => sum + color[0], 0) / bgColors.length),
        Math.round(bgColors.reduce((sum, color) => sum + color[1], 0) / bgColors.length),
        Math.round(bgColors.reduce((sum, color) => sum + color[2], 0) / bgColors.length)
      ];

      // Make pixels similar to background transparent
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        const diff = Math.sqrt(
          Math.pow(r - avgBgColor[0], 2) +
          Math.pow(g - avgBgColor[1], 2) +
          Math.pow(b - avgBgColor[2], 2)
        );
        
        if (diff < threshold) {
          data[i + 3] = 0; // Make transparent
        }
      }
      
      ctx?.putImageData(imageData, 0, 0);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const processedFile = new File([blob], file.name.replace(/\.[^/.]+$/, '.png'), {
              type: 'image/png',
              lastModified: Date.now()
            });
            resolve(processedFile);
          } else {
            reject(new Error('Failed to process image'));
          }
        },
        'image/png'
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Create a thumbnail from an image file
 */
export async function createThumbnail(
  file: File,
  size: number = 150
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { width, height } = img;
      const ratio = Math.min(size / width, size / height);
      const newWidth = width * ratio;
      const newHeight = height * ratio;

      canvas.width = size;
      canvas.height = size;

      // Center the image
      const x = (size - newWidth) / 2;
      const y = (size - newHeight) / 2;

      // Fill background
      if (ctx) {
        ctx.fillStyle = '#f3f4f6';
        ctx.fillRect(0, 0, size, size);
        ctx.drawImage(img, x, y, newWidth, newHeight);
      }

      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Check if a file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Get image dimensions
 */
export async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}
