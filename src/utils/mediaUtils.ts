

/**
 * Check if a URL is already a proxy URL from our media service
 * @param url - The URL to check
 * @returns boolean - true if it's already a proxy URL
 */
export function isProxyUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  return url.includes('/apis/v1/media/') && 
         (url.includes('webhooks.dev.notefy.app') || url.includes('webhooks.prod.notefy.app'));
}

/**
 * Check if a URL is from our private S3 bucket (legacy URLs that need conversion)
 * @param url - The URL to check  
 * @returns boolean - true if it's a private S3 URL that needs proxy conversion
 */
export function isPrivateS3Url(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  return url.includes('notefy-whatsapp-media-dev') || 
         url.includes('notefy-whatsapp-media-prod') || 
         (url.includes('.s3.') && url.includes('.amazonaws.com') && !isProxyUrl(url));
}

/**
 * Get the final media URL - proxy URLs are used as-is, S3 URLs are converted if possible
 * @param mediaUrl - The original media URL from the backend
 * @param organizationId - Organization ID for multi-tenant access
 * @param phoneId - Phone ID for the media
 * @param mediaId - Media ID
 * @returns Promise<string> - The final URL to use for media access
 */
export async function getMediaUrl(
  mediaUrl: string,
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<string> {
  try {
    // If it's already a proxy URL, use it as-is (permanent URLs, no expiry)
    if (isProxyUrl(mediaUrl)) {
      return mediaUrl;
    }

    // If it's not a private S3 URL, use as-is (might be external URL)
    if (!isPrivateS3Url(mediaUrl)) {
      return mediaUrl;
    }

    // For legacy S3 URLs, try to convert to proxy URL if we have the required info
    let extractedOrgId = organizationId;
    let extractedPhoneId = phoneId;
    let extractedMediaId = mediaId;

    if (!extractedOrgId || !extractedPhoneId || !extractedMediaId) {
      const urlParts = extractMediaInfoFromUrl(mediaUrl);
      extractedOrgId = extractedOrgId || urlParts.organizationId;
      extractedPhoneId = extractedPhoneId || urlParts.phoneId;
      extractedMediaId = extractedMediaId || urlParts.mediaId;
    }

    if (extractedOrgId && extractedPhoneId && extractedMediaId) {
      // Determine environment (dev/prod) 
      const isProduction = window.location.hostname.includes('prod') || window.location.hostname.includes('notefy.app');
      const environment = isProduction ? 'prod' : 'dev';

      // Build permanent proxy URL
      const proxyUrl = `https://webhooks.${environment}.notefy.app/apis/v1/media/${extractedOrgId}/${extractedPhoneId}/${extractedMediaId}`;
      
      console.log('🔄 Converted S3 URL to proxy URL:', { original: mediaUrl, proxy: proxyUrl });
      return proxyUrl;
    }

    console.warn('⚠️  Cannot convert S3 URL to proxy URL, missing required info:', mediaUrl);
    return mediaUrl; // Fallback to original URL
  } catch (error) {
    console.error('❌ Error processing media URL:', error);
    return mediaUrl; // Fallback to original URL
  }
}

/**
 * Legacy function for backward compatibility - now simplified to use permanent proxy URLs
 * @deprecated Use getMediaUrl instead
 */
export async function getAuthenticatedMediaUrl(
  mediaUrl: string,
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<string> {
  console.warn('⚠️  getAuthenticatedMediaUrl is deprecated, use getMediaUrl instead');
  return getMediaUrl(mediaUrl, organizationId, phoneId, mediaId);
}

/**
 * Extract media information from S3 URL
 * S3 URLs follow pattern: https://bucket.s3.region.amazonaws.com/org_id/phone_id/media_id/uuid.ext
 */
function extractMediaInfoFromUrl(url: string): {
  organizationId?: string;
  phoneId?: string;
  mediaId?: string;
} {
  try {
    // Handle S3 URLs
    if (url.includes('.s3.') && url.includes('.amazonaws.com/')) {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.substring(1).split('/'); // Remove leading slash
      
      if (pathParts.length >= 3) {
        return {
          organizationId: pathParts[0],
          phoneId: pathParts[1],
          mediaId: pathParts[2]
        };
      }
    }

    // Handle already proxied URLs
    if (url.includes('/apis/v1/media/')) {
      const match = url.match(/\/apis\/v1\/media\/([^\/]+)\/([^\/]+)\/([^\/\?]+)/);
      if (match) {
        return {
          organizationId: match[1],
          phoneId: match[2],
          mediaId: match[3]
        };
      }
    }

    return {};
  } catch (error) {
    console.error('❌ Error extracting media info from URL:', url, error);
    return {};
  }
}

/**
 * Create an image element with the appropriate media URL
 * @param src - Original media URL
 * @param alt - Alt text for the image
 * @param className - CSS classes
 * @param organizationId - Organization ID
 * @param phoneId - Phone ID  
 * @param mediaId - Media ID
 * @returns Promise<HTMLImageElement>
 */
export async function createMediaImage(
  src: string,
  alt: string = '',
  className: string = '',
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<HTMLImageElement> {
  const img = document.createElement('img');
  img.alt = alt;
  img.className = className;

  try {
    const finalUrl = await getMediaUrl(src, organizationId, phoneId, mediaId);
    img.src = finalUrl;
  } catch (error) {
    console.error('❌ Error creating media image:', error);
    img.src = src; // Fallback to original URL
  }

  return img;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use createMediaImage instead
 */
export async function createAuthenticatedImage(
  src: string,
  alt: string = '',
  className: string = '',
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<HTMLImageElement> {
  console.warn('⚠️  createAuthenticatedImage is deprecated, use createMediaImage instead');
  return createMediaImage(src, alt, className, organizationId, phoneId, mediaId);
}

/**
 * Download media - simplified since proxy URLs are permanent and public
 * @param mediaUrl - The media URL
 * @param filename - Desired filename for download
 * @param organizationId - Organization ID
 * @param phoneId - Phone ID
 * @param mediaId - Media ID
 */
export async function downloadMedia(
  mediaUrl: string,
  filename?: string,
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<void> {
  try {
    const finalUrl = await getMediaUrl(mediaUrl, organizationId, phoneId, mediaId);
    
    // Add download_name parameter if filename is provided
    const downloadUrl = filename 
      ? `${finalUrl}?download_name=${encodeURIComponent(filename)}`
      : finalUrl;

    // For proxy URLs, we can directly download since they're permanent and public
    if (isProxyUrl(downloadUrl)) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'media-file';
      link.target = '_blank'; // Open in new tab as fallback
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    }

    // For other URLs, try fetch approach
    try {
      const response = await fetch(downloadUrl);
      if (response.ok) {
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename || 'media-file';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        URL.revokeObjectURL(blobUrl);
      } else {
        throw new Error(`Download failed: ${response.status}`);
      }
    } catch (fetchError) {
      console.warn('⚠️  Fetch download failed, falling back to direct link:', fetchError);
      window.open(downloadUrl, '_blank');
    }
  } catch (error) {
    console.error('❌ Error downloading media:', error);
    // Final fallback to direct link
    window.open(mediaUrl, '_blank');
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use downloadMedia instead
 */
export async function downloadAuthenticatedMedia(
  mediaUrl: string,
  filename?: string,
  organizationId?: string,
  phoneId?: string,
  mediaId?: string
): Promise<void> {
  console.warn('⚠️  downloadAuthenticatedMedia is deprecated, use downloadMedia instead');
  return downloadMedia(mediaUrl, filename, organizationId, phoneId, mediaId);
} 