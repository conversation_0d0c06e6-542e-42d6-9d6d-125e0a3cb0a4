// @ts-nocheck
// Authentication Manager for Notefy Platform

import { Amplify } from 'aws-amplify';
import { 
  signUp, 
  confirmSignUp, 
  signIn, 
  confirmSignIn, 
  signOut, 
  resendSignUpCode,
  getCurrentUser,
  AuthUser,
  SignUpInput,
  SignInInput,
  ConfirmSignUpInput,
  ConfirmSignInInput,
  ResendSignUpCodeInput,
  fetchAuthSession,
  AuthTokens,
  AuthError
} from 'aws-amplify/auth';
import { Hub } from 'aws-amplify/utils';

// Types
export interface EnterpriseUser {
  userId: string;
  organizationId: string;  // ✅ Add organization ID for multi-tenant architecture
  organizationRole: string; // ✅ Add organization role (OWNER, ADMIN, MEMBER)
  phoneNumber: string;
  businessName: string;
  email?: string;
  isVerified: boolean;
  createdAt: string;
  lastLoginAt: string;
  userAttributes: Record<string, any>;
}

export interface AuthResult {
  success: boolean;
  user?: EnterpriseUser;
  requiresMFA?: boolean;
  challengeName?: string;
  error?: string;
}

export interface SignUpResult {
  success: boolean;
  isSignUpComplete: boolean;
  userId?: string;
  error?: string;
}

export interface PasswordResetResult {
  success: boolean;
  error?: string;
}

// Authentication Service Class
class AuthService {
  private currentUser: EnterpriseUser | null = null;
  private authTokens: AuthTokens | null = null;
  private hubUnsubscribe: (() => void) | null = null;

  constructor() {
    this.setupAuthListener();
  }

  // Setup authentication event listener
  private setupAuthListener() {
    this.hubUnsubscribe = Hub.listen('auth', (data) => {
      const { event } = data.payload;
      const eventData = 'data' in data.payload ? data.payload.data : undefined;
      
      switch (event) {
        case 'signedIn':
          console.log('🔐 User signed in:', eventData);
          this.handleSignedIn(eventData);
          break;
        case 'signedOut':
          console.log('🔐 User signed out');
          this.handleSignedOut();
          break;
        case 'tokenRefresh':
          console.log('🔐 Token refreshed');
          this.handleTokenRefresh(eventData);
          break;
        case 'tokenRefresh_failure':
          console.error('❌ Token refresh failed:', eventData);
          this.handleTokenRefreshFailure(eventData);
          break;
        default:
          console.log('🔐 Auth event:', event, eventData);
      }
    });
  }

  // Handle signed in event
  private async handleSignedIn(eventData: any) {
    try {
      const user = await this.getCurrentUser();
      this.currentUser = user;
      this.authTokens = await this.getAuthTokens();
    } catch (error) {
      console.error('Error handling signed in event:', error);
    }
  }

  // Handle signed out event
  private handleSignedOut() {
    this.currentUser = null;
    this.authTokens = null;
  }

  // Handle token refresh
  private async handleTokenRefresh(eventData: any) {
    try {
      this.authTokens = await this.getAuthTokens();
    } catch (error) {
      console.error('Error handling token refresh:', error);
    }
  }

  // Handle token refresh failure
  private handleTokenRefreshFailure(eventData: any) {
    console.error('Token refresh failed, user may need to re-authenticate');
    this.currentUser = null;
    this.authTokens = null;
  }

  // Get current authentication tokens
  private async getAuthTokens(): Promise<AuthTokens | null> {
    try {
      const session = await fetchAuthSession();
      return session.tokens || null;
    } catch (error) {
      console.error('Error fetching auth tokens:', error);
      return null;
    }
  }

  // Convert Cognito user to Enterprise user
  private async convertToEnterpriseUser(cognitoUser: AuthUser): Promise<EnterpriseUser> {
    try {
      // Fetch user attributes using the correct v6 API
      const { fetchUserAttributes } = await import('aws-amplify/auth');
      const attributes = await fetchUserAttributes();
      
      // ✅ COMPREHENSIVE DEBUG: Log all Cognito attributes
      console.log('🔍 CRITICAL DEBUG - Cognito attributes analysis:', {
        all_attributes: attributes,
        custom_org_id: attributes['custom:organization_id'],
        custom_org_role: attributes['custom:organization_role'],
        sub: attributes['sub'],
        phone_number: attributes['phone_number']
      });
      
      // ✅ FIX ROOT CAUSE: Get organization_id from Cognito custom attributes
      // This is set by post-registration Lambda after organization creation
      let organizationId = attributes['custom:organization_id'] || undefined;  // ✅ Use undefined, not empty string
      let organizationRole = attributes['custom:organization_role'] || 'MEMBER';
      
      // ✅ COMPREHENSIVE DEBUG: Check organizationId before proceeding
      console.log('🔍 CRITICAL DEBUG - organizationId from Cognito:', {
        organizationId_value: organizationId,
        organizationId_type: typeof organizationId,
        organizationId_length: organizationId?.length,
        is_undefined: organizationId === undefined,
        is_empty_string: organizationId === '',
        is_falsy: !organizationId
      });
      
      // If organization_id is not in Cognito custom attributes, post-registration may not have completed
      if (!organizationId) {
        console.warn('⚠️ No organization_id found in Cognito custom attributes - post-registration may not have completed');
        
        // Try fetching from DynamoDB as backup (for existing users before this fix)
        try {
          const { generateClient } = await import('aws-amplify/api');
          const { GET_USER_RECORD } = await import('@/lib/graphql-operations');
          
          const client = generateClient();
          const result = await client.graphql({
            query: GET_USER_RECORD,
            variables: { userId: cognitoUser.userId }
          });
          
          if ('data' in result && result.data?.getUserRecord) {
            const userRecord = result.data.getUserRecord;
            organizationId = userRecord.organization_id;
            organizationRole = userRecord.organization_role;
            
            console.log('✅ Fetched organization data from DynamoDB backup:', { organizationId, organizationRole });
          }
        } catch (error) {
          console.error('❌ Failed to fetch user record from DynamoDB backup:', error);
        }
        
        // If still no organization_id, user needs to complete registration
        if (!organizationId) {
          throw new Error('User registration incomplete - please complete signup process');
        }
      } else {
        console.log('✅ Found organization data in Cognito custom attributes:', { organizationId, organizationRole });
      }
      
      return {
        userId: cognitoUser.userId,
        organizationId,
        organizationRole,
        phoneNumber: attributes.phone_number || cognitoUser.username,
        businessName: `${attributes.given_name || ''} ${attributes.family_name || ''}`.trim() || 'Unknown Business',
        email: attributes.email,
        isVerified: attributes.phone_number_verified === 'true',
        createdAt: attributes.created_at || new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        userAttributes: attributes
      };
    } catch (error) {
      console.error('Error converting Cognito user to Enterprise user:', error);
      throw error; // Don't return fallback - force proper registration flow
    }
  }

  // Sign up with phone number (passwordless)
  async signUp(data: {
    phoneNumber: string;
    businessName: string;
    email?: string;
    termsAccepted: boolean;
  }): Promise<SignUpResult> {
    try {
      console.log('🔐 Starting sign up for:', data.phoneNumber);

      if (!data.termsAccepted) {
        throw new Error('Terms and conditions must be accepted');
      }

      // Format phone number
      const formattedPhoneNumber = this.formatPhoneNumber(data.phoneNumber);

      // Split business name into given_name and family_name
      const nameParts = data.businessName.trim().split(' ');
      const givenName = nameParts[0] || 'User';
      const familyName = nameParts.slice(1).join(' ') || 'Business';

      const signUpInput: SignUpInput = {
        username: formattedPhoneNumber,
        password: 'TempPass123!', // Compliant temporary password - not used in custom auth
        options: {
          userAttributes: {
            phone_number: formattedPhoneNumber,
            given_name: givenName,
            family_name: familyName,
            ...(data.email && { email: data.email }),
          },
        },
      };

      const result = await signUp(signUpInput);

      console.log('✅ Sign up successful:', result);

      return {
        success: true,
        isSignUpComplete: result.isSignUpComplete,
        userId: result.userId,
      };
    } catch (error: any) {
      console.error('❌ Sign up failed:', error);
      
      return {
        success: false,
        isSignUpComplete: false,
        error: this.parseAuthError(error),
      };
    }
  }

  // Confirm sign up with WhatsApp code
  async confirmSignUp(username: string, confirmationCode: string): Promise<SignUpResult> {
    try {
      console.log('🔐 Confirming sign up for:', username);

      const formattedPhoneNumber = this.formatPhoneNumber(username);

      const confirmInput: ConfirmSignUpInput = {
        username: formattedPhoneNumber,
        confirmationCode: confirmationCode,
      };

      const result = await confirmSignUp(confirmInput);

      console.log('✅ Sign up confirmed:', result);

      return {
        success: true,
        isSignUpComplete: result.isSignUpComplete,
      };
    } catch (error: any) {
      console.error('❌ Sign up confirmation failed:', error);
      
      return {
        success: false,
        isSignUpComplete: false,
        error: this.parseAuthError(error),
      };
    }
  }

  // Resend sign up code
  async resendSignUpCode(username: string): Promise<SignUpResult> {
    try {
      console.log('🔐 Resending sign up code for:', username);

      const formattedPhoneNumber = this.formatPhoneNumber(username);

      const resendInput: ResendSignUpCodeInput = {
        username: formattedPhoneNumber,
      };

      const result = await resendSignUpCode(resendInput);

      console.log('✅ Code resent:', result);

      return {
        success: true,
        isSignUpComplete: false,
      };
    } catch (error: any) {
      console.error('❌ Resend code failed:', error);
      
      return {
        success: false,
        isSignUpComplete: false,
        error: this.parseAuthError(error),
      };
    }
  }

  // Sign in with custom auth flow (passwordless)
  async signIn(data: { phoneNumber: string }): Promise<AuthResult> {
    try {
      console.log('🔐 Starting sign in for:', data.phoneNumber);

      const formattedPhoneNumber = this.formatPhoneNumber(data.phoneNumber);

      const signInInput: SignInInput = {
        username: formattedPhoneNumber,
        options: {
          authFlowType: 'CUSTOM_WITHOUT_SRP',
        },
      };

      const result = await signIn(signInInput);

      console.log('✅ Sign in initiated:', result);

      // Check if custom challenge is required
      if (result.isSignedIn) {
        const user = await this.getCurrentUser();
        return {
          success: true,
          user: user || undefined,
        };
      } else if (result.nextStep?.signInStep === 'CONFIRM_SIGN_IN_WITH_CUSTOM_CHALLENGE') {
        return {
          success: true,
          requiresMFA: true,
          challengeName: 'CUSTOM_CHALLENGE',
        };
      } else {
        return {
          success: false,
          error: 'Unexpected sign in state',
        };
      }
    } catch (error: any) {
      console.error('❌ Sign in failed:', error);
      
      return {
        success: false,
        error: this.parseAuthError(error),
      };
    }
  }

  // Confirm sign in with WhatsApp code
  async confirmSignIn(challengeResponse: string): Promise<AuthResult> {
    try {
      console.log('🔐 Confirming sign in with challenge response');

      const confirmInput: ConfirmSignInInput = {
        challengeResponse: challengeResponse,
      };

      const result = await confirmSignIn(confirmInput);

      console.log('✅ Sign in confirmed:', result);

      if (result.isSignedIn) {
        const user = await this.getCurrentUser();
        // Ensure tokens are also fetched and cached
        this.authTokens = await this.getAuthTokens();
        return {
          success: true,
          user: user || undefined,
        };
      } else {
        return {
          success: false,
          error: 'Sign in confirmation failed',
        };
      }
    } catch (error: any) {
      console.error('❌ Sign in confirmation failed:', error);
      
      return {
        success: false,
        error: this.parseAuthError(error),
      };
    }
  }

  // Get current user from cache or fetch from Cognito
  async getCurrentUser(): Promise<EnterpriseUser | null> {
    try {
      // Try to get authenticated user from Cognito
      const cognitoUser = await getCurrentUser();
      
      if (cognitoUser) {
        // Convert and cache the user
        const enterpriseUser = await this.convertToEnterpriseUser(cognitoUser);
        this.currentUser = enterpriseUser;
        return enterpriseUser;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // ✅ FORCE REFRESH: Refresh user attributes and get current user again
  async refreshUserAttributes(): Promise<EnterpriseUser | null> {
    try {
      console.log('🔄 Force refreshing user attributes from Cognito...');
      
      // Clear cached user to force fresh fetch
      this.currentUser = null;
      
      // Force fresh fetch from Cognito
      const cognitoUser = await getCurrentUser();
      
      if (cognitoUser) {
        // Convert with fresh attributes
        const enterpriseUser = await this.convertToEnterpriseUser(cognitoUser);
        this.currentUser = enterpriseUser;
        
        console.log('✅ User attributes refreshed successfully:', {
          userId: enterpriseUser.userId,
          organizationId: enterpriseUser.organizationId,
          organizationRole: enterpriseUser.organizationRole
        });
        
        return enterpriseUser;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error refreshing user attributes:', error);
      return null;
    }
  }

  // Sign out
  async signOut(global: boolean = false): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔐 Signing out...');
      
      await signOut({ global });
      
      // Clear state immediately (don't wait for Hub listener)
      this.currentUser = null;
      this.authTokens = null;
      
      console.log('✅ Sign out successful');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Sign out failed:', error);
      return { 
        success: false, 
        error: error.message || 'Sign out failed' 
      };
    }
  }

  // Format phone number for E.164 format
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add country code if missing (assuming US for now)
    if (!cleaned.startsWith('1') && cleaned.length === 10) {
      return `+1${cleaned}`;
    } else if (cleaned.startsWith('1')) {
      return `+${cleaned}`;
    } else {
      return `+${cleaned}`;
    }
  }

  // Parse authentication errors
  private parseAuthError(error: any): string {
    if (error instanceof AuthError) {
      switch (error.name) {
        case 'UsernameExistsException':
          return 'An account with this phone number already exists';
        case 'InvalidParameterException':
          return 'Invalid phone number format';
        case 'CodeMismatchException':
          return 'Invalid verification code';
        case 'ExpiredCodeException':
          return 'Verification code has expired';
        case 'LimitExceededException':
          return 'Too many attempts. Please try again later';
        case 'NotAuthorizedException':
          return 'Invalid credentials';
        case 'UserNotConfirmedException':
          return 'Account not verified. Please check your phone for verification code';
        case 'UserNotFoundException':
          return 'Account not found. Please sign up first';
        default:
          return error.message || 'Authentication failed';
      }
    }
    
    return error.message || 'Unknown error occurred';
  }

  // Get cached user
  getCachedUser(): EnterpriseUser | null {
    return this.currentUser;
  }

  // Get cached auth tokens
  getCachedTokens(): AuthTokens | null {
    return this.authTokens;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.currentUser;
  }

  // Check if user is authenticated (async version for reliable checking)
  async isAuthenticatedAsync(): Promise<boolean> {
    try {
      const currentUser = await getCurrentUser();
      return !!currentUser;
    } catch (error) {
      return false;
    }
  }

  // Cleanup
  cleanup() {
    if (this.hubUnsubscribe) {
      this.hubUnsubscribe();
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService; 