// @ts-nocheck
// Utility functions for Notefy Platform
// Common helpers for formatting, validation, and data manipulation

import { type ClassValue, clsx } from "clsx"
import { twMerge } from 'tailwind-merge';

// Tailwind CSS class merging utility
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Phone number formatting and validation
export class PhoneUtils {
  // Format phone number for display - handles stored format without + prefix
  static formatForDisplay(phoneNumber: string): string {
    if (!phoneNumber) return '';
    
    // Handle phone numbers stored without + prefix
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // If already has + prefix, return as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    
    // Add + prefix for international numbers
    if (cleaned.length >= 10) {
      // UAE numbers: 971XXXXXXXXX (12 digits total)
      if (cleaned.startsWith('971') && cleaned.length === 12) {
        return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
      }
      // US numbers: 1XXXXXXXXXX (11 digits total)
      else if (cleaned.startsWith('1') && cleaned.length === 11) {
        return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
      }
      // Other international numbers
      else if (cleaned.length >= 10) {
        return `+${cleaned}`;
      }
    }
    
    return phoneNumber;
  }

  // Format phone number for E.164 (international standard)
  static formatForE164(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    if (cleaned.length === 10) {
      return `+1${cleaned}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return `+${cleaned}`;
    } else {
      return `+${cleaned}`;
    }
  }

  // Validate phone number format
  static isValid(phoneNumber: string): boolean {
    const cleaned = phoneNumber.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 15;
  }

  // Get country code from phone number
  static getCountryCode(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return '1';
    } else if (cleaned.length === 10) {
      return '1'; // Assume US for 10-digit numbers
    } else {
      // Extract country code (simplified logic)
      return cleaned.slice(0, cleaned.length - 10);
    }
  }
}

// Date and time utilities
export class DateUtils {
  // Format date for display
  static formatForDisplay(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Format date and time for display
  static formatDateTimeForDisplay(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Format relative time (e.g., "2 hours ago")
  static formatRelativeTime(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diff = now.getTime() - dateObj.getTime();
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }

  // Check if date is today
  static isToday(date: string | Date): boolean {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    
    return dateObj.toDateString() === today.toDateString();
  }

  // Check if date is yesterday
  static isYesterday(date: string | Date): boolean {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return dateObj.toDateString() === yesterday.toDateString();
  }
}

// String utilities
export class StringUtils {
  // Capitalize first letter
  static capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  // Convert to title case
  static toTitleCase(str: string): string {
    return str.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  // Truncate string with ellipsis
  static truncate(str: string, length: number): string {
    if (str.length <= length) return str;
    return str.substring(0, length) + '...';
  }

  // Generate random string
  static generateRandomId(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Extract initials from name
  static getInitials(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  // Clean and normalize text
  static normalizeText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .toLowerCase();
  }
}

// Number utilities
export class NumberUtils {
  // Format number with commas
  static formatWithCommas(num: number): string {
    return num.toLocaleString('en-US');
  }

  // Format as currency
  static formatAsCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  // Format as percentage
  static formatAsPercentage(value: number, decimals: number = 1): string {
    return `${(value * 100).toFixed(decimals)}%`;
  }

  // Format file size
  static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  }

  // Generate random number in range
  static randomInRange(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
}

// Array utilities
export class ArrayUtils {
  // Group array by key
  static groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key]);
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }

  // Remove duplicates from array
  static unique<T>(array: T[]): T[] {
    return [...new Set(array)];
  }

  // Sort array by multiple keys
  static sortBy<T>(array: T[], ...keys: (keyof T)[]): T[] {
    return array.sort((a, b) => {
      for (const key of keys) {
        const aVal = a[key];
        const bVal = b[key];
        
        if (aVal < bVal) return -1;
        if (aVal > bVal) return 1;
      }
      return 0;
    });
  }

  // Chunk array into smaller arrays
  static chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  // Get last item from array
  static last<T>(array: T[]): T | undefined {
    return array[array.length - 1];
  }

  // Check if array is empty
  static isEmpty<T>(array: T[]): boolean {
    return array.length === 0;
  }
}

// Object utilities
export class ObjectUtils {
  // Deep clone object
  static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  // Check if object is empty
  static isEmpty(obj: object): boolean {
    return Object.keys(obj).length === 0;
  }

  // Pick specific keys from object
  static pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
    const result = {} as Pick<T, K>;
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key];
      }
    });
    return result;
  }

  // Omit specific keys from object
  static omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
    const result = { ...obj };
    keys.forEach(key => {
      delete result[key];
    });
    return result;
  }

  // Merge objects deeply
  static deepMerge<T>(target: T, source: Partial<T>): T {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = result[key];
        
        if (
          typeof sourceValue === 'object' &&
          sourceValue !== null &&
          typeof targetValue === 'object' &&
          targetValue !== null
        ) {
          result[key] = this.deepMerge(targetValue, sourceValue);
        } else {
          result[key] = sourceValue!;
        }
      }
    }
    
    return result;
  }
}

// Validation utilities
export class ValidationUtils {
  // Email validation
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // URL validation
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Check if string is empty or whitespace
  static isEmptyOrWhitespace(str: string): boolean {
    return !str || str.trim().length === 0;
  }

  // Check if value is numeric
  static isNumeric(value: string): boolean {
    return !isNaN(Number(value)) && !isNaN(parseFloat(value));
  }

  // Password strength validation
  static validatePasswordStrength(password: string): {
    score: number;
    suggestions: string[];
  } {
    let score = 0;
    const suggestions: string[] = [];
    
    if (password.length >= 8) {
      score += 1;
    } else {
      suggestions.push('Use at least 8 characters');
    }
    
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      suggestions.push('Include lowercase letters');
    }
    
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      suggestions.push('Include uppercase letters');
    }
    
    if (/[0-9]/.test(password)) {
      score += 1;
    } else {
      suggestions.push('Include numbers');
    }
    
    if (/[^A-Za-z0-9]/.test(password)) {
      score += 1;
    } else {
      suggestions.push('Include special characters');
    }
    
    return { score, suggestions };
  }
}

// Local storage utilities
export class StorageUtils {
  // Set item in localStorage
  static setItem(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error setting localStorage item:', error);
    }
  }

  // Get item from localStorage
  static getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error getting localStorage item:', error);
      return null;
    }
  }

  // Remove item from localStorage
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing localStorage item:', error);
    }
  }

  // Clear all localStorage
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  // Check if localStorage is available
  static isAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, 'test');
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
}

// Error handling utilities
export class ErrorUtils {
  // Format error message for display
  static formatErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message && typeof error.message === 'string') {
      return error.message;
    }
    
    if (error?.error?.message && typeof error.error.message === 'string') {
      return error.error.message;
    }
    
    // Handle GraphQL errors array
    if (error?.errors && Array.isArray(error.errors) && error.errors.length > 0) {
      const errorMessages = error.errors
        .map((err: any) => err?.message)
        .filter((msg: any) => typeof msg === 'string')
        .join(', ');
      if (errorMessages) {
        return errorMessages;
      }
    }
    
    return 'An unknown error occurred';
  }

  // Log error with context
  static logError(error: any, context?: string): void {
    console.error(`[${context || 'Error'}]`, error);
    
    // In production, you might want to send this to an error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Send to error tracking service
      // e.g., Sentry, Rollbar, etc.
    }
  }

  // Create user-friendly error messages
  static getUserFriendlyMessage(error: any): string {
    const errorMessage = this.formatErrorMessage(error);
    
    // Ensure errorMessage is a string
    if (!errorMessage || typeof errorMessage !== 'string') {
      return 'Something went wrong. Please try again.';
    }
    
    // Map common error patterns to user-friendly messages
    const errorMappings: Record<string, string> = {
      'Network Error': 'Connection problem. Please check your internet connection.',
      'Unauthorized': 'You are not authorized to perform this action.',
      'Forbidden': 'You do not have permission to access this resource.',
      'Not Found': 'The requested resource was not found.',
      'Internal Server Error': 'A server error occurred. Please try again later.',
      'Bad Request': 'Invalid request. Please check your input.',
    };
    
    for (const [pattern, message] of Object.entries(errorMappings)) {
      if (errorMessage.includes(pattern)) {
        return message;
      }
    }
    
    return 'Something went wrong. Please try again.';
  }
}

// RTL (Right-to-Left) utilities
export class RTLUtils {
  // Detect if text contains RTL characters
  static isRTL(text: string): boolean {
    const rtlChars = /[\u0590-\u083F]|[\u08A0-\u08FF]|[\uFB1D-\uFDFF]|[\uFE70-\uFEFF]/mg;
    return rtlChars.test(text);
  }

  // Get text direction based on content
  static getTextDirection(text: string): 'ltr' | 'rtl' {
    return this.isRTL(text) ? 'rtl' : 'ltr';
  }

  // Get appropriate alignment for text
  static getTextAlign(text: string, isOutbound: boolean): 'left' | 'right' {
    const isTextRTL = this.isRTL(text);

    if (isOutbound) {
      return isTextRTL ? 'left' : 'right';
    } else {
      return isTextRTL ? 'right' : 'left';
    }
  }
}

// Accessibility utilities
export class A11yUtils {
  // Generate unique IDs for accessibility
  static generateId(prefix: string = 'element'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Format time for screen readers
  static formatTimeForScreenReader(date: Date): string {
    return date.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  // Get message status description for screen readers
  static getMessageStatusDescription(status: string): string {
    switch (status) {
      case 'sending':
        return 'Message is being sent';
      case 'sent':
        return 'Message sent';
      case 'delivered':
        return 'Message delivered';
      case 'read':
        return 'Message read';
      case 'failed':
        return 'Message failed to send';
      default:
        return 'Message status unknown';
    }
  }

  // Get voice message description for screen readers
  static getVoiceMessageDescription(duration: number, isOutbound: boolean): string {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    const timeStr = minutes > 0 ? `${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}` : `${seconds} second${seconds !== 1 ? 's' : ''}`;
    const direction = isOutbound ? 'sent' : 'received';
    return `Voice message ${direction}, duration ${timeStr}`;
  }
}

// All utilities are already exported individually above

// Default export
export default {
  cn,
  PhoneUtils,
  DateUtils,
  StringUtils,
  NumberUtils,
  ArrayUtils,
  ObjectUtils,
  ValidationUtils,
  StorageUtils,
  ErrorUtils,
  RTLUtils,
  A11yUtils,
};