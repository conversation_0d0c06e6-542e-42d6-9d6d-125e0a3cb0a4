// AWS Amplify Gen 2 Configuration for Notefy Platform
// Integrates Cognito passwordless authentication with AppSync GraphQL
// @ts-nocheck
import { Amplify } from 'aws-amplify';
import type { ResourcesConfig } from 'aws-amplify';

// Feature flags for gradual rollout
export const featureFlags = {
  passwordlessAuth: true,
  whatsappVerification: true,
  realTimeSync: true,
  offlineSupport: true,
  analytics: true
} as const;

// Authentication configuration types
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any | null;
  error: string | null;
}

export interface SignUpData {
  phoneNumber: string;
  businessName: string;
  email?: string;
  termsAccepted: boolean;
}

export interface SignInData {
  phoneNumber: string;
}

export interface ResetPasswordData {
  phoneNumber: string;
}

export interface ConfirmResetPasswordData {
  phoneNumber: string;
  confirmationCode: string;
  newPassword: string;
}

// AWS Amplify Configuration
export const amplifyConfig: ResourcesConfig = {
  Auth: {
    Cognito: {
      // User Pool Configuration
      userPoolId: import.meta.env.VITE_USER_POOL_ID || '',
      userPoolClientId: import.meta.env.VITE_USER_POOL_CLIENT_ID || '',
      // Identity Pool Configuration (required for S3 access)
      identityPoolId: import.meta.env.VITE_IDENTITY_POOL_ID || '',
      
      // Authentication flow configuration
      loginWith: {
        phone: true,
        email: false,
        username: false,
      },
      
      // Passwordless configuration
      signUpVerificationMethod: 'code',
      userAttributes: {
        phone_number: {
          required: true,
        },
        email: {
          required: false,
        },
      },
      
      // MFA Configuration (disabled for custom auth)
      mfa: {
        status: 'off',
        smsEnabled: false,
        totpEnabled: false,
      },
      
      // Password policy (disabled for passwordless)
      passwordFormat: {
        requireLowercase: false,
        requireUppercase: false,
        requireNumbers: false,
        requireSpecialCharacters: false,
        minLength: 8,
      },
    },
  },
  
  API: {
    GraphQL: {
      // AppSync Configuration
      endpoint: import.meta.env.VITE_GRAPHQL_ENDPOINT || '',
      region: import.meta.env.VITE_AWS_REGION || 'us-east-1',
      defaultAuthMode: 'userPool' as const,
      
      // Real-time endpoint for subscriptions
      realtimeEndpoint: import.meta.env.VITE_GRAPHQL_REALTIME_ENDPOINT || '',
      
      // API Key fallback for public operations
      apiKey: import.meta.env.VITE_GRAPHQL_API_KEY || '',
    },
  },
  
  Storage: {
    S3: {
      // S3 Configuration for file uploads
      bucket: import.meta.env.VITE_S3_BUCKET || '',
      region: import.meta.env.VITE_AWS_REGION || 'us-east-1',
    },
  },
  
  Analytics: {
    Pinpoint: {
      // Analytics configuration
      appId: import.meta.env.VITE_PINPOINT_APP_ID || '',
      region: import.meta.env.VITE_AWS_REGION || 'us-east-1',
    },
  },
};

// Environment validation
const validateEnvironment = () => {
  const requiredEnvVars = [
    'VITE_USER_POOL_ID',
    'VITE_USER_POOL_CLIENT_ID',
    'VITE_IDENTITY_POOL_ID',
    'VITE_GRAPHQL_ENDPOINT',
    'VITE_AWS_REGION',
  ];
  
  const missingVars = requiredEnvVars.filter(
    varName => !import.meta.env[varName]
  );
  
  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);
  }
};

// Initialize Amplify
export const configureAmplify = () => {
  try {
    // Validate environment
    validateEnvironment();
    
    // Configure Amplify
    Amplify.configure(amplifyConfig);
    
    console.log('✅ Amplify configured successfully');
    
    // Development logging
    if (import.meta.env.DEV) {
      console.log('🔧 Amplify Configuration:', {
        auth: {
          userPoolId: amplifyConfig.Auth?.Cognito?.userPoolId,
          loginWith: amplifyConfig.Auth?.Cognito?.loginWith,
        },
        api: {
          endpoint: amplifyConfig.API?.GraphQL?.endpoint,
          authMode: amplifyConfig.API?.GraphQL?.defaultAuthMode,
        },
        features: featureFlags,
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Amplify configuration failed:', error);
    return false;
  }
};

// Export default configuration
export default amplifyConfig; 