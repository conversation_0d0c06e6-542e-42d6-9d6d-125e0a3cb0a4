import { uploadData } from 'aws-amplify/storage';
import { v4 as uuidv4 } from 'uuid';

export interface UploadResult {
  success: boolean;
  key?: string;
  url?: string;
  error?: any;
}

/**
 * Uploads a file to a designated S3 path, ensuring tenant isolation and conversation context.
 * 
 * @param file The file object to upload.
 * @param organizationId The ID of the organization to ensure tenant data isolation.
 * @param conversationId The ID of the conversation for context-specific storage.
 * @param phoneNumberId The phone number ID to match the existing bucket structure.
 * @param progressCallback An optional callback to track upload progress.
 * @returns A promise that resolves with the result of the upload.
 */
export const uploadFileToS3 = async (
  file: File,
  organizationId: string,
  conversationId: string,
  phoneNumberId: string,
  // TODO: Find the correct type for this progress event
  progressCallback?: (progress: any) => void
): Promise<UploadResult> => {
  if (!organizationId || !conversationId || !phoneNumberId) {
    return { success: false, error: new Error("Organization ID, Conversation ID, and Phone Number ID are required for upload.") };
  }

  const fileExtension = file.name.split('.').pop();
  const fileName = `${uuidv4()}.${fileExtension}`;
  // Use 'uploads/' prefix to distinguish from inbound media and trigger processing
  const s3Key = `uploads/${organizationId}/${phoneNumberId}/${conversationId}/${fileName}`;

  try {
    const result = await uploadData({
      key: s3Key,
      data: file,
      options: {
        contentType: file.type,
        onProgress: progressCallback,
      }
    }).result;

    return {
      success: true,
      key: result.key,
      url: result.key, // Return the key as the 'url' to be sent in the mutation
    };
  } catch (error) {
    console.error("Error uploading file to S3:", error);
    return { success: false, error };
  }
}; 