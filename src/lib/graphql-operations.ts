// GraphQL Operations for Notefy Platform
// Contains all queries, mutations, subscriptions, and type definitions

// ============================================================================
// Type Definitions
// ============================================================================

export enum SubscriptionTier {
  FREE = 'FREE',
  BASIC = 'BASIC',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE'
}

export interface User {
  id: string;
  phoneNumber: string;
  businessName: string;
  email?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferences;
  whatsappConnections?: WhatsAppConnection[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    whatsapp: boolean;
    push: boolean;
  };
  language: string;
  timezone: string;
}

export interface WhatsAppConnection {
  id: string;
  userId?: string;
  organizationId?: string;
  phoneNumber: string;
  displayName: string;
  businessAccountId: string;
  phoneNumberId: string;
  webhookUrl?: string;
  accessToken?: string;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'DISCONNECTED' | 'ERROR';
  isVerified: boolean;
  connectedAt: string;
  lastMessageAt?: string;
  totalConversations: number;
  activeConversations: number;
  healthStatus?: 'HEALTHY' | 'CAUTION' | 'WARNING' | 'ERROR' | 'UNKNOWN';
  connectedStoresCount?: number;
  storeConnections?: {
    id: string;
    storeId: string;
    store: {
      id: string;
      shopDomain: string;
      storeName: string;
      status: string;
    };
  }[];
  businessProfile?: {
    displayName: string;
    about?: string;
    email?: string;
    website?: string;
    category?: string;
    address?: string;
    profilePictureUrl?: string;
  };
  usageStats?: {
    totalConversations: number;
    activeConversations: number;
    messagesSent?: number;
    messagesReceived?: number;
    lastActiveDate?: string;
    connectionUptime?: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  id: string;
  connectionId: string;
  customerPhoneNumber: string;
  customerName?: string;
  status: 'active' | 'archived' | 'spam';
  assignedTo?: string;
  lastMessageAt: string;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface Message {
  id: string;
  conversationId: string;
  type: 'text' | 'image' | 'document' | 'audio' | 'video' | 'location' | 'template';
  direction: 'inbound' | 'outbound';
  content: string;
  mediaUrl?: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: string;
  metadata?: Record<string, any>;
  sender?: {
    id: string;
    name: string;
    type: 'customer' | 'agent' | 'system';
  };
}

export interface ShopifyStore {
  id: string;
  userId?: string;
  organizationId?: string;
  shopDomain: string;
  storeName: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING' | 'ERROR';
  connectedAt: string;
  lastWebhookAt?: string;
  totalOrders: number;
  whatsappConnectionId?: string;
  whatsappConnection?: {
    id: string;
    displayName: string;
    phoneNumber: string;
    status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'ERROR';
  };
  webhooks?: {
    id: string;
    webhookType: string;
    status: string;
    lastTriggered?: string;
    successCount?: number;
    errorCount?: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface ShopifyProduct {
  id: string;
  storeId: string;
  shopifyProductId: string;
  title: string;
  description?: string;
  price: number;
  imageUrl?: string;
  status: 'active' | 'draft' | 'archived';
  inventory: number;
  createdAt: string;
  updatedAt: string;
}

export interface ShopifyOrder {
  id: string;
  storeId: string;
  shopifyOrderId: string;
  orderNumber: string;
  customerEmail?: string;
  customerPhone?: string;
  totalPrice: number;
  status: string;
  fulfillmentStatus?: string;
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// Input Types
// ============================================================================

export interface UserPreferencesInput {
  theme?: 'light' | 'dark' | 'system';
  notifications?: {
    whatsapp?: boolean;
    push?: boolean;
  };
  language?: string;
  timezone?: string;
}

export interface SendMessageInput {
  conversationId: string;
  type: 'text' | 'image' | 'document' | 'audio' | 'video' | 'location' | 'template';
  content: string;
  mediaUrl?: string;
  templateName?: string;
  templateParams?: Record<string, any>;
}

export interface CreateWhatsAppConnectionInput {
  phoneNumberId: string;
  businessPhoneNumber: string;
  displayName: string;
  accessToken: string;
  webhookUrl?: string;
}

export interface UpdateWhatsAppConnectionInput {
  id: string;
  displayName?: string;
  webhookUrl?: string;
  status?: 'connected' | 'disconnected' | 'pending' | 'error';
}

// ============================================================================
// WhatsApp Business API Enterprise Onboarding Types
// ============================================================================

export interface InitiateEmbeddedSignupInput {
  configId: string;
  redirectUri: string;
  state?: string;
  userId?: string;
  organizationId?: string;
}

export interface CompleteEmbeddedSignupInput {
  code: string;
  state: string;
  userId: string;
  organizationId?: string;
}

export interface VerifyBusinessPhoneInput {
  phoneNumberId: string;
  verificationMethod: 'SMS' | 'VOICE';
  locale?: string;
}

export interface ConfirmVerificationInput {
  phoneNumberId: string;
  code: string;
}

export interface UpdateBusinessProfileInput {
  phoneNumberId: string;
  displayName?: string;
  about?: string;
  address?: string;
  description?: string;
  email?: string;
  websites?: string[];
  vertical?: string;
  profilePictureUrl?: string;
  businessHours?: {
    [key: string]: {
      openTime: string;
      closeTime: string;
    };
  };
}

export interface ConfigureWebhooksInput {
  phoneNumberId: string;
  webhookUrl: string;
  webhookFields: string[];
  verifyToken: string;
  businessAccountId?: string;
}

export interface ActivateNumberInput {
  phoneNumberId: string;
  businessAccountId: string;
  messagingProduct: 'whatsapp';
}

export interface AssociateWithShopifyInput {
  whatsappConnectionId: string;
  shopifyStoreId: string;
  routingRules: {
    orderEvents: boolean;
    customerEvents: boolean;
    productEvents: boolean;
  };
}

export interface UpdateConnectionStatusInput {
  connectionId: string;
  status?: 'connected' | 'disconnected' | 'pending' | 'error';
  verificationStatus?: 'verified' | 'unverified' | 'pending';
  accountReviewStatus?: 'approved' | 'pending' | 'rejected';
  statusReason?: string;
}

export interface BusinessVerificationInput {
  businessAccountId: string;
  businessName: string;
  businessType: string;
  businessCategory: string;
  businessDescription?: string;
  businessWebsite?: string;
  businessEmail: string;
  businessAddress: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
}

export interface UploadDocumentsInput {
  businessAccountId: string;
  documentType: 'business_license' | 'tax_certificate' | 'identity_document';
  fileName: string;
  fileData: string; // base64 encoded
}

export interface WebhookTokenInput {
  businessAccountId: string;
  phoneNumberId: string;
}

export interface TestWebhookInput {
  phoneNumberId: string;
  webhookUrl: string;
  verifyToken: string;
}

export interface CreateTemplateInput {
  businessAccountId: string;
  name: string;
  category: 'AUTHENTICATION' | 'MARKETING' | 'UTILITY';
  language: string;
  components: TemplateComponent[];
}

export interface TemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  text?: string;
  buttons?: TemplateButton[];
  examples?: string[][];
}

export interface TemplateButton {
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER';
  text: string;
  url?: string;
  phoneNumber?: string;
}

// ============================================================================
// Enhanced WhatsApp Connection Interface
// ============================================================================

export interface EnhancedWhatsAppConnection extends WhatsAppConnection {
  businessAccountId: string;
  accountReviewStatus?: 'approved' | 'pending' | 'rejected';
  businessProfile?: {
    displayName: string;
    about?: string;
    address?: string;
    description?: string;
    email?: string;
    websites?: string[];
    vertical?: string;
    profilePictureUrl?: string;
  };
  features?: {
    messaging: boolean;
    templates: boolean;
    businessProfile: boolean;
    commerce: boolean;
  };
  limits?: {
    dailyMessages: number;
    monthlyMessages: number;
    templateMessages: number;
  };
  webhookConfig?: {
    url: string;
    fields: string[];
    verifyToken: string;
    isActive: boolean;
  };
  associatedStores?: string[];
  lastHealthCheck?: string;
  healthStatus?: 'HEALTHY' | 'WARNING' | 'ERROR' | 'CAUTION' | 'UNKNOWN';
}

export interface ConnectShopifyStoreInput {
  storeName: string;
  domain: string;
  accessToken: string;
  syncEnabled?: boolean;
}

export interface UpdateConversationInput {
  id: string;
  status?: 'active' | 'archived' | 'spam';
  assignedTo?: string;
  tags?: string[];
}

export interface CustomerContext {
  businessPhone: string;
  customerPhone: string;
  customerName: string;
  lastMessageTimestamp?: string;
  lastMessageContent?: string;
  messageCount: number;
  conversationStatus: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// User Record Interface for Authentication
export interface UserRecord {
  user_id: string;
  organization_id: string;
  organization_role: string;
  phone_number: string;
  profile: {
    given_name: string;
    family_name: string;
    display_name: string;
    phone_number: string;
    avatar_url?: string;
    timezone: string;
    language: string;
    preferences: {
      whatsapp_notifications: boolean;
      sms_notifications: boolean;
      push_notifications: boolean;
      marketing_messages: boolean;
    };
  };
  subscription_tier: string;
  status: string;
  phone_verified: boolean;
  whatsapp_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

// ============================================================================
// GraphQL Queries
// ============================================================================

export const GET_CURRENT_USER = `
  query GetCurrentUser {
    getCurrentUser {
      id
      phoneNumber
      businessName
      email
      isVerified
      createdAt
      updatedAt
      preferences {
        theme
        notifications {
          whatsapp
          push
        }
        language
        timezone
      }
    }
  }
`;

export const GET_WHATSAPP_CONNECTIONS = `
  query GetWhatsAppConnections($organizationId: ID, $status: ConnectionStatus) {
    listWhatsAppConnections(organizationId: $organizationId, status: $status) {
      id
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      status
      isVerified
      connectedAt
      lastMessageAt
      totalConversations
      activeConversations
      healthStatus
      connectedStoresCount
      storeConnections {
        id
        storeId
        store {
          id
          shopDomain
          storeName
          status
        }
      }
      businessProfile {
        displayName
        about
        email
        website
        category
        address
        profilePictureUrl
      }
      usageStats {
        totalConversations
        activeConversations
        messagesSent
        messagesReceived
        lastActiveDate
        connectionUptime
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_USER_WHATSAPP_CONNECTIONS = `
  query GetUserWhatsAppConnections {
    getUserWhatsAppConnections {
      id
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      status
      isVerified
      connectedAt
      lastMessageAt
      totalConversations
      activeConversations
      healthStatus
      connectedStoresCount
      storeConnections {
        id
        storeId
        store {
          id
          shopDomain
          storeName
          status
        }
      }
      businessProfile {
        displayName
        about
        email
        website
        category
        address
        profilePictureUrl
      }
      usageStats {
        totalConversations
        activeConversations
        messagesSent
        messagesReceived
        lastActiveDate
        connectionUptime
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_WHATSAPP_CONNECTION = `
  query GetWhatsAppConnection($connectionId: ID!) {
    getWhatsAppConnection(connectionId: $connectionId) {
      id
      userId
      phoneNumberId
      businessPhoneNumber
      displayName
      status
      verificationStatus
      webhookUrl
      createdAt
      updatedAt
      messageCount
      lastMessageAt
    }
  }
`;

// OLD CONVERSATION OPERATIONS REMOVED - WILL BE REPLACED

// OLD CONVERSATION OPERATIONS REMOVED - WILL BE REPLACED WITH NEW CHAT SYSTEM

// ============================================================================
// Shopify Store Operations
// ============================================================================

export const GET_SHOPIFY_STORES = `
  query GetShopifyStores($organizationId: ID, $status: StoreStatus) {
    listShopifyStores(organizationId: $organizationId, status: $status) {
      id
      shopDomain
      storeName
      status
      connectedAt
      lastWebhookAt
      totalOrders
      whatsappConnectionId
      whatsappConnection {
        id
        displayName
        phoneNumber
        status
      }
      webhooks {
        id
        webhookType
        status
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_USER_SHOPIFY_STORES = `
  query GetUserShopifyStores {
    getUserShopifyStores {
      id
      shopDomain
      storeName
      status
      connectedAt
      lastWebhookAt
      totalOrders
      whatsappConnectionId
      whatsappConnection {
        id
        displayName
        phoneNumber
        status
      }
      webhooks {
        id
        webhookType
        status
        lastTriggered
        successCount
        errorCount
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_ANALYTICS_OVERVIEW = `
  query GetAnalyticsOverview($timeRange: String!, $organizationId: ID) {
    getAnalyticsOverview(timeRange: $timeRange, organizationId: $organizationId) {
      totalMessages
      totalConversations
      responseTime
      customerSatisfaction
      activeConnections
      revenue
      timeSeriesData {
        timestamp
        messages
        conversations
        revenue
      }
    }
  }
`;

// User Record Query for Authentication
export const GET_USER_RECORD = `
  query GetUserRecord($userId: ID!) {
    getUserRecord(userId: $userId) {
      user_id
      organization_id
      organization_role
      phone_number
      profile {
        given_name
        family_name
        display_name
        phone_number
        avatar_url
        timezone
        language
        preferences {
          whatsapp_notifications
          sms_notifications
          push_notifications
          marketing_messages
        }
      }
      subscription_tier
      status
      phone_verified
      whatsapp_verified
      created_at
      updated_at
      last_login_at
    }
  }
`;

// ============================================================================
// GraphQL Mutations
// ============================================================================

export const UPDATE_USER_PREFERENCES = `
  mutation UpdateUserPreferences($preferences: UserPreferencesInput!) {
    updateUserPreferences(preferences: $preferences) {
      id
      phoneNumber
      businessName
      email
      isVerified
      createdAt
      updatedAt
      preferences {
        theme
        notifications {
          email
          sms
          push
        }
        language
        timezone
      }
    }
  }
`;

export const SEND_MESSAGE = `
  mutation SendMessage($input: SendMessageInput!) {
    sendMessage(input: $input) {
      id
      conversationId
      type
      direction
      content
      mediaUrl
      status
      timestamp
      success
      errorMessage
      whatsappMessageId
    }
  }
`;

export const UPDATE_CONVERSATION_STATUS = `
  mutation UpdateConversationStatus($input: UpdateConversationInput!) {
    updateConversation(input: $input) {
      id
      connectionId
      customerPhoneNumber
      customerName
      status
      assignedTo
      lastMessageAt
      unreadCount
      createdAt
      updatedAt
      tags
    }
  }
`;

export const ASSIGN_CONVERSATION = `
  mutation AssignConversation($conversationId: ID!, $assignedTo: String!) {
    assignConversation(conversationId: $conversationId, assignedTo: $assignedTo) {
      id
      connectionId
      customerPhoneNumber
      customerName
      status
      assignedTo
      lastMessageAt
      unreadCount
      createdAt
      updatedAt
      tags
    }
  }
`;

export const CREATE_WHATSAPP_CONNECTION = `
  mutation CreateWhatsAppConnection($input: CreateWhatsAppConnectionInput!) {
    createWhatsAppConnection(input: $input) {
      id
      userId
      phoneNumberId
      businessPhoneNumber
      displayName
      status
      verificationStatus
      webhookUrl
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_WHATSAPP_CONNECTION = `
  mutation UpdateWhatsAppConnection($input: UpdateWhatsAppConnectionInput!) {
    updateWhatsAppConnection(input: $input) {
      id
      userId
      phoneNumberId
      businessPhoneNumber
      displayName
      status
      verificationStatus
      webhookUrl
      createdAt
      updatedAt
    }
  }
`;

export const CONNECT_SHOPIFY_STORE = `
  mutation ConnectShopifyStore($input: ConnectShopifyStoreInput!) {
    connectShopifyStore(input: $input) {
      id
      userId
      storeName
      domain
      status
      syncEnabled
      lastSyncAt
      createdAt
      updatedAt
    }
  }
`;

// ============================================================================
// GraphQL Subscriptions
// ============================================================================

// Real-time message notification subscription - Organization scoped
export const ON_MESSAGE_NOTIFICATION = `
subscription OnMessageNotification($organizationId: ID!) {
  onMessageNotification(organizationId: $organizationId) {
    id
    messageId
    conversationId
    organizationId
    messageType
    direction
    status
    content
    timestamp
    fromNumber
    contactName
  }
}
`;

// Media upload status subscription - Conversation scoped
export const ON_MEDIA_UPLOAD_STATUS_CHANGED = `
subscription OnMediaUploadStatusChanged($conversationId: ID!) {
  onMediaUploadStatusChanged(conversationId: $conversationId) {
    conversationId
    s3Key
    organizationId
    mediaId
    mediaType
    mediaCategory
    fileName
    fileSize
    status
    errorMessage
    createdAt
    updatedAt
    completedAt
    expiresAt
  }
}
`;

// Legacy subscription - DEPRECATED, use ON_MESSAGE_NOTIFICATION instead
export const ON_MESSAGE_RECEIVED = `
  subscription OnMessageReceived($connectionId: ID) {
    onMessageReceived(connectionId: $connectionId) {
      id
      messageId
      conversationId
      sortKey
      organizationId
      businessPhone
      customerPhone
      whatsappMessageId
      timestamp
      messageType
      direction
      status
      fromNumber
      phoneNumberId
      content
      createdAt
      updatedAt
      contactName
      wabaId
      textContent
      mediaUrl
      mediaType
      caption
    }
  }
`;

export const ON_CONVERSATION_UPDATED = `
  subscription OnConversationUpdated($connectionId: ID) {
    onConversationUpdated(connectionId: $connectionId) {
      id
      conversationId
      organizationId
      businessPhone
      customerPhone
      phoneNumberId
      wabaId
      customerName
      customerProfilePicture
      status
      assignedUserId
      messageCount
      unreadCount
      lastMessageAt
      lastMessageContent
      createdAt
      updatedAt
      tags
      shopifyCustomerId
      recentOrders {
        orderId
        orderNumber
        totalPrice
        currency
        status
        createdAt
      }
      storeId
      storeName
      messages {
        id
        messageId
        conversationId
        whatsappMessageId
        messageType
        direction
        status
        fromNumber
        contactName
        timestamp
        createdAt
        updatedAt
      }
    }
  }
`;

export const ON_CONNECTION_STATUS_CHANGED = `
  subscription OnConnectionStatusChanged($userId: ID!) {
    onConnectionStatusChanged(userId: $userId) {
      id
      userId
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      status
      isVerified
      connectedAt
      lastMessageAt
      totalConversations
      activeConversations
      businessProfile {
        displayName
        about
        email
        website
        category
        address
        profilePictureUrl
      }
      createdAt
      updatedAt
    }
  }
`;

export const ON_USER_PRESENCE_CHANGED = `
  subscription OnUserPresenceChanged($organizationId: ID) {
    onUserPresenceChanged(organizationId: $organizationId) {
      id
      phoneNumber
      email
      firstName
      lastName
      fullName
      profilePicture
      isOnline
      lastActiveAt
      updatedAt
    }
  }
`;

// Additional subscriptions for complete real-time functionality
export const ON_MESSAGE_SENT = `
  subscription OnMessageSent($organizationId: ID!) {
    onMessageSent(organizationId: $organizationId) {
      success
      error {
        code
        message
        details
      }
      message {
        id
        messageId
        conversationId
        sortKey
        organizationId
        businessPhone
        customerPhone
        whatsappMessageId
        timestamp
        messageType
        direction
        status
        fromNumber
        phoneNumberId
        content
        createdAt
        updatedAt
        contactName
        wabaId
        textContent
        mediaUrl
        mediaType
        caption
      }
    }
  }
`;

export const ON_NEW_CONVERSATION = `
  subscription OnNewConversation($connectionId: ID) {
    onNewConversation(connectionId: $connectionId) {
      id
      conversationId
      organizationId
      businessPhone
      customerPhone
      phoneNumberId
      wabaId
      customerName
      customerProfilePicture
      status
      assignedUserId
      messageCount
      unreadCount
      tags
      lastMessageAt
      lastMessageContent
      createdAt
      updatedAt
      shopifyCustomerId
      recentOrders {
        orderId
        orderNumber
        totalPrice
        currency
        status
        createdAt
      }
      storeId
      storeName
      messages {
        id
        messageId
        conversationId
        whatsappMessageId
        messageType
        direction
        status
        fromNumber
        contactName
        timestamp
        createdAt
        updatedAt
      }
    }
  }
`;

export const ON_MESSAGE_STATUS_UPDATED = `
  subscription OnMessageStatusUpdated($conversationId: ID!) {
    onMessageStatusUpdated(conversationId: $conversationId) {
      id
      conversationId
      whatsappMessageId
      status
      deliveredAt
      readAt
      updatedAt
    }
  }
`;

// ============================================================================
// CRM Queries
// ============================================================================

export const GET_CUSTOMER_CONTEXT = `
  query GetCustomerContext($businessPhone: AWSPhone!, $customerPhone: AWSPhone!) {
    getCustomerContext(businessPhone: $businessPhone, customerPhone: $customerPhone) {
      businessPhone
      customerPhone
      customerName
      lastMessageTimestamp
      lastMessageContent
      messageCount
      conversationStatus
      tags
      createdAt
      updatedAt
    }
  }
`;

export const LIST_CUSTOMERS = `
  query ListCustomers($businessPhone: AWSPhone!, $limit: Int) {
    listCustomers(businessPhone: $businessPhone, limit: $limit) {
      businessPhone
      customerPhone
      customerName
      lastMessageTimestamp
      lastMessageContent
      messageCount
      conversationStatus
      tags
      createdAt
      updatedAt
    }
  }
  `;

// ============================================================================
// Export all operations
// ============================================================================

export default {
  queries: {
    GET_CURRENT_USER,
    GET_WHATSAPP_CONNECTIONS,
    GET_USER_WHATSAPP_CONNECTIONS,
    GET_WHATSAPP_CONNECTION,
    GET_SHOPIFY_STORES,
    GET_USER_SHOPIFY_STORES,
    GET_ANALYTICS_OVERVIEW,
    GET_USER_RECORD,
    GET_CUSTOMER_CONTEXT,
    LIST_CUSTOMERS,
  },
  mutations: {
    UPDATE_USER_PREFERENCES,
    CREATE_WHATSAPP_CONNECTION,
    UPDATE_WHATSAPP_CONNECTION,
    CONNECT_SHOPIFY_STORE,
  },
  subscriptions: {
    ON_CONNECTION_STATUS_CHANGED,
    ON_USER_PRESENCE_CHANGED,
  },
}; 