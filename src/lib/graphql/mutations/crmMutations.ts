import { generateClient } from 'aws-amplify/api';

const client = generateClient();

// GraphQL mutations for CRM operations
export const CREATE_CUSTOMER = /* GraphQL */ `
  mutation CreateCustomer($input: CreateCustomerInput!) {
    createCustomer(input: $input) {
      id
      phoneId
      organizationId
      customerPhone
      customerName
      wabaId
      displayPhoneNumber
      conversationStatus
      messageCount
      shopifyStoreId
      shopifyCustomerId
      shopifyCustomerUrl
      shopifyOrdersCount
      shopifyTotalSpent
      tags
      createdAt
      updatedAt
      businessPhone
      entityId
    }
  }
`;

export const UPDATE_CUSTOMER = /* GraphQL */ `
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      id
      phoneId
      organizationId
      customerPhone
      customerName
      wabaId
      displayPhoneNumber
      conversationStatus
      messageCount
      shopifyStoreId
      shopifyCustomerId
      shopifyCustomerUrl
      shopifyOrdersCount
      shopifyTotalSpent
      tags
      lastMessageTimestamp
      lastMessageContent
      createdAt
      updatedAt
      businessPhone
      entityId
    }
  }
`;

export const DELETE_CUSTOMER = /* GraphQL */ `
  mutation DeleteCustomer($businessPhone: AWSPhone!, $customerPhone: AWSPhone!) {
    deleteCustomer(businessPhone: $businessPhone, customerPhone: $customerPhone)
  }
`;

export const ADD_CUSTOMER_TAG = /* GraphQL */ `
  mutation AddCustomerTag($businessPhone: AWSPhone!, $customerPhone: AWSPhone!, $tag: String!) {
    addCustomerTag(businessPhone: $businessPhone, customerPhone: $customerPhone, tag: $tag) {
      businessPhone
      customerPhone
      customerName
      tags
      updatedAt
    }
  }
`;

export const REMOVE_CUSTOMER_TAG = /* GraphQL */ `
  mutation RemoveCustomerTag($businessPhone: AWSPhone!, $customerPhone: AWSPhone!, $tag: String!) {
    removeCustomerTag(businessPhone: $businessPhone, customerPhone: $customerPhone, tag: $tag) {
      businessPhone
      customerPhone
      customerName
      tags
      updatedAt
    }
  }
`;

export const UPDATE_CUSTOMER_NOTES = /* GraphQL */ `
  mutation UpdateCustomerNotes($businessPhone: AWSPhone!, $customerPhone: AWSPhone!, $notes: String!) {
    updateCustomerNotes(businessPhone: $businessPhone, customerPhone: $customerPhone, notes: $notes) {
      businessPhone
      customerPhone
      customerName
      notes
      updatedAt
    }
  }
`;

// Input types for mutations
export interface CreateCustomerInput {
  phoneId: string;
  organizationId: string;
  customerPhone: string;
  customerName: string;
  wabaId?: string;
  displayPhoneNumber?: string;
  tags?: string[];
  shopifyStoreId?: string;
}

export interface UpdateCustomerInput {
  id: string;
  customerName?: string;
  conversationStatus?: string;
  tags?: string[];
  shopifyStoreId?: string;
}

// Mutation functions using Amplify
export const createCustomer = async (input: CreateCustomerInput) => {
  try {
    const response = await client.graphql({
      query: CREATE_CUSTOMER,
      variables: { input }
    });
    
    if ('data' in response && response.data?.createCustomer) {
      return response.data.createCustomer;
    }
    throw new Error('Failed to create customer');
  } catch (error) {
    console.error('Error creating customer:', error);
    throw error;
  }
};

export const updateCustomer = async (input: UpdateCustomerInput) => {
  try {
    const response = await client.graphql({
      query: UPDATE_CUSTOMER,
      variables: { input }
    });
    
    if ('data' in response && response.data?.updateCustomer) {
      return response.data.updateCustomer;
    }
    throw new Error('Failed to update customer');
  } catch (error) {
    console.error('Error updating customer:', error);
    throw error;
  }
};

export const deleteCustomer = async (businessPhone: string, customerPhone: string) => {
  try {
    const response = await client.graphql({
      query: DELETE_CUSTOMER,
      variables: { businessPhone, customerPhone }
    });
    
    if ('data' in response) {
      return response.data?.deleteCustomer;
    }
    throw new Error('Failed to delete customer');
  } catch (error) {
    console.error('Error deleting customer:', error);
    throw error;
  }
};

export const addCustomerTag = async (businessPhone: string, customerPhone: string, tag: string) => {
  try {
    const response = await client.graphql({
      query: ADD_CUSTOMER_TAG,
      variables: { businessPhone, customerPhone, tag }
    });
    
    if ('data' in response && response.data?.addCustomerTag) {
      return response.data.addCustomerTag;
    }
    throw new Error('Failed to add tag');
  } catch (error) {
    console.error('Error adding customer tag:', error);
    throw error;
  }
};

export const removeCustomerTag = async (businessPhone: string, customerPhone: string, tag: string) => {
  try {
    const response = await client.graphql({
      query: REMOVE_CUSTOMER_TAG,
      variables: { businessPhone, customerPhone, tag }
    });
    
    if ('data' in response && response.data?.removeCustomerTag) {
      return response.data.removeCustomerTag;
    }
    throw new Error('Failed to remove tag');
  } catch (error) {
    console.error('Error removing customer tag:', error);
    throw error;
  }
};

export const updateCustomerNotes = async (businessPhone: string, customerPhone: string, notes: string) => {
  try {
    const response = await client.graphql({
      query: UPDATE_CUSTOMER_NOTES,
      variables: { businessPhone, customerPhone, notes }
    });
    
    if ('data' in response && response.data?.updateCustomerNotes) {
      return response.data.updateCustomerNotes;
    }
    throw new Error('Failed to update notes');
  } catch (error) {
    console.error('Error updating customer notes:', error);
    throw error;
  }
}; 