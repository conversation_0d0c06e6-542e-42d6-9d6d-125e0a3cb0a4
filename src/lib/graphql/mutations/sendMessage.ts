// Enhanced SendMessage mutation for AWS Amplify

// SendMessage Input matching the actual GraphQL schema
export interface SendMessageInput {
  // Core identification
  conversationId: string;
  messageType: 'TEXT' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'DOCUMENT' | 'STICKER' | 'LOCATION' | 'CONTACT';
  
  // *** MANDATORY TENANT ISOLATION ***
  organizationId: string;  // Required - validated against user's organization
  
  // Text messages
  text?: string;
  
  // Media messages (image, video, audio, document)
  mediaUrl?: string;
  mediaType?: string;
  caption?: string;
  fileName?: string;
  fileSize?: number;
  
  // Location messages
  latitude?: number;
  longitude?: number;
  locationName?: string;
  locationAddress?: string;
  
  // Contact messages
  contacts?: Array<{
    name: string;
    phoneNumber?: string;
    email?: string;
    organization?: string;
    address?: string;
    birthday?: string;
    url?: string;
  }>;
  
  // Message threading
  replyToMessageId?: string;
  
  // Internal tracking (auto-generated if not provided)
  messageId?: string;
}

// SendMessage Response matching the actual GraphQL schema
export interface SendMessageResponse {
  success: boolean;
  message?: {
    id: string;
    messageId: string;
    conversationId: string;
    organizationId: string;
    messageType: string;
    direction: string;
    status: string;
    content: string;
    createdAt: string;
    updatedAt: string;
    sortKey: string;
    // Content fields based on message type
    textContent?: string;
    mediaUrl?: string;
    mediaType?: string;
    caption?: string;
    fileName?: string;
    fileSize?: number;
    latitude?: number;
    longitude?: number;
    locationName?: string;
    locationAddress?: string;
    contactsData?: string;
    // User context and message threading
    sentByUserId?: string;
    replyToMessageId?: string;
    // Additional fields from schema
    whatsappMessageId?: string;
    businessPhone?: string;
    customerPhone?: string;
    phoneNumberId?: string;
    wabaId?: string;
    fromNumber?: string;
    contactName?: string;
    timestamp?: string;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// GraphQL Mutation matching the actual AppSync schema
export const SEND_MESSAGE = `
  mutation SendMessage($input: SendMessageInput!) {
    sendMessage(input: $input) {
      success
      message {
        # Core identification fields
        id
        messageId
        conversationId
        organizationId
        sortKey
        
        # Contact and phone fields for UI
        businessPhone
        customerPhone
        phoneNumberId
        fromNumber
        wabaId
        contactName
        
        # Message metadata
        messageType
        direction
        status
        content
        sentByUserId
        
        # Timestamps for chronological ordering
        createdAt
        updatedAt
        timestamp
        
        # Content-specific fields for easy UI access
        textContent
        mediaUrl
        mediaType
        caption
        fileName
        fileSize
        latitude
        longitude
        locationName
        locationAddress
        contactsData
        replyToMessageId
        
        # Additional fields from schema
        whatsappMessageId
      }
      error {
        code
        message
        details
      }
    }
  }
`;

// Types are already exported above with interfaces 