// WhatsApp Embedded Signup Mutation
export const PROCESS_WHATSAPP_EMBEDDED_SIGNUP = `
  mutation ProcessWhatsAppEmbeddedSignup($input: WhatsAppEmbeddedSignupInput!) {
    processWhatsAppEmbeddedSignup(input: $input) {
      success
      wabaId
      phoneNumberId
      connectionId
      displayPhoneNumber
      webhookUrl
      businessToken
      message
      errorMessage
      errorType
    }
  }
`;

// WhatsApp Store Connection Mutation
export const CONNECT_WHATSAPP_TO_STORE = `
  mutation ConnectWhatsAppToStore($input: WhatsAppStoreConnectionInput!) {
    connectWhatsAppToStore(input: $input) {
      success
      connectionId
      webhookUrl
      shopDomain
      displayPhoneNumber
      notificationSettings {
        sendOrderConfirmations
        sendShippingUpdates
        sendDeliveryConfirmations
        templateLanguage
        timezone
      }
      message
    }
  }
`;

// WhatsApp Connection Management
export const CREATE_WHATSAPP_CONNECTION = `
  mutation CreateWhatsAppConnection($input: CreateWhatsAppConnectionInput!) {
    createWhatsAppConnection(input: $input) {
      id
      phoneNumber
      displayName
      status
      businessAccountId
      phoneNumberId
      webhookUrl
      isVerified
      connectedAt
      businessProfile {
        displayName
        about
        email
        website
        category
      }
    }
  }
`;

export const UPDATE_WHATSAPP_CONNECTION = `
  mutation UpdateWhatsAppConnection($id: ID!, $input: UpdateWhatsAppConnectionInput!) {
    updateWhatsAppConnection(id: $id, input: $input) {
      id
      displayName
      status
      updatedAt
    }
  }
`;

export const DELETE_WHATSAPP_CONNECTION = `
  mutation DeleteWhatsAppConnection($id: ID!) {
    deleteWhatsAppConnection(id: $id)
  }
`;

export const VERIFY_WHATSAPP_CONNECTION = `
  mutation VerifyWhatsAppConnection($id: ID!) {
    verifyWhatsAppConnection(id: $id) {
      id
      isVerified
      status
      updatedAt
    }
  }
`;

// TypeScript interfaces
export interface WhatsAppEmbeddedSignupInput {
  wabaId: string;
  phoneNumberId: string;
  businessId?: string;
  sessionData?: Record<string, any>;
  user_id?: string;        // ✅ Backend Lambda expects user_id
  organizationId?: string;
  authorizationCode?: string;
}

export interface WhatsAppEmbeddedSignupResult {
  success: boolean;
  wabaId?: string;
  phoneNumberId?: string;
  connectionId?: string;
  displayPhoneNumber?: string;
  webhookUrl?: string;
  businessToken?: string;
  message: string;
  errorMessage?: string;
  errorType?: string;
}

export interface WhatsAppStoreConnectionInput {
  wabaId: string;
  phoneNumberId: string;
  storeId: string;
  organizationId?: string;
}

export interface CreateWhatsAppConnectionInput {
  phoneNumber: string;
  displayName: string;
  businessAccountId: string;
  phoneNumberId: string;
  accessToken: string;
  organizationId?: string;
}

export interface UpdateWhatsAppConnectionInput {
  displayName?: string;
  status?: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'DISCONNECTED' | 'ERROR';
} 