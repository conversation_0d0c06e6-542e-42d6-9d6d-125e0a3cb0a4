// GraphQL mutations for Shopify integration using AWS Amplify GraphQL client

export const INITIATE_SHOPIFY_OAUTH = `
  mutation InitiateShopifyOAuth($input: InitiateShopifyOAuthInput!) {
    initiateShopifyOAuth(input: $input) {
      authorizationUrl
      state
      shop
    }
  }
`;

export const CREATE_SHOPIFY_STORE = `
  mutation CreateShopifyStore($input: CreateShopifyStoreInput!) {
    createShopifyStore(input: $input) {
      id
      shopDomain
      storeName
      status
      connectedAt
      webhooks {
        id
        webhookType
        status
      }
    }
  }
`;

export const UPDATE_SHOPIFY_STORE = `
  mutation UpdateShopifyStore($id: ID!, $input: UpdateShopifyStoreInput!) {
    updateShopifyStore(id: $id, input: $input) {
      id
      storeName
      status
      whatsappConnectionId
      updatedAt
    }
  }
`;

export const DELETE_SHOPIFY_STORE = `
  mutation DeleteShopifyStore($id: ID!) {
    deleteShopifyStore(id: $id)
  }
`;

export const SYNC_SHOPIFY_STORE = `
  mutation SyncShopifyStore($id: ID!) {
    syncShopifyStore(id: $id) {
      id
      storeName
      status
      lastWebhookAt
      totalOrders
      updatedAt
    }
  }
`; 