import { executeQuery } from '@/lib/graphql-client';

// ==============================================================================
// Type Definitions
// ==============================================================================

export interface Customer {
  id: string;
  phone_id: string;
  organization_id: string;
  customer_phone: string;
  customer_name: string;
  conversation_status: 'active' | 'inactive' | 'archived';
  message_count: number;
  last_message_content: string;
  last_message_timestamp: string;
  created_at: string;
  updated_at: string;
  waba_id?: string;
  display_phone_number?: string;
  shopify_store_id?: string;
  shopify_customer_id?: string;
  shopify_customer_url?: string;
  shopify_total_spent?: number;
  shopify_orders_count?: number;
  tags: string[] | null;

  // Frontend-only properties, not in GraphQL schema
  businessPhone?: string; // a temporary property for context
}

export interface CustomerFilterInput {
  customerNameContains?: string;
  phoneContains?: string;
  status?: 'active' | 'inactive' | 'archived';
  businessPhone?: string;
  shopifyStoreId?: string;
  hasShopifyAccount?: boolean;
}

export interface FrontendFilters {
  businessPhone?: string;
  storeId?: string;
  searchQuery?: string;
}

// ==============================================================================
// GraphQL Queries
// ==============================================================================

const GetCustomerContextQuery = `
  query GetCustomer($businessPhone: String!, $customerPhone: String!) {
    getCustomerContext(businessPhone: $businessPhone, customerPhone: $customerPhone) {
      id
      phone_id
      organization_id
      customer_phone
      customer_name
      conversation_status
      message_count
      last_message_content
      last_message_timestamp
      created_at
      updated_at
      waba_id
      display_phone_number
      shopify_store_id
      shopify_customer_id
      shopify_customer_url
      shopify_total_spent
      shopify_orders_count
      tags
    }
  }
`;

const CustomersQuery = `
  query Customers($organizationId: ID!) {
    customers(organizationId: $organizationId) {
      items {
        id
        phone_id
        organization_id
        customer_phone
        customer_name
        conversation_status
        message_count
        last_message_content
        last_message_timestamp
        created_at
        updated_at
        waba_id
        display_phone_number
        shopify_store_id
        shopify_customer_id
        shopify_customer_url
        shopify_total_spent
        shopify_orders_count
        tags
      }
      totalCount
    }
  }
`;


// ==============================================================================
// Query Functions
// ==============================================================================

export const getCustomer = async (businessPhone: string, customerPhone: string): Promise<Customer | null> => {
  try {
    const response = await executeQuery({
      query: GetCustomerContextQuery,
      variables: { businessPhone, customerPhone }
    });

    if (response.data?.getCustomerContext) {
      return response.data.getCustomerContext;
    }
    return null;
  } catch (error) {
    console.error('Error fetching customer context:', error);
    throw new Error('Failed to fetch customer context. Please check your network connection and credentials.');
  }
};

export const listCustomers = async (variables: {
  organizationId: string;
  filter?: CustomerFilterInput;
  limit?: number;
  nextToken?: string;
}): Promise<{ items: Customer[]; totalCount: number; nextToken?: string }> => {
  try {
    const response = await executeQuery({
      query: CustomersQuery,
      variables
    });
    
    if (response.data?.customers) {
      return response.data.customers;
    }
    
    // Default empty state
    return { items: [], totalCount: 0 };
  } catch (error) {
    console.error('Error listing customers:', error);
    throw new Error('Failed to list customers. Please check your network connection and credentials.');
  }
}; 