// Chat System Queries
// Using unified resolvers created in Task 1-3

export const LIST_ORGANIZATION_CONVERSATIONS = `
  query ListOrganizationConversations($organizationId: ID!, $limit: Int, $nextToken: String, $filter: ConversationFilter) {
    listOrganizationConversations(organizationId: $organizationId, limit: $limit, nextToken: $nextToken, filter: $filter) {
      items {
        id
        conversationId
        organizationId
        businessPhone
        customerPhone
        phoneNumberId
        wabaId
        customerName
        customerProfilePicture
        status
        assignedUserId
        messageCount
        unreadCount
        lastMessageAt
        lastMessageContent
        createdAt
        updatedAt
        tags
        shopifyCustomerId
        storeId
        storeName
      }
      nextToken
      scannedCount
    }
  }
`;

export const GET_CONVERSATION_MESSAGES = `
  query GetConversationMessages($conversationId: ID!, $limit: Int, $nextToken: String, $filter: MessageFilter) {
    getConversationMessages(conversationId: $conversationId, limit: $limit, nextToken: $nextToken, filter: $filter) {
      items {
        id
        conversationId
        messageId
        sortKey
        organizationId
        businessPhone
        customerPhone
        phoneNumberId
        wabaId
        fromNumber
        contactName
        messageType
        direction
        status
        content
        textContent
        mediaUrl
        mediaType
        fileName
        caption
        latitude
        longitude
        locationName
        locationAddress
        templateName
        templateLanguage
        templateParameters
        timestamp
        createdAt
        updatedAt
        deliveredAt
        readAt
        ttl
      }
      nextToken
      scannedCount
    }
  }
`;

export const GET_CONVERSATION = `
  query GetConversation($conversationId: ID!) {
    getConversation(conversationId: $conversationId) {
      id
      conversationId
      organizationId
      businessPhone
      customerPhone
      phoneNumberId
      wabaId
      customerName
      customerProfilePicture
      status
      assignedUserId
      messageCount
      unreadCount
      lastMessageAt
      lastMessageContent
      createdAt
      updatedAt
      tags
      shopifyCustomerId
      storeId
      storeName
    }
  }
`;

// Mutations for sending messages
export const SEND_TEXT_MESSAGE = `
  mutation SendTextMessage($input: SendTextMessageInput!) {
    sendTextMessage(input: $input) {
      id
      conversationId
      messageId
      content
      direction
      status
      timestamp
      createdAt
    }
  }
`;

export const SEND_MEDIA_MESSAGE = `
  mutation SendMediaMessage($input: SendMediaMessageInput!) {
    sendMediaMessage(input: $input) {
      id
      conversationId
      messageId
      content
      mediaUrl
      mediaType
      direction
      status
      timestamp
      createdAt
    }
  }
`;

export const MARK_MESSAGE_READ = `
  mutation MarkMessageRead($input: MarkMessageReadInput!) {
    markMessageRead(input: $input) {
      success
      messageId
      readAt
    }
  }
`;

// Subscriptions for real-time updates
export const ON_MESSAGE_RECEIVED = `
  subscription OnMessageReceived($organizationId: ID!) {
    onMessageReceived(organizationId: $organizationId) {
      id
      conversationId
      messageId
      content
      direction
      status
      timestamp
      messageType
      mediaUrl
      mediaType
      fromNumber
      contactName
    }
  }
`;

export const ON_MESSAGE_STATUS_UPDATED = `
  subscription OnMessageStatusUpdated($organizationId: ID!) {
    onMessageStatusUpdated(organizationId: $organizationId) {
      messageId
      conversationId
      status
      deliveredAt
      readAt
    }
  }
`;