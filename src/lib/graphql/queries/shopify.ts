// Shopify GraphQL queries for AWS Amplify
// Using raw GraphQL strings instead of gql template literals

export const GET_SHOPIFY_STORE = `
  query GetShopifyStore($id: ID!) {
    getShopifyStore(id: $id) {
      id
      userId
      organizationId
      shopDomain
      storeName
      status
      connectedAt
      lastWebhookAt
      totalOrders
      whatsappConnectionId
      whatsappConnection {
        id
        displayName
        phoneNumber
        status
      }
      webhooks {
        id
        webhookType
        endpoint
        status
        lastTriggered
        successCount
        errorCount
        createdAt
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_SHOPIFY_STORES = `
  query ListShopifyStores($organizationId: ID, $status: StoreStatus) {
    listShopifyStores(organizationId: $organizationId, status: $status) {
      id
      shopDomain
      storeName
      status
      connectedAt
      lastWebhookAt
      totalOrders
      whatsappConnectionId
      whatsappConnection {
        id
        displayName
        phoneNumber
        status
      }
      webhooks {
        id
        webhookType
        status
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_USER_SHOPIFY_STORES = `
  query GetUserShopifyStores {
    getUserShopifyStores {
      id
      shopDomain
      storeName
      status
      connectedAt
      lastWebhookAt
      totalOrders
      whatsappConnectionId
      whatsappConnection {
        id
        displayName
        phoneNumber
        status
      }
      webhooks {
        id
        webhookType
        status
        lastTriggered
        successCount
        errorCount
      }
      createdAt
      updatedAt
    }
  }
`;

export const GET_SHOPIFY_WEBHOOKS = `
  query GetShopifyWebhooks($storeId: ID!, $webhookType: WebhookType) {
    listShopifyWebhooks(storeId: $storeId, webhookType: $webhookType) {
      id
      storeId
      webhookType
      endpoint
      status
      lastTriggered
      successCount
      errorCount
      createdAt
      updatedAt
    }
  }
`;

export const GET_SHOPIFY_ANALYTICS = `
  query GetShopifyAnalytics($storeId: ID!, $dateRange: DateRangeInput!) {
    getShopifyAnalytics(storeId: $storeId, dateRange: $dateRange) {
      totalOrders
      totalRevenue
      avgOrderValue
      conversionRate
      topProducts
      ordersByDay
      revenueByDay
      customerMetrics {
        newCustomers
        returningCustomers
        customerLifetimeValue
      }
      webhookMetrics {
        totalWebhooks
        successfulWebhooks
        failedWebhooks
        avgProcessingTime
      }
    }
  }
`; 