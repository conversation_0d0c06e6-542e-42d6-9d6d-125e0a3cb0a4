// WhatsApp Connection Queries
export const GET_WHATSAPP_CONNECTION = `
  query GetWhatsAppConnection($id: ID!) {
    getWhatsAppConnection(id: $id) {
      id
      userId
      organizationId
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      webhookUrl
      status
      isVerified
      connectedAt
      lastMessageAt
      totalConversations
      activeConversations
      businessProfile {
        displayName
        about
        email
        website
        category
        address
        profilePictureUrl
      }
      user {
        id
        phoneNumber
        email
        firstName
        lastName
      }
      organization {
        id
        name
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_WHATSAPP_CONNECTIONS = `
  query ListWhatsAppConnections($organizationId: ID, $status: ConnectionStatus) {
    listWhatsAppConnections(organizationId: $organizationId, status: $status) {
      id
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      status
      isVerified
      connectedAt
      totalConversations
      activeConversations
      businessProfile {
        displayName
        about
      }
      user {
        id
        firstName
        lastName
      }
    }
  }
`;

export const GET_USER_WHATSAPP_CONNECTIONS = `
  query GetUserWhatsAppConnections {
    getUserWhatsAppConnections {
      id
      phoneNumber
      displayName
      businessAccountId
      phoneNumberId
      status
      isVerified
      connectedAt
      totalConversations
      activeConversations
      businessProfile {
        displayName
        about
      }
    }
  }
`;

// WhatsApp Store Connection Queries
export const GET_WHATSAPP_STORE_CONNECTION = `
  query GetWhatsAppStoreConnection($id: ID!) {
    getWhatsAppStoreConnection(id: $id) {
      id
      wabaId
      phoneNumberId
      storeId
      shopDomain
      displayPhoneNumber
      verifiedName
      webhookConfiguration {
        orderNotifications
        paymentNotifications
        fulfillmentNotifications
        customerNotifications
      }
      notificationSettings {
        sendOrderConfirmations
        sendShippingUpdates
        sendDeliveryConfirmations
        templateLanguage
        timezone
      }
      status
      store {
        id
        shopDomain
        storeName
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_WHATSAPP_STORE_CONNECTIONS = `
  query ListWhatsAppStoreConnections($storeId: ID, $wabaId: String) {
    listWhatsAppStoreConnections(storeId: $storeId, wabaId: $wabaId) {
      id
      wabaId
      phoneNumberId
      storeId
      shopDomain
      displayPhoneNumber
      verifiedName
      status
      store {
        id
        shopDomain
        storeName
      }
      createdAt
      updatedAt
    }
  }
`;

// Conversation and Message queries removed - will be rebuilt with unified system

// TypeScript interfaces
export interface WhatsAppConnection {
  id: string;
  userId: string;
  organizationId?: string;
  phoneNumber: string;
  displayName: string;
  businessAccountId: string;
  phoneNumberId: string;
  webhookUrl: string;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'DISCONNECTED' | 'ERROR';
  isVerified: boolean;
  connectedAt: string;
  lastMessageAt?: string;
  totalConversations: number;
  activeConversations: number;
  businessProfile?: {
    displayName: string;
    about?: string;
    email?: string;
    website?: string;
    category?: string;
    address?: string;
    profilePictureUrl?: string;
  };
  user: {
    id: string;
    phoneNumber: string;
    email?: string;
    firstName?: string;
    lastName?: string;
  };
  organization?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface WhatsAppStoreConnection {
  id: string;
  wabaId: string;
  phoneNumberId: string;
  storeId: string;
  shopDomain: string;
  displayPhoneNumber?: string;
  verifiedName?: string;
  webhookConfiguration: {
    orderNotifications: boolean;
    paymentNotifications: boolean;
    fulfillmentNotifications: boolean;
    customerNotifications: boolean;
  };
  notificationSettings: {
    sendOrderConfirmations: boolean;
    sendShippingUpdates: boolean;
    sendDeliveryConfirmations: boolean;
    templateLanguage: string;
    timezone: string;
  };
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'DISCONNECTED' | 'ERROR';
  store: {
    id: string;
    shopDomain: string;
    storeName: string;
  };
  createdAt: string;
  updatedAt: string;
} 