// @ts-nocheck
// GraphQL client for AWS Amplify

import { generateClient, type GraphQLResult } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';

export interface GraphQLOperation<T = any, V = any> {
  query: string;
  variables?: V;
}

export interface GraphQLError {
  message: string;
  locations?: Array<{
    line: number;
    column: number;
  }>;
  path?: string[];
  extensions?: Record<string, any>;
}

export interface GraphQLResponse<T = any> {
  data?: T;
  errors?: GraphQLError[];
  extensions?: Record<string, any>;
}

// Initialize the Amplify GraphQL client
const client = generateClient();

// Enhanced error handler with context
export function handleGraphQLError(errors: GraphQLError[], context?: string): string[] {
  if (!errors || errors.length === 0) return [];
  
  const errorMessages = errors
    .map(error => error?.message || 'Unknown error')
    .filter(msg => typeof msg === 'string');
  
  if (context) {
    console.error(`GraphQL Error in ${context}:`, errorMessages);
  }
  
  return errorMessages;
}

// Check if user is authenticated before executing subscriptions
export async function checkAuthentication(): Promise<boolean> {
  try {
    console.log('🔐 Checking authentication status...');
    const user = await getCurrentUserContext();
    const isAuthenticated = !!user.userId;
    console.log('🔐 Authentication result:', { 
      isAuthenticated, 
      userId: user.userId,
      userIdType: typeof user.userId 
    });
    return isAuthenticated;
  } catch (error) {
    console.warn('🔐 Authentication check failed:', error);
    console.warn('🔐 Error details:', {
      message: error?.message,
      name: error?.name,
      stack: error?.stack
    });
    return false;
  }
}

// Generic GraphQL operation executor with better error handling
export async function executeGraphQLOperation<T = any>(
  operation: () => Promise<GraphQLResponse<T>>
): Promise<GraphQLResponse<T>> {
  try {
    const result = await operation();
    
    if (result.errors && result.errors.length > 0) {
      const errorMessages = handleGraphQLError(result.errors, 'GraphQL Operation');
      console.error('GraphQL operation completed with errors:', errorMessages);
      console.error('Full error details:', result.errors);
    }
    
    return result;
  } catch (error) {
    console.error('GraphQL operation failed:', error);
    throw new Error(`GraphQL operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Current user context for authenticated operations
export async function getCurrentUserContext() {
  try {
    const user = await getCurrentUser();
    return {
      userId: user.userId,
      username: user.username,
      isAuthenticated: true,
    };
  } catch (error) {
    console.warn('User not authenticated:', error);
    return {
      userId: null,
      username: null,
      isAuthenticated: false,
    };
  }
}

// Execute GraphQL Query
export async function executeQuery<T = any, V = any>(
  operation: GraphQLOperation<T, V>
): Promise<GraphQLResponse<T>> {
  return executeGraphQLOperation(async () => {
    const response = await client.graphql({
      query: operation.query,
      variables: operation.variables as any,
    });
    
    return {
      data: response.data as T,
      errors: response.errors as GraphQLError[],
    };
  });
}

// Execute GraphQL Mutation
export async function executeMutation<T = any, V = any>(
  operation: GraphQLOperation<T, V>
): Promise<GraphQLResponse<T>> {
  return executeGraphQLOperation(async () => {
    const response = await client.graphql({
      query: operation.query,
      variables: operation.variables as any,
    });
    
    return {
      data: response.data as T,
      errors: response.errors as GraphQLError[],
    };
  });
}

// Execute GraphQL Subscription with simplified types
export function executeSubscription<T = any, V = any>(
  operation: GraphQLOperation<T, V>,
  callbacks: {
    next: (data: T) => void;
    error: (error: string[]) => void;
    complete?: () => void;
  }
): { unsubscribe: () => void } {
  let subscription: any = null;
  let retryCount = 0;
  let isRetrying = false;
  let isUnsubscribed = false;
  const maxRetries = 3;
  const retryDelay = 2000; // 2 seconds

  const cleanup = () => {
    if (subscription && typeof subscription.unsubscribe === 'function') {
      subscription.unsubscribe();
    }
    subscription = null;
  };

  const attemptSubscription = async () => {
    if (isUnsubscribed || isRetrying) {
      return;
    }

    try {
      // Check authentication before attempting subscription
      const isAuthenticated = await checkAuthentication();
      if (!isAuthenticated) {
        callbacks.error(['User not authenticated. Please log in to receive real-time updates.']);
        return;
      }

      // Clean up any existing subscription
      cleanup();

      subscription = client.graphql({
        query: operation.query,
        variables: operation.variables as any,
      });

      // Handle the subscription based on its type
      if ('subscribe' in subscription) {
        const sub = (subscription as any).subscribe({
          next: (result: any) => {
            if (isUnsubscribed) return;
            
            retryCount = 0; // Reset retry count on successful connection
            isRetrying = false;
            
            if (result.errors && result.errors.length > 0) {
              const errorMessages = handleGraphQLError(result.errors, 'GraphQL Subscription');
              console.error('GraphQL Subscription Result Errors:', result.errors);
              callbacks.error(errorMessages);
            } else if (result.data) {
              callbacks.next(result.data);
            }
          },
          error: (error: any) => {
            if (isUnsubscribed || isRetrying) return;
            
            console.error('Subscription error details:', error);
            
            // Extract the actual error message
            let errorMessage = '';
            if (error?.errors && Array.isArray(error.errors) && error.errors.length > 0) {
              errorMessage = error.errors[0]?.message || 'Unknown subscription error';
              console.error('First error message:', errorMessage);
            } else if (error?.message) {
              errorMessage = error.message;
            } else {
              errorMessage = JSON.stringify(error);
            }
            
            // Check if it's a connection error and retry
            const isConnectionError = errorMessage.includes('Connection failed') || 
                                    errorMessage.includes('Network error') ||
                                    errorMessage.includes('WebSocket') ||
                                    errorMessage.includes('Unauthorized') ||
                                    errorMessage.includes('Authentication');
            
            if (isConnectionError && retryCount < maxRetries && !isRetrying) {
              isRetrying = true;
              retryCount++;
              console.log(`Retrying subscription connection (attempt ${retryCount}/${maxRetries}) in ${retryDelay}ms...`);
              
              // Clean up current subscription before retry
              cleanup();
              
              setTimeout(() => {
                if (!isUnsubscribed) {
                  isRetrying = false;
                  attemptSubscription();
                }
              }, retryDelay);
              return;
            }
            
            // Stop retrying and report the error
            isRetrying = false;
            const errorMessages = error?.errors 
              ? handleGraphQLError(error.errors, 'GraphQL Subscription')
              : ['Connection failed: ' + errorMessage];
            callbacks.error(errorMessages);
          },
          complete: () => {
            if (!isUnsubscribed) {
              callbacks.complete?.();
            }
          },
        });
        
        subscription = sub;
      } else {
        // Handle as Promise (not a subscription)
        (subscription as Promise<any>).then(result => {
          if (isUnsubscribed) return;
          
          if (result.errors && result.errors.length > 0) {
            const errorMessages = handleGraphQLError(result.errors, 'GraphQL Query');
            callbacks.error(errorMessages);
          } else if (result.data) {
            callbacks.next(result.data);
          }
        }).catch(error => {
          if (isUnsubscribed) return;
          
          const errorMessages = ['Subscription error: ' + (error?.message || 'Unknown error')];
          callbacks.error(errorMessages);
        });
      }
    } catch (error) {
      if (isUnsubscribed) return;
      
      console.error('Failed to create subscription:', error);
      isRetrying = false;
      callbacks.error(['Failed to establish subscription: ' + (error?.message || 'Unknown error')]);
    }
  };

  // Start the subscription attempt
  attemptSubscription();

  return {
    unsubscribe: () => {
      isUnsubscribed = true;
      isRetrying = false;
      cleanup();
    },
  };
}

// Optimistic update helper
export function createOptimisticUpdate<T>(
  existingData: T[],
  newItem: T,
  idField: keyof T = 'id' as keyof T
): T[] {
  return [newItem, ...existingData.filter(item => item[idField] !== newItem[idField])];
}

// Cache invalidation helper
export function invalidateCache(queries: string[]) {
  queries.forEach(query => {
    console.log(`Cache invalidated for query: ${query}`);
    // In a real implementation, this would clear specific cache entries
    // For now, just log the invalidation
  });
}

// Export the configured client for direct use if needed
export { client as graphqlClient }; 

// Debug function to test a simple subscription
export function testSubscription() {
  console.log('🧪 Testing simple subscription...');
  
  const testQuery = `
    subscription TestConnection {
      onConnectionStatusChanged(userId: "test") {
        id
        status
      }
    }
  `;
  
  return executeSubscription(
    { query: testQuery },
    {
      next: (data) => {
        console.log('🧪 Test subscription data received:', data);
      },
      error: (error) => {
        console.error('🧪 Test subscription error:', error);
      },
      complete: () => {
        console.log('🧪 Test subscription completed');
      }
    }
  );
}

// Export for debugging in console
if (typeof window !== 'undefined') {
  (window as any).testGraphQLSubscription = testSubscription;
} 