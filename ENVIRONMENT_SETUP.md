# Environment Setup for Notefy App

## Required Environment Variables

Create a `.env.local` file in the `notefy-app/` directory with the following content:

```bash
# AWS Configuration for Development
VITE_AWS_REGION=me-central-1
VITE_USER_POOL_ID=me-central-1_bhXeEyVRP
VITE_USER_POOL_CLIENT_ID=gqdc38md9atc2n7gip3ok11e9
VITE_IDENTITY_POOL_ID=me-central-1:5d722d03-02fd-4534-8136-333854a96f34
VITE_GRAPHQL_ENDPOINT=https://sync.dev.notefy.app/graphql
VITE_GRAPHQL_REALTIME_ENDPOINT=wss://sync.dev.notefy.app/graphql/realtime

# Environment
VITE_ENVIRONMENT=dev
VITE_APP_NAME=notefy

# Optional
VITE_API_KEY=
VITE_S3_BUCKET=notefy-whatsapp-media-dev
VITE_PINPOINT_APP_ID=
```

## Quick Setup Commands

```bash
# Navigate to the app directory
cd notefy-app

# Create environment file
cat > .env.local << 'EOF'
VITE_AWS_REGION=me-central-1
VITE_USER_POOL_ID=me-central-1_bhXeEyVRP
VITE_USER_POOL_CLIENT_ID=gqdc38md9atc2n7gip3ok11e9
VITE_IDENTITY_POOL_ID=me-central-1:5d722d03-02fd-4534-8136-333854a96f34
VITE_GRAPHQL_ENDPOINT=https://sync.dev.notefy.app/graphql
VITE_GRAPHQL_REALTIME_ENDPOINT=wss://sync.dev.notefy.app/graphql/realtime
VITE_ENVIRONMENT=dev
VITE_APP_NAME=notefy
VITE_API_KEY=
VITE_S3_BUCKET=notefy-whatsapp-media-dev
VITE_PINPOINT_APP_ID=
EOF

# Install dependencies and start development server
yarn install
yarn dev
```

## Testing Shopify OAuth Flow

1. **Start the development server:**
   ```bash
   yarn dev
   ```

2. **Navigate to Shopify Integration:**
   - Go to: `http://localhost:5173/portal/shopify-integration`
   - Click "Connect Shopify Store" button
   - Enter a test shop name (e.g., "test-store")
   - Click "Connect Store"

3. **Expected Flow:**
   - Frontend → GraphQL → AppSync → Lambda → Shopify OAuth URL
   - Should redirect to Shopify's OAuth authorization page
   - After authorization, redirects back to callback URL

## Current Status

✅ **Fixed Issues:**
- GraphQL import path corrected
- Schema updated to include `user_id` parameter  
- Cross-account IAM permissions added for secrets access
- ShopifyConnectionForm integrated into ShopifyIntegrationPage

✅ **Infrastructure Ready:**
- Lambda: `notefy-dev-shopify-generate-auth-link`
- GraphQL: Connected to Lambda via AppSync
- Secrets: Cross-account access configured

🔧 **Ready for Testing:**
- Frontend UI is connected and ready
- All AWS infrastructure is deployed
- OAuth flow should work end-to-end 